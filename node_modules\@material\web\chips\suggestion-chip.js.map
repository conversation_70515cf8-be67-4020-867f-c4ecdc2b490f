{"version": 3, "file": "suggestion-chip.js", "sourceRoot": "", "sources": ["suggestion-chip.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,MAAM,IAAI,cAAc,EAAC,MAAM,+BAA+B,CAAC;AACvE,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AACnE,OAAO,EAAC,cAAc,EAAC,MAAM,+BAA+B,CAAC;AAC7D,OAAO,EAAC,MAAM,EAAC,MAAM,iCAAiC,CAAC;AAQvD;;;;;GAKG;AAEI,IAAM,gBAAgB,GAAtB,MAAM,gBAAiB,SAAQ,cAAc;;AAClC,uBAAM,GAAwB,CAAC,YAAY,EAAE,cAAc,EAAE,MAAM,CAAC,AAA9D,CAA+D;AAD1E,gBAAgB;IAD5B,aAAa,CAAC,oBAAoB,CAAC;GACvB,gBAAgB,CAE5B", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {styles as elevatedStyles} from './internal/elevated-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\nimport {SuggestionChip} from './internal/suggestion-chip.js';\nimport {styles} from './internal/suggestion-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-suggestion-chip': MdSuggestionChip;\n  }\n}\n\n/**\n * TODO(b/*********): add docs\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-suggestion-chip')\nexport class MdSuggestionChip extends SuggestionChip {\n  static override styles: CSSResultOrNative[] = [sharedStyles, elevatedStyles, styles];\n}\n"]}