{"version": 3, "file": "filled-field.js", "sourceRoot": "", "sources": ["filled-field.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,IAAI,EAAC,MAAM,KAAK,CAAC;AAEzB,OAAO,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AAEjC;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,KAAK;IACjB,gBAAgB;QACjC,OAAO,IAAI,CAAA,kCAAkC,CAAC;IAChD,CAAC;IAEkB,gBAAgB;QACjC,OAAO,IAAI,CAAA,mCAAmC,CAAC;IACjD,CAAC;IAEkB,eAAe;QAChC,OAAO,IAAI,CAAA,sCAAsC,CAAC;IACpD,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html} from 'lit';\n\nimport {Field} from './field.js';\n\n/**\n * A filled field component.\n */\nexport class FilledField extends Field {\n  protected override renderBackground() {\n    return html` <div class=\"background\"></div> `;\n  }\n\n  protected override renderStateLayer() {\n    return html` <div class=\"state-layer\"></div> `;\n  }\n\n  protected override renderIndicator() {\n    return html`<div class=\"active-indicator\"></div>`;\n  }\n}\n"]}