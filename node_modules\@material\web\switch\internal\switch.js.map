{"version": 3, "file": "switch.js", "sourceRoot": "", "sources": ["switch.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,8BAA8B,CAAC;AACtC,OAAO,wBAAwB,CAAC;AAEhC,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAiB,MAAM,KAAK,CAAC;AACxE,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAC,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAY,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAEhE,OAAO,EAAC,kBAAkB,EAAC,MAAM,iCAAiC,CAAC;AACnE,OAAO,EACL,aAAa,EACb,kBAAkB,GACnB,MAAM,yCAAyC,CAAC;AACjD,OAAO,EACL,uBAAuB,EACvB,iBAAiB,GAClB,MAAM,gDAAgD,CAAC;AACxD,OAAO,EAAC,eAAe,EAAC,MAAM,2CAA2C,CAAC;AAC1E,OAAO,EACL,eAAe,EACf,iBAAiB,EACjB,yBAAyB,GAC1B,MAAM,+CAA+C,CAAC;AACvD,OAAO,EAAC,qBAAqB,EAAC,MAAM,2CAA2C,CAAC;AAChF,OAAO,EACL,YAAY,EACZ,YAAY,EACZ,mBAAmB,GACpB,MAAM,yCAAyC,CAAC;AACjD,OAAO,EAAC,iBAAiB,EAAC,MAAM,uDAAuD,CAAC;AAExF,wCAAwC;AACxC,MAAM,eAAe,GAAG,kBAAkB,CACxC,yBAAyB,CACvB,mBAAmB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,CACvD,CACF,CAAC;AAEF;;;;;GAKG;AACH,MAAM,OAAO,MAAO,SAAQ,eAAe;IAyCzC;QACE,KAAK,EAAE,CAAC;QAnCV;;;WAGG;QACwB,aAAQ,GAAG,KAAK,CAAC;QAE5C;;WAEG;QACwB,UAAK,GAAG,KAAK,CAAC;QAEzC;;;WAGG;QAEH,yBAAoB,GAAG,KAAK,CAAC;QAE7B;;;;;WAKG;QACwB,aAAQ,GAAG,KAAK,CAAC;QAE5C;;;WAGG;QACS,UAAK,GAAG,IAAI,CAAC;QAMvB,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO;QACT,CAAC;QAED,2EAA2E;QAC3E,sCAAsC;QACtC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,KAAiB,EAAE,EAAE;YACnD,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC7C,OAAO;YACT,CAAC;YACD,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,0EAA0E;QAC1E,wDAAwD;QACxD,kBAAkB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACpC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,KAAoB,EAAE,EAAE;YACxD,aAAa,CAAC,KAAK,EAAE,GAAG,EAAE;gBACxB,MAAM,WAAW,GAAG,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,CAAC;gBACpE,IAAI,WAAW,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBAChD,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YACrB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEkB,MAAM;QACvB,OAAO,IAAI,CAAA;2BACY,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;;;;;;uBAMpC,IAAkB,CAAC,SAAS,IAAI,OAAO;qBAC1C,IAAI,CAAC,QAAQ;sBACZ,IAAI,CAAC,QAAQ;sBACb,IAAI,CAAC,QAAQ;mBAChB,IAAI,CAAC,WAAW;oBACf,IAAI,CAAC,YAAY;;;+BAGN,IAAI,CAAC,YAAY,EAAE;;KAE7C,CAAC;IACJ,CAAC;IAEO,gBAAgB;QACtB,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,QAAQ;YACzB,YAAY,EAAE,CAAC,IAAI,CAAC,QAAQ;YAC5B,UAAU,EAAE,IAAI,CAAC,QAAQ;SAC1B,CAAC;IACJ,CAAC;IAEO,YAAY;QAClB,MAAM,OAAO,GAAG;YACd,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK;SACpE,CAAC;QACF,OAAO,IAAI,CAAA;QACP,IAAI,CAAC,iBAAiB,EAAE;;6CAEa,IAAI,CAAC,QAAQ;8BAC5B,QAAQ,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAA,EAAE;;;KAG3D,CAAC;IACJ,CAAC;IAEO,WAAW;QACjB,OAAO,IAAI,CAAA;;UAEL,IAAI,CAAC,YAAY,EAAE;UACnB,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAA,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE;;KAE9D,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,OAAO,IAAI,CAAA;;;;;;;KAOV,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,OAAO,IAAI,CAAA;;;;;;;KAOV,CAAC;IACJ,CAAC;IAEO,iBAAiB;QACvB,OAAO,IAAI,CAAA,6BAA6B,CAAC;IAC3C,CAAC;IAEO,eAAe;QACrB,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,oBAAoB,CAAC;IACjD,CAAC;IAEO,WAAW,CAAC,KAAY;QAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,MAA0B,CAAC;QAChD,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;QAC/B,uEAAuE;IACzE,CAAC;IAEO,YAAY,CAAC,KAAY;QAC/B,0DAA0D;QAC1D,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC/B,CAAC;IAMQ,CAAC,YAAY,CAAC;QACrB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IAC3C,CAAC;IAEQ,CAAC,YAAY,CAAC;QACrB,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAEQ,iBAAiB;QACxB,2EAA2E;QAC3E,mDAAmD;QACnD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;IAChD,CAAC;IAEQ,wBAAwB,CAAC,KAAa;QAC7C,IAAI,CAAC,QAAQ,GAAG,KAAK,KAAK,MAAM,CAAC;IACnC,CAAC;IAEQ,CAAC,eAAe,CAAC;QACxB,OAAO,IAAI,iBAAiB,CAAC,GAAG,EAAE,CAAC,CAAC;YAClC,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC,CAAC;IACN,CAAC;IAEQ,CAAC,iBAAiB,CAAC;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;;AA1MD,kBAAkB;AACF,wBAAiB,GAAmB;IAClD,IAAI,EAAE,MAAM;IACZ,cAAc,EAAE,IAAI;CACrB,AAHgC,CAG/B;AAMyB;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;wCAAkB;AAKjB;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;qCAAe;AAOzC;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,yBAAyB,EAAC,CAAC;oDACnC;AAQF;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;wCAAkB;AAMhC;IAAX,QAAQ,EAAE;qCAAc;AAEQ;IAAhC,KAAK,CAAC,OAAO,CAAC;qCAAkD", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, isServer, LitElement, nothing, TemplateResult} from 'lit';\nimport {property, query} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\n\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\nimport {\n  afterDispatch,\n  setupDispatchHooks,\n} from '../../internal/events/dispatch-hooks.js';\nimport {\n  dispatchActivationClick,\n  isActivationClick,\n} from '../../internal/events/form-label-activation.js';\nimport {redispatchEvent} from '../../internal/events/redispatch-event.js';\nimport {\n  createValidator,\n  getValidityAnchor,\n  mixinConstraintValidation,\n} from '../../labs/behaviors/constraint-validation.js';\nimport {mixinElementInternals} from '../../labs/behaviors/element-internals.js';\nimport {\n  getFormState,\n  getFormValue,\n  mixinFormAssociated,\n} from '../../labs/behaviors/form-associated.js';\nimport {CheckboxValidator} from '../../labs/behaviors/validators/checkbox-validator.js';\n\n// Separate variable needed for closure.\nconst switchBaseClass = mixinDelegatesAria(\n  mixinConstraintValidation(\n    mixinFormAssociated(mixinElementInternals(LitElement)),\n  ),\n);\n\n/**\n * @fires input {InputEvent} Fired whenever `selected` changes due to user\n * interaction (bubbles and composed).\n * @fires change {Event} Fired whenever `selected` changes due to user\n * interaction (bubbles).\n */\nexport class Switch extends switchBaseClass {\n  /** @nocollapse */\n  static override shadowRootOptions: ShadowRootInit = {\n    mode: 'open',\n    delegatesFocus: true,\n  };\n\n  /**\n   * Puts the switch in the selected state and sets the form submission value to\n   * the `value` property.\n   */\n  @property({type: Boolean}) selected = false;\n\n  /**\n   * Shows both the selected and deselected icons.\n   */\n  @property({type: Boolean}) icons = false;\n\n  /**\n   * Shows only the selected icon, and not the deselected icon. If `true`,\n   * overrides the behavior of the `icons` property.\n   */\n  @property({type: Boolean, attribute: 'show-only-selected-icon'})\n  showOnlySelectedIcon = false;\n\n  /**\n   * When true, require the switch to be selected when participating in\n   * form submission.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/checkbox#validation\n   */\n  @property({type: Boolean}) required = false;\n\n  /**\n   * The value associated with this switch on form submission. `null` is\n   * submitted when `selected` is `false`.\n   */\n  @property() value = 'on';\n\n  @query('input') private readonly input!: HTMLInputElement | null;\n\n  constructor() {\n    super();\n    if (isServer) {\n      return;\n    }\n\n    // This click listener does not currently need dispatch hooks since it does\n    // not check `event.defaultPrevented`.\n    this.addEventListener('click', (event: MouseEvent) => {\n      if (!isActivationClick(event) || !this.input) {\n        return;\n      }\n      this.focus();\n      dispatchActivationClick(this.input);\n    });\n\n    // Add the aria keyboard interaction pattern for switch and the Enter key.\n    // See https://www.w3.org/WAI/ARIA/apg/patterns/switch/.\n    setupDispatchHooks(this, 'keydown');\n    this.addEventListener('keydown', (event: KeyboardEvent) => {\n      afterDispatch(event, () => {\n        const ignoreEvent = event.defaultPrevented || event.key !== 'Enter';\n        if (ignoreEvent || this.disabled || !this.input) {\n          return;\n        }\n\n        this.input.click();\n      });\n    });\n  }\n\n  protected override render(): TemplateResult {\n    return html`\n      <div class=\"switch ${classMap(this.getRenderClasses())}\">\n        <input\n          id=\"switch\"\n          class=\"touch\"\n          type=\"checkbox\"\n          role=\"switch\"\n          aria-label=${(this as ARIAMixin).ariaLabel || nothing}\n          ?checked=${this.selected}\n          ?disabled=${this.disabled}\n          ?required=${this.required}\n          @input=${this.handleInput}\n          @change=${this.handleChange} />\n\n        <md-focus-ring part=\"focus-ring\" for=\"switch\"></md-focus-ring>\n        <span class=\"track\"> ${this.renderHandle()} </span>\n      </div>\n    `;\n  }\n\n  private getRenderClasses(): ClassInfo {\n    return {\n      'selected': this.selected,\n      'unselected': !this.selected,\n      'disabled': this.disabled,\n    };\n  }\n\n  private renderHandle() {\n    const classes = {\n      'with-icon': this.showOnlySelectedIcon ? this.selected : this.icons,\n    };\n    return html`\n      ${this.renderTouchTarget()}\n      <span class=\"handle-container\">\n        <md-ripple for=\"switch\" ?disabled=\"${this.disabled}\"></md-ripple>\n        <span class=\"handle ${classMap(classes)}\">\n          ${this.shouldShowIcons() ? this.renderIcons() : html``}\n        </span>\n      </span>\n    `;\n  }\n\n  private renderIcons() {\n    return html`\n      <div class=\"icons\">\n        ${this.renderOnIcon()}\n        ${this.showOnlySelectedIcon ? html`` : this.renderOffIcon()}\n      </div>\n    `;\n  }\n\n  /**\n   * https://fonts.google.com/icons?selected=Material%20Symbols%20Outlined%3Acheck%3AFILL%400%3Bwght%40500%3BGRAD%400%3Bopsz%4024\n   */\n  private renderOnIcon() {\n    return html`\n      <slot class=\"icon icon--on\" name=\"on-icon\">\n        <svg viewBox=\"0 0 24 24\">\n          <path\n            d=\"M9.55 18.2 3.65 12.3 5.275 10.675 9.55 14.95 18.725 5.775 20.35 7.4Z\" />\n        </svg>\n      </slot>\n    `;\n  }\n\n  /**\n   * https://fonts.google.com/icons?selected=Material%20Symbols%20Outlined%3Aclose%3AFILL%400%3Bwght%40500%3BGRAD%400%3Bopsz%4024\n   */\n  private renderOffIcon() {\n    return html`\n      <slot class=\"icon icon--off\" name=\"off-icon\">\n        <svg viewBox=\"0 0 24 24\">\n          <path\n            d=\"M6.4 19.2 4.8 17.6 10.4 12 4.8 6.4 6.4 4.8 12 10.4 17.6 4.8 19.2 6.4 13.6 12 19.2 17.6 17.6 19.2 12 13.6Z\" />\n        </svg>\n      </slot>\n    `;\n  }\n\n  private renderTouchTarget() {\n    return html`<span class=\"touch\"></span>`;\n  }\n\n  private shouldShowIcons(): boolean {\n    return this.icons || this.showOnlySelectedIcon;\n  }\n\n  private handleInput(event: Event) {\n    const target = event.target as HTMLInputElement;\n    this.selected = target.checked;\n    // <input> 'input' event bubbles and is composed, don't re-dispatch it.\n  }\n\n  private handleChange(event: Event) {\n    // <input> 'change' event is not composed, re-dispatch it.\n    redispatchEvent(this, event);\n  }\n\n  // Writable mixin properties for lit-html binding, needed for lit-analyzer\n  declare disabled: boolean;\n  declare name: string;\n\n  override [getFormValue]() {\n    return this.selected ? this.value : null;\n  }\n\n  override [getFormState]() {\n    return String(this.selected);\n  }\n\n  override formResetCallback() {\n    // The selected property does not reflect, so the original attribute set by\n    // the user is used to determine the default value.\n    this.selected = this.hasAttribute('selected');\n  }\n\n  override formStateRestoreCallback(state: string) {\n    this.selected = state === 'true';\n  }\n\n  override [createValidator]() {\n    return new CheckboxValidator(() => ({\n      checked: this.selected,\n      required: this.required,\n    }));\n  }\n\n  override [getValidityAnchor]() {\n    return this.input;\n  }\n}\n"]}