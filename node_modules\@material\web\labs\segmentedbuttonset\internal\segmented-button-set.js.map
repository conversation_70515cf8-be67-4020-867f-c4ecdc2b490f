{"version": 3, "file": "segmented-button-set.js", "sourceRoot": "", "sources": ["segmented-button-set.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,EAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAC,MAAM,KAAK,CAAC;AAC9C,OAAO,EAAC,QAAQ,EAAE,qBAAqB,EAAC,MAAM,mBAAmB,CAAC;AAGlE,OAAO,EAAC,kBAAkB,EAAC,MAAM,oCAAoC,CAAC;AAGtE,wCAAwC;AACxC,MAAM,2BAA2B,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAEnE;;;;;;;;;GASG;AACH,MAAM,OAAO,kBAAmB,SAAQ,2BAA2B;IAAnE;;QAC6B,gBAAW,GAAG,KAAK,CAAC;IAwFjD,CAAC;IApFC,iBAAiB,CAAC,KAAa;QAC7B,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED,iBAAiB,CAAC,KAAa,EAAE,QAAiB;QAChD,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;YAAE,OAAO;QACzC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC1C,CAAC;IAED,iBAAiB,CAAC,KAAa;QAC7B,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED,iBAAiB,CAAC,KAAa,EAAE,QAAiB;QAChD,8BAA8B;QAC9B,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;YAAE,OAAO;QACzC,2BAA2B;QAC3B,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;YAAE,OAAO;QAE1C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACxC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC/B,OAAO;QACT,CAAC;QAED,wDAAwD;QACxD,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC/B,gDAAgD;QAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,IAAI,CAAC,KAAK,KAAK;gBAAE,SAAS;YAC1B,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,gCAAgC,CAAC,KAAkB;QACzD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAyB,CAAC,CAAC;QACpE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAEO,eAAe,CAAC,KAAa;QACnC,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;YAAE,OAAO;QACzC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC/D,CAAC;IAEO,gBAAgB,CAAC,KAAa;QACpC,OAAO,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACnD,CAAC;IAEO,kBAAkB,CAAC,KAAa;QACtC,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,gCAAgC,EAAE;YAChD,MAAM,EAAE;gBACN,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;gBAC3B,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ;gBACtC,KAAK;aACN;YACD,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;SACf,CAAC,CACH,CAAC;IACJ,CAAC;IAEkB,MAAM;QACvB,iCAAiC;QACjC,MAAM,EAAC,SAAS,EAAC,GAAG,IAAuB,CAAC;QAC5C,OAAO,IAAI,CAAA;;;yCAG0B,IAAI,CAAC,gCAAgC;qBACzD,SAAS,IAAI,OAAO;;;;KAIpC,CAAC;IACJ,CAAC;IAES,gBAAgB;QACxB,OAAO,EAAE,CAAC;IACZ,CAAC;CACF;AAxF4B;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;uDAAqB;AAEP;IAAvC,qBAAqB,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;mDAA6B", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, LitElement, nothing} from 'lit';\nimport {property, queryAssignedElements} from 'lit/decorators.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\nimport {SegmentedButton} from '../../segmentedbutton/internal/segmented-button.js';\n\n// Separate variable needed for closure.\nconst segmentedButtonSetBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * SegmentedButtonSet is the parent component for two or more\n * `SegmentedButton` components. **Only** `SegmentedButton` components may be\n * used as children.\n *\n * @fires segmented-button-set-selection {CustomEvent<{button: SegmentedButton, selected: boolean, index: number}>}\n * Dispatched when a button is selected programattically with the\n * `setButtonSelected` or the `toggleSelection` methods as well as on user\n * interaction. --bubbles --composed\n */\nexport class SegmentedButtonSet extends segmentedButtonSetBaseClass {\n  @property({type: Boolean}) multiselect = false;\n\n  @queryAssignedElements({flatten: true}) buttons!: SegmentedButton[];\n\n  getButtonDisabled(index: number): boolean {\n    if (this.indexOutOfBounds(index)) return false;\n    return this.buttons[index].disabled;\n  }\n\n  setButtonDisabled(index: number, disabled: boolean) {\n    if (this.indexOutOfBounds(index)) return;\n    this.buttons[index].disabled = disabled;\n  }\n\n  getButtonSelected(index: number): boolean {\n    if (this.indexOutOfBounds(index)) return false;\n    return this.buttons[index].selected;\n  }\n\n  setButtonSelected(index: number, selected: boolean) {\n    // Ignore out-of-index values.\n    if (this.indexOutOfBounds(index)) return;\n    // Ignore disabled buttons.\n    if (this.getButtonDisabled(index)) return;\n\n    if (this.multiselect) {\n      this.buttons[index].selected = selected;\n      this.emitSelectionEvent(index);\n      return;\n    }\n\n    // Single-select segmented buttons are not unselectable.\n    if (!selected) return;\n\n    this.buttons[index].selected = true;\n    this.emitSelectionEvent(index);\n    // Deselect all other buttons for single-select.\n    for (let i = 0; i < this.buttons.length; i++) {\n      if (i === index) continue;\n      this.buttons[i].selected = false;\n    }\n  }\n\n  private handleSegmentedButtonInteraction(event: CustomEvent) {\n    const index = this.buttons.indexOf(event.target as SegmentedButton);\n    this.toggleSelection(index);\n  }\n\n  private toggleSelection(index: number) {\n    if (this.indexOutOfBounds(index)) return;\n    this.setButtonSelected(index, !this.buttons[index].selected);\n  }\n\n  private indexOutOfBounds(index: number): boolean {\n    return index < 0 || index >= this.buttons.length;\n  }\n\n  private emitSelectionEvent(index: number) {\n    this.dispatchEvent(\n      new CustomEvent('segmented-button-set-selection', {\n        detail: {\n          button: this.buttons[index],\n          selected: this.buttons[index].selected,\n          index,\n        },\n        bubbles: true,\n        composed: true,\n      }),\n    );\n  }\n\n  protected override render() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`\n      <span\n        role=\"group\"\n        @segmented-button-interaction=\"${this.handleSegmentedButtonInteraction}\"\n        aria-label=${ariaLabel || nothing}\n        class=\"md3-segmented-button-set\">\n        <slot></slot>\n      </span>\n    `;\n  }\n\n  protected getRenderClasses() {\n    return {};\n  }\n}\n"]}