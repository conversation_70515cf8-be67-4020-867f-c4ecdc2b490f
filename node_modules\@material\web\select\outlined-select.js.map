{"version": 3, "file": "outlined-select.js", "sourceRoot": "", "sources": ["outlined-select.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,cAAc,EAAC,MAAM,+BAA+B,CAAC;AAC7D,OAAO,EAAC,MAAM,EAAC,MAAM,sCAAsC,CAAC;AAC5D,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAQnE;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AAEI,IAAM,gBAAgB,GAAtB,MAAM,gBAAiB,SAAQ,cAAc;;AAClC,uBAAM,GAAwB,CAAC,YAAY,EAAE,MAAM,CAAC,AAA9C,CAA+C;AAD1D,gBAAgB;IAD5B,aAAa,CAAC,oBAAoB,CAAC;GACvB,gBAAgB,CAE5B", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {OutlinedSelect} from './internal/outlined-select.js';\nimport {styles} from './internal/outlined-select-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-outlined-select': MdOutlinedSelect;\n  }\n}\n\n/**\n * @summary\n * Select menus display a list of choices on temporary surfaces and display the\n * currently selected menu item above the menu.\n *\n * @description\n * The select component allows users to choose a value from a fixed list of\n * available options. Composed of an interactive anchor button and a menu, it is\n * analogous to the native HTML `<select>` element. This is the \"outlined\"\n * variant.\n *\n * @example\n * ```html\n * <md-outlined-select label=\"fruits\">\n *   <!-- An empty selected option will give select an \"un-filled\" state -->\n *   <md-select-option selected></md-select-option>\n *   <md-select-option value=\"apple\" headline=\"Apple\"></md-select-option>\n *   <md-select-option value=\"banana\" headline=\"Banana\"></md-select-option>\n *   <md-select-option value=\"kiwi\" headline=\"Kiwi\"></md-select-option>\n *   <md-select-option value=\"orange\" headline=\"Orange\"></md-select-option>\n *   <md-select-option value=\"tomato\" headline=\"Tomato\"></md-select-option>\n * </md-outlined-select>\n * ```\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-outlined-select')\nexport class MdOutlinedSelect extends OutlinedSelect {\n  static override styles: CSSResultOrNative[] = [sharedStyles, styles];\n}\n"]}