{"version": 3, "file": "harness.js", "sourceRoot": "", "sources": ["harness.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAqB,OAAO,EAAC,MAAM,uBAAuB,CAAC;AAKlE;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,OAAY;IACjC,KAAK,CAAC,qBAAqB;QAClC,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;QAClC,OAAO,IAAI,CAAC,OAAsB,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,0BAA0B;QACtC,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;QAClC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,YAAY,CAAE,CAAC;QACvE,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;QAC7C,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,SAAS,CAAC,MAAM,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACxC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,YAAY,CAAE,CAAC;QACvE,MAAM,OAAO,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC;QACvD,OAAO,OAAO,KAAK,GAAG,CAAC;IACzB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,OAAa;IAC5C,mEAAmE;IACnE,gBAAgB;IACP,KAAK,CAAC,qBAAqB;QAClC,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;QAClC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,OAAsB,CAAC;QACrC,CAAC;QAED,MAAM,mBAAmB,GACrB,IAAI,CAAC,OAAO,CAAC,SAAqC;aACjD,OAAsB,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACtE,OAAO,MAAM,mBAAmB,CAAC,qBAAqB,EAAE,CAAC;IAC3D,CAAC;IAED,IAAI,cAAc;QAChB,OAAQ,IAAI,CAAC,OAAO,CAAC,IAAuC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACxE,OAAO,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,CAAe,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {ElementWithHarness, Harness} from '../testing/harness.js';\n\nimport {Tab} from './internal/tab.js';\nimport {Tabs} from './internal/tabs.js';\n\n/**\n * Test harness for Tab.\n */\nexport class TabHarness extends Harness<Tab> {\n  override async getInteractiveElement() {\n    await this.element.updateComplete;\n    return this.element as HTMLElement;\n  }\n\n  private async completeIndicatorAnimation() {\n    await this.element.updateComplete;\n    const indicator = this.element.renderRoot.querySelector('.indicator')!;\n    const animations = indicator.getAnimations();\n    for (const animation of animations) {\n      animation.finish();\n    }\n  }\n\n  async isIndicatorShowing() {\n    await this.completeIndicatorAnimation();\n    const indicator = this.element.renderRoot.querySelector('.indicator')!;\n    const opacity = getComputedStyle(indicator)['opacity'];\n    return opacity === '1';\n  }\n}\n\n/**\n * Test harness for Tabs.\n */\nexport class TabsHarness extends Harness<Tabs> {\n  // Note, Tabs interactive element is the interactive element of the\n  // selected tab.\n  override async getInteractiveElement() {\n    await this.element.updateComplete;\n    if (!this.element.activeTab) {\n      return this.element as HTMLElement;\n    }\n\n    const selectedItemHarness =\n      ((this.element.activeTab as ElementWithHarness<Tab>)\n        .harness as TabHarness) ?? new TabHarness(this.element.activeTab);\n    return await selectedItemHarness.getInteractiveElement();\n  }\n\n  get harnessedItems() {\n    return (this.element.tabs as Array<ElementWithHarness<Tab>>).map((item) => {\n      return (item.harness ?? new TabHarness(item)) as TabHarness;\n    });\n  }\n}\n"]}