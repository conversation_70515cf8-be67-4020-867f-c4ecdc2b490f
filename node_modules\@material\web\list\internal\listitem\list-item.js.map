{"version": 3, "file": "list-item.js", "sourceRoot": "", "sources": ["list-item.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,iCAAiC,CAAC;AACzC,OAAO,4BAA4B,CAAC;AACpC,OAAO,2BAA2B,CAAC;AAEnC,OAAO,EAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAiC,MAAM,KAAK,CAAC;AAC9E,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAC,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAY,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAChE,OAAO,EAAC,OAAO,EAAE,IAAI,IAAI,UAAU,EAAc,MAAM,oBAAoB,CAAC;AAG5E,OAAO,EAAC,kBAAkB,EAAC,MAAM,oCAAoC,CAAC;AACtE,OAAO,EACL,4BAA4B,GAE7B,MAAM,+BAA+B,CAAC;AAOvC,wCAAwC;AACxC,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAEzD;;;GAGG;AACH,MAAM,OAAO,UAAW,SAAQ,iBAAiB;IAAjD;;QAOE;;WAEG;QACuC,aAAQ,GAAG,KAAK,CAAC;QAE3D;;;WAGG;QACwB,SAAI,GAAiB,MAAM,CAAC;QAEvD;;WAEG;QAEH,eAAU,GAAG,IAAI,CAAC;QAElB;;WAEG;QACS,SAAI,GAAG,EAAE,CAAC;QAEtB;;;WAGG;QACS,WAAM,GAAiD,EAAE,CAAC;IAsJxE,CAAC;IAlJC,IAAY,UAAU;QACpB,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC;IAC/C,CAAC;IAEkB,UAAU,CAAC,OAAmC;QAC/D,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;QACrB,CAAC;QAED,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC;IAEkB,MAAM;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAA;;;YAGvB,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,eAAe,EAAE;;;;UAI/C,IAAI,CAAC,UAAU,EAAE;;KAEtB,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACO,cAAc,CAAC,OAAgB;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC;QACtC,IAAI,GAAgB,CAAC;QACrB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,MAAM;gBACT,GAAG,GAAG,OAAO,CAAA,GAAG,CAAC;gBACjB,MAAM;YACR,KAAK,QAAQ;gBACX,GAAG,GAAG,OAAO,CAAA,QAAQ,CAAC;gBACtB,MAAM;YACR,QAAQ;YACR,KAAK,MAAM;gBACT,GAAG,GAAG,OAAO,CAAA,IAAI,CAAC;gBAClB,MAAM;QACV,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC;QAC3C,2EAA2E;QAC3E,0DAA0D;QAC1D,MAAM,MAAM,GAAG,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;QACjE,OAAO,UAAU,CAAA;SACZ,GAAG;;oBAEQ,IAAI,CAAC,UAAU,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1C,IAAI,CAAC,UAAU;;wBAEV,IAAwB,CAAC,YAAY,IAAI,OAAO;uBACjD,IAAwB,CAAC,WAAW,IAAI,OAAO;wBAC9C,IAAwB,CAAC,YAAY,IAAI,OAAO;wBAChD,IAAwB,CAAC,YAAY,IAAI,OAAO;2BAC9C,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;eAC7C,IAAI,CAAC,IAAI,IAAI,OAAO;iBAClB,MAAM;iBACN,IAAI,CAAC,OAAO;SACpB,OAAO,KAAK,GAAG;KACnB,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,YAAY;QACpB,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAA;;;kBAGG,IAAI,CAAC,UAAU,eAAe,CAAC;IAC/C,CAAC;IAED;;OAEG;IACO,eAAe;QACvB,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAA;4BACa,IAAI,CAAC,4BAA4B;;;8BAG/B,CAAC;IAC7B,CAAC;IAES,4BAA4B,CAAC,CAAQ,IAAG,CAAC;IAEnD;;OAEG;IACO,gBAAgB;QACxB,OAAO,EAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACO,UAAU;QAClB,OAAO,IAAI,CAAA;;;;;;;;KAQV,CAAC;IACJ,CAAC;IAES,OAAO;QACf,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QACD,uEAAuE;QACvE,IAAI,CAAC,aAAa,CAAC,4BAA4B,EAAE,CAAC,CAAC;IACrD,CAAC;IAEQ,KAAK;QACZ,wEAAwE;QACxE,qDAAqD;QACrD,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC;IAC7B,CAAC;IAEQ,KAAK;QACZ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,wEAAwE;YACxE,yBAAyB;YACzB,KAAK,CAAC,KAAK,EAAE,CAAC;YACd,OAAO;QACT,CAAC;QAED,2EAA2E;QAC3E,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;;AArLD,kBAAkB;AACF,4BAAiB,GAAG;IAClC,GAAG,UAAU,CAAC,iBAAiB;IAC/B,cAAc,EAAE,IAAI;CACrB,AAHgC,CAG/B;AAKwC;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;4CAAkB;AAMhC;IAA1B,QAAQ,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;wCAA6B;AAMvD;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;8CAClD;AAKN;IAAX,QAAQ,EAAE;wCAAW;AAMV;IAAX,QAAQ,EAAE;0CAA2D;AAE9B;IAAvC,KAAK,CAAC,YAAY,CAAC;gDAAsD", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../../focus/md-focus-ring.js';\nimport '../../../labs/item/item.js';\nimport '../../../ripple/ripple.js';\n\nimport {html, LitElement, nothing, PropertyValues, TemplateResult} from 'lit';\nimport {property, query} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\nimport {literal, html as staticHtml, StaticValue} from 'lit/static-html.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\nimport {\n  createRequestActivationEvent,\n  ListItem,\n} from '../list-navigation-helpers.js';\n\n/**\n * Supported behaviors for a list item.\n */\nexport type ListItemType = 'text' | 'button' | 'link';\n\n// Separate variable needed for closure.\nconst listItemBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * @fires request-activation {Event} Requests the list to set `tabindex=0` on\n * the item and focus it. --bubbles --composed\n */\nexport class ListItemEl extends listItemBaseClass implements ListItem {\n  /** @nocollapse */\n  static override shadowRootOptions = {\n    ...LitElement.shadowRootOptions,\n    delegatesFocus: true,\n  };\n\n  /**\n   * Disables the item and makes it non-selectable and non-interactive.\n   */\n  @property({type: Boolean, reflect: true}) disabled = false;\n\n  /**\n   * Sets the behavior of the list item, defaults to \"text\". Change to \"link\" or\n   * \"button\" for interactive items.\n   */\n  @property({reflect: true}) type: ListItemType = 'text';\n\n  /**\n   * READONLY. Sets the `md-list-item` attribute on the element.\n   */\n  @property({type: Boolean, attribute: 'md-list-item', reflect: true})\n  isListItem = true;\n\n  /**\n   * Sets the underlying `HTMLAnchorElement`'s `href` resource attribute.\n   */\n  @property() href = '';\n\n  /**\n   * Sets the underlying `HTMLAnchorElement`'s `target` attribute when `href` is\n   * set.\n   */\n  @property() target: '_blank' | '_parent' | '_self' | '_top' | '' = '';\n\n  @query('.list-item') protected readonly listItemRoot!: HTMLElement | null;\n\n  private get isDisabled() {\n    return this.disabled && this.type !== 'link';\n  }\n\n  protected override willUpdate(changed: PropertyValues<ListItemEl>) {\n    if (this.href) {\n      this.type = 'link';\n    }\n\n    super.willUpdate(changed);\n  }\n\n  protected override render() {\n    return this.renderListItem(html`\n      <md-item>\n        <div slot=\"container\">\n          ${this.renderRipple()} ${this.renderFocusRing()}\n        </div>\n        <slot name=\"start\" slot=\"start\"></slot>\n        <slot name=\"end\" slot=\"end\"></slot>\n        ${this.renderBody()}\n      </md-item>\n    `);\n  }\n\n  /**\n   * Renders the root list item.\n   *\n   * @param content the child content of the list item.\n   */\n  protected renderListItem(content: unknown) {\n    const isAnchor = this.type === 'link';\n    let tag: StaticValue;\n    switch (this.type) {\n      case 'link':\n        tag = literal`a`;\n        break;\n      case 'button':\n        tag = literal`button`;\n        break;\n      default:\n      case 'text':\n        tag = literal`li`;\n        break;\n    }\n\n    const isInteractive = this.type !== 'text';\n    // TODO(b/265339866): announce \"button\"/\"link\" inside of a list item. Until\n    // then all are \"listitem\" roles for correct announcement.\n    const target = isAnchor && !!this.target ? this.target : nothing;\n    return staticHtml`\n      <${tag}\n        id=\"item\"\n        tabindex=\"${this.isDisabled || !isInteractive ? -1 : 0}\"\n        ?disabled=${this.isDisabled}\n        role=\"listitem\"\n        aria-selected=${(this as ARIAMixinStrict).ariaSelected || nothing}\n        aria-checked=${(this as ARIAMixinStrict).ariaChecked || nothing}\n        aria-expanded=${(this as ARIAMixinStrict).ariaExpanded || nothing}\n        aria-haspopup=${(this as ARIAMixinStrict).ariaHasPopup || nothing}\n        class=\"list-item ${classMap(this.getRenderClasses())}\"\n        href=${this.href || nothing}\n        target=${target}\n        @focus=${this.onFocus}\n      >${content}</${tag}>\n    `;\n  }\n\n  /**\n   * Handles rendering of the ripple element.\n   */\n  protected renderRipple(): TemplateResult | typeof nothing {\n    if (this.type === 'text') {\n      return nothing;\n    }\n\n    return html` <md-ripple\n      part=\"ripple\"\n      for=\"item\"\n      ?disabled=${this.isDisabled}></md-ripple>`;\n  }\n\n  /**\n   * Handles rendering of the focus ring.\n   */\n  protected renderFocusRing(): TemplateResult | typeof nothing {\n    if (this.type === 'text') {\n      return nothing;\n    }\n\n    return html` <md-focus-ring\n      @visibility-changed=${this.onFocusRingVisibilityChanged}\n      part=\"focus-ring\"\n      for=\"item\"\n      inward></md-focus-ring>`;\n  }\n\n  protected onFocusRingVisibilityChanged(e: Event) {}\n\n  /**\n   * Classes applied to the list item root.\n   */\n  protected getRenderClasses(): ClassInfo {\n    return {'disabled': this.isDisabled};\n  }\n\n  /**\n   * Handles rendering the headline and supporting text.\n   */\n  protected renderBody() {\n    return html`\n      <slot></slot>\n      <slot name=\"overline\" slot=\"overline\"></slot>\n      <slot name=\"headline\" slot=\"headline\"></slot>\n      <slot name=\"supporting-text\" slot=\"supporting-text\"></slot>\n      <slot\n        name=\"trailing-supporting-text\"\n        slot=\"trailing-supporting-text\"></slot>\n    `;\n  }\n\n  protected onFocus() {\n    if (this.tabIndex !== -1) {\n      return;\n    }\n    // Handles the case where the user clicks on the element and then tabs.\n    this.dispatchEvent(createRequestActivationEvent());\n  }\n\n  override focus() {\n    // TODO(b/300334509): needed for some cases where delegatesFocus doesn't\n    // work programmatically like in FF and select-option\n    this.listItemRoot?.focus();\n  }\n\n  override click() {\n    if (!this.listItemRoot) {\n      // If the element has not finished rendering, call super to ensure click\n      // events are dispatched.\n      super.click();\n      return;\n    }\n\n    // Forward click to the element to ensure link <a>.click() works correctly.\n    this.listItemRoot.click();\n  }\n}\n"]}