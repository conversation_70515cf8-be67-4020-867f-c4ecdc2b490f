//
// Copyright 2021 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use '../../elevation/elevation';
@use '../../tokens';
// go/keep-sorted end

$_md-sys-motion: tokens.md-sys-motion-values();

@mixin styles() {
  md-elevation {
    // TODO: replace duration with animation tokens
    transition-duration: 280ms;
    transition-timing-function: map.get($_md-sys-motion, 'emphasized-easing');
  }

  :host(:is([disabled], [soft-disabled])) md-elevation {
    transition: none;
  }

  md-elevation {
    @include elevation.theme(
      (
        'level': var(--_container-elevation),
        'shadow-color': var(--_container-shadow-color),
      )
    );
  }

  // apply elevation in order of focused, hovered, pressed, disabled
  // this ensures a button will have hover elevation after being focused
  :host(:focus-within) md-elevation {
    @include elevation.theme(
      (
        'level': var(--_focus-container-elevation),
      )
    );
  }

  :host(:hover) md-elevation {
    @include elevation.theme(
      (
        'level': var(--_hover-container-elevation),
      )
    );
  }

  :host(:active) md-elevation {
    @include elevation.theme(
      (
        'level': var(--_pressed-container-elevation),
      )
    );
  }

  :host(:is([disabled], [soft-disabled])) md-elevation {
    @include elevation.theme(
      (
        'level': var(--_disabled-container-elevation),
      )
    );
  }
}
