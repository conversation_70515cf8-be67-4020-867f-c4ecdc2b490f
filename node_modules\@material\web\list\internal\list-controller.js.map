{"version": 3, "file": "list-controller.js", "sourceRoot": "", "sources": ["list-controller.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EACL,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,oBAAoB,EACpB,aAAa,EACb,uBAAuB,GAExB,MAAM,8BAA8B,CAAC;AAEtC,sDAAsD;AAEtD;;GAEG;AACH,0DAA0D;AAC1D,MAAM,CAAC,MAAM,aAAa,GAAG;IAC3B,SAAS,EAAE,WAAW;IACtB,SAAS,EAAE,WAAW;IACtB,OAAO,EAAE,SAAS;IAClB,UAAU,EAAE,YAAY;IACxB,IAAI,EAAE,MAAM;IACZ,GAAG,EAAE,KAAK;CACF,CAAC;AAmDX;;GAEG;AACH,MAAM,OAAO,cAAc;IAUzB,YAAY,MAAkC;QA+C9C;;;WAGG;QACH,kBAAa,GAAG,CAAC,KAAoB,EAAE,EAAE;YACvC,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;YACtB,IAAI,KAAK,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxD,OAAO;YACT,CAAC;YACD,sEAAsE;YACtE,iCAAiC;YACjC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YAEzB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBAClB,OAAO;YACT,CAAC;YAED,MAAM,gBAAgB,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAElE,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC3B,MAAM,cAAc,GAAG,KAAK;gBAC1B,CAAC,CAAC,aAAa,CAAC,UAAU;gBAC1B,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC;YAC5B,MAAM,UAAU,GAAG,KAAK;gBACtB,CAAC,CAAC,aAAa,CAAC,SAAS;gBACzB,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC;YAE7B,IAAI,cAAc,GAAgB,IAAI,CAAC;YACvC,QAAQ,GAAG,EAAE,CAAC;gBACZ,yBAAyB;gBACzB,KAAK,aAAa,CAAC,SAAS,CAAC;gBAC7B,KAAK,UAAU;oBACb,cAAc,GAAG,gBAAgB,CAC/B,KAAK,EACL,gBAAgB,EAChB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,cAAc,EAAE,CACtB,CAAC;oBACF,MAAM;gBAER,6BAA6B;gBAC7B,KAAK,aAAa,CAAC,OAAO,CAAC;gBAC3B,KAAK,cAAc;oBACjB,cAAc,GAAG,oBAAoB,CACnC,KAAK,EACL,gBAAgB,EAChB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,cAAc,EAAE,CACtB,CAAC;oBACF,MAAM;gBAER,0BAA0B;gBAC1B,KAAK,aAAa,CAAC,IAAI;oBACrB,cAAc,GAAG,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC9D,MAAM;gBAER,yBAAyB;gBACzB,KAAK,aAAa,CAAC,GAAG;oBACpB,cAAc,GAAG,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC7D,MAAM;gBAER;oBACE,MAAM;YACV,CAAC;YAED,IACE,cAAc;gBACd,gBAAgB;gBAChB,gBAAgB,CAAC,IAAI,KAAK,cAAc,EACxC,CAAC;gBACD,mEAAmE;gBACnE,kBAAkB;gBAClB,gBAAgB,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;YACtC,CAAC;QACH,CAAC,CAAC;QA0CF;;WAEG;QACH,sBAAiB,GAAG,GAAG,EAAE;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YAEzB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC,CAAC;QAEF;;WAEG;QACH,wBAAmB,GAAG,CAAC,KAAY,EAAE,EAAE;YACrC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,MAAM,MAAM,GAAG,KAAK,CAAC,MAAc,CAAC;YACpC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC1B,MAAM,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC,CAAC;QAEF;;;WAGG;QACH,iBAAY,GAAG,GAAG,EAAE;YAClB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,8DAA8D;YAC9D,IAAI,oBAAoB,GAAG,KAAK,CAAC;YAEjC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;gBAEzD,IAAI,WAAW,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBACzC,oBAAoB,GAAG,IAAI,CAAC;oBAC5B,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;oBAClB,SAAS;gBACX,CAAC;gBAED,yCAAyC;gBACzC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;YACrB,CAAC;YAED,IAAI,oBAAoB,EAAE,CAAC;gBACzB,OAAO;YACT,CAAC;YAED,MAAM,oBAAoB,GAAG,uBAAuB,CAClD,KAAK,EACL,IAAI,CAAC,aAAa,CACnB,CAAC;YAEF,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC1B,OAAO;YACT,CAAC;YAED,oBAAoB,CAAC,QAAQ,GAAG,CAAC,CAAC;QACpC,CAAC,CAAC;QA7NA,MAAM,EACJ,MAAM,EACN,gBAAgB,EAChB,KAAK,EACL,cAAc,EACd,YAAY,EACZ,cAAc,EACd,aAAa,EACb,cAAc,GACf,GAAG,MAAM,CAAC;QACX,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;;OAGG;IACH,IAAI,KAAK;QACP,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC3C,MAAM,KAAK,GAAW,EAAE,CAAC;QAEzB,KAAK,MAAM,YAAY,IAAI,UAAU,EAAE,CAAC;YACtC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACzC,0DAA0D;YAC1D,IAAI,MAAM,EAAE,CAAC;gBACX,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACzB,SAAS;YACX,CAAC;YAED,qEAAqE;YACrE,MAAM,OAAO,GAAI,YAA4C,CAAC,IAAI,CAAC;YACnE,IAAI,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;gBACpC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtB,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAgFD;;;;;OAKG;IACH,gBAAgB;QACd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,gBAAgB,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAClE,IAAI,gBAAgB,EAAE,CAAC;YACrB,gBAAgB,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QACtC,CAAC;QACD,OAAO,gBAAgB,CACrB,KAAK,EACL,gBAAgB,EAChB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,cAAc,EAAE,CACtB,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,oBAAoB;QAClB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,gBAAgB,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAClE,IAAI,gBAAgB,EAAE,CAAC;YACrB,gBAAgB,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QACtC,CAAC;QACD,OAAO,oBAAoB,CACzB,KAAK,EACL,gBAAgB,EAChB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,cAAc,EAAE,CACtB,CAAC;IACJ,CAAC;CA4DF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {\n  activateFirstItem,\n  activateLastItem,\n  activateNextItem,\n  activatePreviousItem,\n  getActiveItem,\n  getFirstActivatableItem,\n  ListItem,\n} from './list-navigation-helpers.js';\n\n// TODO: move this file to List and make List use this\n\n/**\n * Default keys that trigger navigation.\n */\n// tslint:disable:enforce-name-casing Following Enum style\nexport const NavigableKeys = {\n  ArrowDown: 'ArrowDown',\n  ArrowLeft: 'ArrowLeft',\n  ArrowUp: 'ArrowUp',\n  ArrowRight: 'ArrowRight',\n  Home: 'Home',\n  End: 'End',\n} as const;\n// tslint:enable:enforce-name-casing\n\n/**\n * Default set of navigable keys.\n */\nexport type NavigableKeys = (typeof NavigableKeys)[keyof typeof NavigableKeys];\n\n/**\n * The configuration object to customize the behavior of the List Controller\n */\nexport interface ListControllerConfig<Item extends ListItem> {\n  /**\n   * A function that determines whether or not the given element is an Item\n   */\n  isItem: (item: HTMLElement) => item is Item;\n  /**\n   * A function that returns an array of elements to consider as items. For\n   * example, all the slotted elements.\n   */\n  getPossibleItems: () => HTMLElement[];\n  /**\n   * A function that returns whether or not the list is in an RTL context.\n   */\n  isRtl: () => boolean;\n  /**\n   * Deactivates an item such as setting the tabindex to -1 and or sets selected\n   * to false.\n   */\n  deactivateItem: (item: Item) => void;\n  /**\n   * Activates an item such as setting the tabindex to 1 and or sets selected to\n   * true (but does not focus).\n   */\n  activateItem: (item: Item) => void;\n  /**\n   * Whether or not the key should be handled by the list for navigation.\n   */\n  isNavigableKey: (key: string) => boolean;\n  /**\n   * Whether or not the item can be activated. Defaults to items that are not\n   * disabled.\n   */\n  isActivatable?: (item: Item) => boolean;\n  /**\n   * Whether or not navigating past the end of the list wraps to the beginning\n   * and vice versa. Defaults to true.\n   */\n  wrapNavigation?: () => boolean;\n}\n\n/**\n * A controller that handles list keyboard navigation and item management.\n */\nexport class ListController<Item extends ListItem> {\n  isItem: (item: HTMLElement) => item is Item;\n  private readonly getPossibleItems: () => HTMLElement[];\n  private readonly isRtl: () => boolean;\n  private readonly deactivateItem: (item: Item) => void;\n  private readonly activateItem: (item: Item) => void;\n  private readonly isNavigableKey: (key: string) => boolean;\n  private readonly isActivatable?: (item: Item) => boolean;\n  private readonly wrapNavigation: () => boolean;\n\n  constructor(config: ListControllerConfig<Item>) {\n    const {\n      isItem,\n      getPossibleItems,\n      isRtl,\n      deactivateItem,\n      activateItem,\n      isNavigableKey,\n      isActivatable,\n      wrapNavigation,\n    } = config;\n    this.isItem = isItem;\n    this.getPossibleItems = getPossibleItems;\n    this.isRtl = isRtl;\n    this.deactivateItem = deactivateItem;\n    this.activateItem = activateItem;\n    this.isNavigableKey = isNavigableKey;\n    this.isActivatable = isActivatable;\n    this.wrapNavigation = wrapNavigation ?? (() => true);\n  }\n\n  /**\n   * The items being managed by the list. Additionally, attempts to see if the\n   * object has a sub-item in the `.item` property.\n   */\n  get items(): Item[] {\n    const maybeItems = this.getPossibleItems();\n    const items: Item[] = [];\n\n    for (const itemOrParent of maybeItems) {\n      const isItem = this.isItem(itemOrParent);\n      // if the item is a list item, add it to the list of items\n      if (isItem) {\n        items.push(itemOrParent);\n        continue;\n      }\n\n      // If the item exposes an `item` property check if it is a list item.\n      const subItem = (itemOrParent as HTMLElement & {item?: Item}).item;\n      if (subItem && this.isItem(subItem)) {\n        items.push(subItem);\n      }\n    }\n\n    return items;\n  }\n\n  /**\n   * Handles keyboard navigation. Should be bound to the node that will act as\n   * the List.\n   */\n  handleKeydown = (event: KeyboardEvent) => {\n    const key = event.key;\n    if (event.defaultPrevented || !this.isNavigableKey(key)) {\n      return;\n    }\n    // do not use this.items directly in upcoming calculations so we don't\n    // re-query the DOM unnecessarily\n    const items = this.items;\n\n    if (!items.length) {\n      return;\n    }\n\n    const activeItemRecord = getActiveItem(items, this.isActivatable);\n\n    event.preventDefault();\n\n    const isRtl = this.isRtl();\n    const inlinePrevious = isRtl\n      ? NavigableKeys.ArrowRight\n      : NavigableKeys.ArrowLeft;\n    const inlineNext = isRtl\n      ? NavigableKeys.ArrowLeft\n      : NavigableKeys.ArrowRight;\n\n    let nextActiveItem: Item | null = null;\n    switch (key) {\n      // Activate the next item\n      case NavigableKeys.ArrowDown:\n      case inlineNext:\n        nextActiveItem = activateNextItem(\n          items,\n          activeItemRecord,\n          this.isActivatable,\n          this.wrapNavigation(),\n        );\n        break;\n\n      // Activate the previous item\n      case NavigableKeys.ArrowUp:\n      case inlinePrevious:\n        nextActiveItem = activatePreviousItem(\n          items,\n          activeItemRecord,\n          this.isActivatable,\n          this.wrapNavigation(),\n        );\n        break;\n\n      // Activate the first item\n      case NavigableKeys.Home:\n        nextActiveItem = activateFirstItem(items, this.isActivatable);\n        break;\n\n      // Activate the last item\n      case NavigableKeys.End:\n        nextActiveItem = activateLastItem(items, this.isActivatable);\n        break;\n\n      default:\n        break;\n    }\n\n    if (\n      nextActiveItem &&\n      activeItemRecord &&\n      activeItemRecord.item !== nextActiveItem\n    ) {\n      // If a new item was activated, remove the tabindex of the previous\n      // activated item.\n      activeItemRecord.item.tabIndex = -1;\n    }\n  };\n\n  /**\n   * Activates the next item in the list. If at the end of the list, the first\n   * item will be activated.\n   *\n   * @return The activated list item or `null` if there are no items.\n   */\n  activateNextItem(): Item | null {\n    const items = this.items;\n    const activeItemRecord = getActiveItem(items, this.isActivatable);\n    if (activeItemRecord) {\n      activeItemRecord.item.tabIndex = -1;\n    }\n    return activateNextItem(\n      items,\n      activeItemRecord,\n      this.isActivatable,\n      this.wrapNavigation(),\n    );\n  }\n\n  /**\n   * Activates the previous item in the list. If at the start of the list, the\n   * last item will be activated.\n   *\n   * @return The activated list item or `null` if there are no items.\n   */\n  activatePreviousItem(): Item | null {\n    const items = this.items;\n    const activeItemRecord = getActiveItem(items, this.isActivatable);\n    if (activeItemRecord) {\n      activeItemRecord.item.tabIndex = -1;\n    }\n    return activatePreviousItem(\n      items,\n      activeItemRecord,\n      this.isActivatable,\n      this.wrapNavigation(),\n    );\n  }\n\n  /**\n   * Listener to be bound to the `deactivate-items` item event.\n   */\n  onDeactivateItems = () => {\n    const items = this.items;\n\n    for (const item of items) {\n      this.deactivateItem(item);\n    }\n  };\n\n  /**\n   * Listener to be bound to the `request-activation` item event..\n   */\n  onRequestActivation = (event: Event) => {\n    this.onDeactivateItems();\n    const target = event.target as Item;\n    this.activateItem(target);\n    target.focus();\n  };\n\n  /**\n   * Listener to be bound to the `slotchange` event for the slot that renders\n   * the items.\n   */\n  onSlotchange = () => {\n    const items = this.items;\n    // Whether we have encountered an item that has been activated\n    let encounteredActivated = false;\n\n    for (const item of items) {\n      const isActivated = !item.disabled && item.tabIndex > -1;\n\n      if (isActivated && !encounteredActivated) {\n        encounteredActivated = true;\n        item.tabIndex = 0;\n        continue;\n      }\n\n      // Deactivate the rest including disabled\n      item.tabIndex = -1;\n    }\n\n    if (encounteredActivated) {\n      return;\n    }\n\n    const firstActivatableItem = getFirstActivatableItem(\n      items,\n      this.isActivatable,\n    );\n\n    if (!firstActivatableItem) {\n      return;\n    }\n\n    firstActivatableItem.tabIndex = 0;\n  };\n}\n"]}