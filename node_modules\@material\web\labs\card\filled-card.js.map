{"version": 3, "file": "filled-card.js", "sourceRoot": "", "sources": ["filled-card.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,IAAI,EAAC,MAAM,oBAAoB,CAAC;AACxC,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AACnE,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAQnE;;;GAGG;AAEI,IAAM,YAAY,GAAlB,MAAM,YAAa,SAAQ,IAAI;;AACpB,mBAAM,GAAwB,CAAC,YAAY,EAAE,YAAY,CAAC,AAApD,CAAqD;AADhE,YAAY;IADxB,aAAa,CAAC,gBAAgB,CAAC;GACnB,YAAY,CAExB", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Card} from './internal/card.js';\nimport {styles as filledStyles} from './internal/filled-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-filled-card': MdFilledCard;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-filled-card')\nexport class MdFilledCard extends Card {\n  static override styles: CSSResultOrNative[] = [sharedStyles, filledStyles];\n}\n"]}