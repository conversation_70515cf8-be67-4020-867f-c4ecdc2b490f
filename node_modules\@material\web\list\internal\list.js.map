{"version": 3, "file": "list.js", "sourceRoot": "", "sources": ["list.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAC,MAAM,KAAK,CAAC;AAC/C,OAAO,EAAC,qBAAqB,EAAC,MAAM,mBAAmB,CAAC;AAExD,OAAO,EAAC,cAAc,EAAE,aAAa,EAAC,MAAM,sBAAsB,CAAC;AAGnE,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAS,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;AAMxE,gEAAgE;AAChE,MAAM,OAAO,IAAK,SAAQ,UAAU;IAYlC,cAAc;IACd,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;IACnC,CAAC;IAqBD;QACE,KAAK,EAAE,CAAC;QApBO,mBAAc,GAAG,IAAI,cAAc,CAAW;YAC7D,MAAM,EAAE,CAAC,IAAiB,EAAoB,EAAE,CAC9C,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;YACnC,gBAAgB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS;YACtC,KAAK,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,KAAK;YACvD,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE;gBACvB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;YACrB,CAAC;YACD,YAAY,EAAE,CAAC,IAAI,EAAE,EAAE;gBACrB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;YACpB,CAAC;YACD,cAAc,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC;YACnD,aAAa,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;SAChE,CAAC,CAAC;QAEc,cAAS;QACxB,0BAA0B;QACzB,IAAoB,CAAC,eAAe,EAAE,CAAC;QAIxC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,CAAC;YAC7B,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAEkB,MAAM;QACvB,OAAO,IAAI,CAAA;;4BAEa,IAAI,CAAC,cAAc,CAAC,iBAAiB;8BACnC,IAAI,CAAC,cAAc,CAAC,mBAAmB;sBAC/C,IAAI,CAAC,cAAc,CAAC,YAAY;;KAEjD,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;IAChD,CAAC;IAED;;;;;OAKG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAAE,CAAC;IACpD,CAAC;CACF;AA/DW;IADT,qBAAqB,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;uCACmC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, isServer, LitElement} from 'lit';\nimport {queryAssignedElements} from 'lit/decorators.js';\n\nimport {ListController, NavigableKeys} from './list-controller.js';\nimport {ListItem as SharedListItem} from './list-navigation-helpers.js';\n\nconst NAVIGABLE_KEY_SET = new Set<string>(Object.values(NavigableKeys));\n\ninterface ListItem extends SharedListItem {\n  type: 'text' | 'button' | 'link';\n}\n\n// tslint:disable-next-line:enforce-comments-on-exported-symbols\nexport class List extends LitElement {\n  /**\n   * An array of activatable and disableable list items. Queries every assigned\n   * element that has the `md-list-item` attribute.\n   *\n   * _NOTE:_ This is a shallow, flattened query via\n   * `HTMLSlotElement.queryAssignedElements` and thus will _only_ include direct\n   * children / directly slotted elements.\n   */\n  @queryAssignedElements({flatten: true})\n  protected slotItems!: Array<ListItem | (HTMLElement & {item?: ListItem})>;\n\n  /** @export */\n  get items() {\n    return this.listController.items;\n  }\n\n  private readonly listController = new ListController<ListItem>({\n    isItem: (item: HTMLElement): item is ListItem =>\n      item.hasAttribute('md-list-item'),\n    getPossibleItems: () => this.slotItems,\n    isRtl: () => getComputedStyle(this).direction === 'rtl',\n    deactivateItem: (item) => {\n      item.tabIndex = -1;\n    },\n    activateItem: (item) => {\n      item.tabIndex = 0;\n    },\n    isNavigableKey: (key) => NAVIGABLE_KEY_SET.has(key),\n    isActivatable: (item) => !item.disabled && item.type !== 'text',\n  });\n\n  private readonly internals =\n    // Cast needed for closure\n    (this as HTMLElement).attachInternals();\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.internals.role = 'list';\n      this.addEventListener('keydown', this.listController.handleKeydown);\n    }\n  }\n\n  protected override render() {\n    return html`\n      <slot\n        @deactivate-items=${this.listController.onDeactivateItems}\n        @request-activation=${this.listController.onRequestActivation}\n        @slotchange=${this.listController.onSlotchange}>\n      </slot>\n    `;\n  }\n\n  /**\n   * Activates the next item in the list. If at the end of the list, the first\n   * item will be activated.\n   *\n   * @return The activated list item or `null` if there are no items.\n   */\n  activateNextItem(): ListItem | null {\n    return this.listController.activateNextItem();\n  }\n\n  /**\n   * Activates the previous item in the list. If at the start of the list, the\n   * last item will be activated.\n   *\n   * @return The activated list item or `null` if there are no items.\n   */\n  activatePreviousItem(): ListItem | null {\n    return this.listController.activatePreviousItem();\n  }\n}\n"]}