//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
@use 'sass:string';
// go/keep-sorted end
// go/keep-sorted start
@use './internal/shape';
@use './internal/validate';
@use './md-sys-color';
@use './md-sys-shape';
@use './md-sys-state';
@use './md-sys-typescale';
@use './v0_192/md-comp-filled-text-field';
// go/keep-sorted end

$supported-tokens: (
  // go/keep-sorted start
  'active-indicator-color',
  'active-indicator-height',
  'bottom-space',
  'caret-color',
  'container-color',
  'container-shape',
  'container-shape-end-end',
  'container-shape-end-start',
  'container-shape-start-end',
  'container-shape-start-start',
  'disabled-active-indicator-color',
  'disabled-active-indicator-height',
  'disabled-active-indicator-opacity',
  'disabled-container-color',
  'disabled-container-opacity',
  'disabled-input-text-color',
  'disabled-input-text-opacity',
  'disabled-label-text-color',
  'disabled-label-text-opacity',
  'disabled-leading-icon-color',
  'disabled-leading-icon-opacity',
  'disabled-supporting-text-color',
  'disabled-supporting-text-opacity',
  'disabled-trailing-icon-color',
  'disabled-trailing-icon-opacity',
  'error-active-indicator-color',
  'error-focus-active-indicator-color',
  'error-focus-caret-color',
  'error-focus-input-text-color',
  'error-focus-label-text-color',
  'error-focus-leading-icon-color',
  'error-focus-supporting-text-color',
  'error-focus-trailing-icon-color',
  'error-hover-active-indicator-color',
  'error-hover-input-text-color',
  'error-hover-label-text-color',
  'error-hover-leading-icon-color',
  'error-hover-state-layer-color',
  'error-hover-state-layer-opacity',
  'error-hover-supporting-text-color',
  'error-hover-trailing-icon-color',
  'error-input-text-color',
  'error-label-text-color',
  'error-leading-icon-color',
  'error-supporting-text-color',
  'error-trailing-icon-color',
  'focus-active-indicator-color',
  'focus-active-indicator-height',
  'focus-caret-color',
  'focus-input-text-color',
  'focus-label-text-color',
  'focus-leading-icon-color',
  'focus-supporting-text-color',
  'focus-trailing-icon-color',
  'hover-active-indicator-color',
  'hover-active-indicator-height',
  'hover-input-text-color',
  'hover-label-text-color',
  'hover-leading-icon-color',
  'hover-state-layer-color',
  'hover-state-layer-opacity',
  'hover-supporting-text-color',
  'hover-trailing-icon-color',
  'icon-input-space',
  'input-text-color',
  'input-text-font',
  'input-text-line-height',
  'input-text-placeholder-color',
  'input-text-prefix-color',
  'input-text-prefix-trailing-space',
  'input-text-size',
  'input-text-suffix-color',
  'input-text-suffix-leading-space',
  'input-text-weight',
  'label-text-color',
  'label-text-font',
  'label-text-line-height',
  'label-text-populated-line-height',
  'label-text-populated-size',
  'label-text-size',
  'label-text-weight',
  'leading-icon-color',
  'leading-icon-size',
  'leading-space',
  'supporting-text-color',
  'supporting-text-font',
  'supporting-text-line-height',
  'supporting-text-size',
  'supporting-text-weight',
  'top-space',
  'trailing-icon-color',
  'trailing-icon-size',
  'trailing-space',
  'with-label-bottom-space',
  'with-label-top-space',
  'with-leading-icon-leading-space',
  'with-trailing-icon-trailing-space',
  // go/keep-sorted end
);

$unsupported-tokens: (
  // go/keep-sorted start
  'input-text-tracking',
  'input-text-type',
  'label-text-tracking',
  'label-text-type',
  'supporting-text-tracking',
  'supporting-text-type',
  // go/keep-sorted end
);

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $tokens: md-comp-filled-text-field.values($deps, $exclude-hardcoded-values);
  $new-tokens: map.merge(
    shape.get-new-logical-shape-tokens($tokens, 'container-shape'),
    (
      'icon-input-space': if($exclude-hardcoded-values, null, 16px),
      'leading-space': if($exclude-hardcoded-values, null, 16px),
      'trailing-space': if($exclude-hardcoded-values, null, 16px),
      'top-space': if($exclude-hardcoded-values, null, 16px),
      'bottom-space': if($exclude-hardcoded-values, null, 16px),
      'input-text-prefix-trailing-space':
        if($exclude-hardcoded-values, null, 2px),
      'input-text-suffix-leading-space':
        if($exclude-hardcoded-values, null, 2px),
      'with-label-top-space': if($exclude-hardcoded-values, null, 8px),
      'with-label-bottom-space': if($exclude-hardcoded-values, null, 8px),
      // TODO(b/270705687): remove when focus-caret-color token added
      'focus-caret-color': map.get($deps, 'md-sys-color', 'primary'),
      'with-leading-icon-leading-space':
        if($exclude-hardcoded-values, null, 12px),
      'with-trailing-icon-trailing-space':
        if($exclude-hardcoded-values, null, 12px),
    )
  );

  $tokens: validate.values(
    $tokens,
    $supported-tokens: $supported-tokens,
    $unsupported-tokens: $unsupported-tokens,
    $new-tokens: $new-tokens
  );

  $tokens: map.merge(
    $tokens,
    (
      // TODO(b/259455114): remove when focus tokens update to 3px
      'focus-active-indicator-height': if($exclude-hardcoded-values, null, 3px)
    )
  );

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      @if string.index($token, 'container-shape-') == 1 {
        // Add fallback to shorthand for logical shape properties.
        $value: var(--md-filled-text-field-container-shape, #{$value});
      }

      $tokens: map.set(
        $tokens,
        $token,
        var(--md-filled-text-field-#{$token}, #{$value})
      );
    }
  }

  @return $tokens;
}
