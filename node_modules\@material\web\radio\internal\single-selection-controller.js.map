{"version": 3, "file": "single-selection-controller.js", "sourceRoot": "", "sources": ["single-selection-controller.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAcH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,MAAM,OAAO,yBAAyB;IACpC;;;OAGG;IACH,IAAI,QAAQ;QACV,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;QAED,0EAA0E;QAC1E,6DAA6D;QAC7D,OAAO,KAAK,CAAC,IAAI,CACf,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAyB,UAAU,IAAI,IAAI,CAAC,CACH,CAAC;IACxE,CAAC;IAKD,YAA6B,IAA4B;QAA5B,SAAI,GAAJ,IAAI,CAAwB;QAHjD,YAAO,GAAG,KAAK,CAAC;QAChB,SAAI,GAAsB,IAAI,CAAC;QAyCtB,kBAAa,GAAG,GAAG,EAAE;YACpC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,CAAC;QAEe,mBAAc,GAAG,GAAG,EAAE;YACrC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,CAAC;QAqCF;;;;WAIG;QACc,kBAAa,GAAG,CAAC,KAAoB,EAAE,EAAE;YACxD,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,KAAK,WAAW,CAAC;YACzC,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,SAAS,CAAC;YACrC,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,KAAK,WAAW,CAAC;YACzC,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,KAAK,YAAY,CAAC;YAC3C,wBAAwB;YACxB,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC5C,OAAO;YACT,CAAC;YAED,2DAA2D;YAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACrB,OAAO;YACT,CAAC;YAED,8DAA8D;YAC9D,qDAAqD;YACrD,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,wCAAwC;YACxC,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC;YAC9D,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC;YAE9D,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,IAAI,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC;YACzD,8DAA8D;YAC9D,8DAA8D;YAC9D,OAAO,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC/B,IAAI,SAAS,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;oBACjC,gDAAgD;oBAChD,SAAS,GAAG,CAAC,CAAC;gBAChB,CAAC;qBAAM,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;oBACzB,6CAA6C;oBAC7C,SAAS,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;gBAClC,CAAC;gBAED,gDAAgD;gBAChD,yCAAyC;gBACzC,MAAM,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;gBACxC,IAAI,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;oBACzC,IAAI,QAAQ,EAAE,CAAC;wBACb,SAAS,EAAE,CAAC;oBACd,CAAC;yBAAM,CAAC;wBACN,SAAS,EAAE,CAAC;oBACd,CAAC;oBAED,SAAS;gBACX,CAAC;gBAED,uDAAuD;gBACvD,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;oBAC/B,IAAI,OAAO,KAAK,WAAW,EAAE,CAAC;wBAC5B,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;wBACxB,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;wBACtB,OAAO,CAAC,IAAI,EAAE,CAAC;oBACjB,CAAC;gBACH,CAAC;gBAED,0EAA0E;gBAC1E,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC;gBAC3B,WAAW,CAAC,QAAQ,GAAG,CAAC,CAAC;gBACzB,WAAW,CAAC,KAAK,EAAE,CAAC;gBACpB,sEAAsE;gBACtE,qDAAqD;gBACrD,WAAW,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;gBAEhE,MAAM;YACR,CAAC;QACH,CAAC,CAAC;IA9J0D,CAAC;IAE7D,aAAa;QACX,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAgB,CAAC;QAClD,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC1D,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC1D,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5D,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACtB,uEAAuE;YACvE,wCAAwC;YACxC,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;QAED,mCAAmC;QACnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED,gBAAgB;QACd,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC7D,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC7D,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC/D,gDAAgD;QAChD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;;OAGG;IACH,mBAAmB;QACjB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAYO,eAAe;QACrB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACpC,IAAI,OAAO,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC1B,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,2DAA2D;QAC3D,oDAAoD;QACpD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACnE,mEAAmE;QACnE,IAAI,cAAc,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACnC,MAAM,SAAS,GAAG,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC;YAC9C,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC;YAEvB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;oBAC1B,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC;YACD,OAAO;QACT,CAAC;QAED,wDAAwD;QACxD,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;CA6EF", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {ReactiveController} from 'lit';\n\n/**\n * An element that supports single-selection with `SingleSelectionController`.\n */\nexport interface SingleSelectionElement extends HTMLElement {\n  /**\n   * Whether or not the element is selected.\n   */\n  checked: boolean;\n}\n\n/**\n * A `ReactiveController` that provides root node-scoped single selection for\n * elements, similar to native `<input type=\"radio\">` selection.\n *\n * To use, elements should add the controller and call\n * `selectionController.handleCheckedChange()` in a getter/setter. This must\n * be synchronous to match native behavior.\n *\n * @example\n * const CHECKED = Symbol('checked');\n *\n * class MyToggle extends LitElement {\n *   get checked() { return this[CHECKED]; }\n *   set checked(checked: boolean) {\n *     const oldValue = this.checked;\n *     if (oldValue === checked) {\n *       return;\n *     }\n *\n *     this[CHECKED] = checked;\n *     this.selectionController.handleCheckedChange();\n *     this.requestUpdate('checked', oldValue);\n *   }\n *\n *   [CHECKED] = false;\n *\n *   private selectionController = new SingleSelectionController(this);\n *\n *   constructor() {\n *     super();\n *     this.addController(this.selectionController);\n *   }\n * }\n */\nexport class SingleSelectionController implements ReactiveController {\n  /**\n   * All single selection elements in the host element's root with the same\n   * `name` attribute, including the host element.\n   */\n  get controls(): [SingleSelectionElement, ...SingleSelectionElement[]] {\n    const name = this.host.getAttribute('name');\n    if (!name || !this.root || !this.host.isConnected) {\n      return [this.host];\n    }\n\n    // Cast as unknown since there is not enough information for typescript to\n    // know that there is always at least one element (the host).\n    return Array.from(\n      this.root.querySelectorAll<SingleSelectionElement>(`[name=\"${name}\"]`),\n    ) as unknown as [SingleSelectionElement, ...SingleSelectionElement[]];\n  }\n\n  private focused = false;\n  private root: ParentNode | null = null;\n\n  constructor(private readonly host: SingleSelectionElement) {}\n\n  hostConnected() {\n    this.root = this.host.getRootNode() as ParentNode;\n    this.host.addEventListener('keydown', this.handleKeyDown);\n    this.host.addEventListener('focusin', this.handleFocusIn);\n    this.host.addEventListener('focusout', this.handleFocusOut);\n    if (this.host.checked) {\n      // Uncheck other siblings when attached if already checked. This mimics\n      // native <input type=\"radio\"> behavior.\n      this.uncheckSiblings();\n    }\n\n    // Update for the newly added host.\n    this.updateTabIndices();\n  }\n\n  hostDisconnected() {\n    this.host.removeEventListener('keydown', this.handleKeyDown);\n    this.host.removeEventListener('focusin', this.handleFocusIn);\n    this.host.removeEventListener('focusout', this.handleFocusOut);\n    // Update for siblings that are still connected.\n    this.updateTabIndices();\n    this.root = null;\n  }\n\n  /**\n   * Should be called whenever the host's `checked` property changes\n   * synchronously.\n   */\n  handleCheckedChange() {\n    if (!this.host.checked) {\n      return;\n    }\n\n    this.uncheckSiblings();\n    this.updateTabIndices();\n  }\n\n  private readonly handleFocusIn = () => {\n    this.focused = true;\n    this.updateTabIndices();\n  };\n\n  private readonly handleFocusOut = () => {\n    this.focused = false;\n    this.updateTabIndices();\n  };\n\n  private uncheckSiblings() {\n    for (const sibling of this.controls) {\n      if (sibling !== this.host) {\n        sibling.checked = false;\n      }\n    }\n  }\n\n  /**\n   * Updates the `tabindex` of the host and its siblings.\n   */\n  private updateTabIndices() {\n    // There are three tabindex states for a group of elements:\n    // 1. If any are checked, that element is focusable.\n    const siblings = this.controls;\n    const checkedSibling = siblings.find((sibling) => sibling.checked);\n    // 2. If an element is focused, the others are no longer focusable.\n    if (checkedSibling || this.focused) {\n      const focusable = checkedSibling || this.host;\n      focusable.tabIndex = 0;\n\n      for (const sibling of siblings) {\n        if (sibling !== focusable) {\n          sibling.tabIndex = -1;\n        }\n      }\n      return;\n    }\n\n    // 3. If none are checked or focused, all are focusable.\n    for (const sibling of siblings) {\n      sibling.tabIndex = 0;\n    }\n  }\n\n  /**\n   * Handles arrow key events from the host. Using the arrow keys will\n   * select and check the next or previous sibling with the host's\n   * `name` attribute.\n   */\n  private readonly handleKeyDown = (event: KeyboardEvent) => {\n    const isDown = event.key === 'ArrowDown';\n    const isUp = event.key === 'ArrowUp';\n    const isLeft = event.key === 'ArrowLeft';\n    const isRight = event.key === 'ArrowRight';\n    // Ignore non-arrow keys\n    if (!isLeft && !isRight && !isDown && !isUp) {\n      return;\n    }\n\n    // Don't try to select another sibling if there aren't any.\n    const siblings = this.controls;\n    if (!siblings.length) {\n      return;\n    }\n\n    // Prevent default interactions on the element for arrow keys,\n    // since this controller will introduce new behavior.\n    event.preventDefault();\n\n    // Check if moving forwards or backwards\n    const isRtl = getComputedStyle(this.host).direction === 'rtl';\n    const forwards = isRtl ? isLeft || isDown : isRight || isDown;\n\n    const hostIndex = siblings.indexOf(this.host);\n    let nextIndex = forwards ? hostIndex + 1 : hostIndex - 1;\n    // Search for the next sibling that is not disabled to select.\n    // If we return to the host index, there is nothing to select.\n    while (nextIndex !== hostIndex) {\n      if (nextIndex >= siblings.length) {\n        // Return to start if moving past the last item.\n        nextIndex = 0;\n      } else if (nextIndex < 0) {\n        // Go to end if moving before the first item.\n        nextIndex = siblings.length - 1;\n      }\n\n      // Check if the next sibling is disabled. If so,\n      // move the index and continue searching.\n      const nextSibling = siblings[nextIndex];\n      if (nextSibling.hasAttribute('disabled')) {\n        if (forwards) {\n          nextIndex++;\n        } else {\n          nextIndex--;\n        }\n\n        continue;\n      }\n\n      // Uncheck and remove focusability from other siblings.\n      for (const sibling of siblings) {\n        if (sibling !== nextSibling) {\n          sibling.checked = false;\n          sibling.tabIndex = -1;\n          sibling.blur();\n        }\n      }\n\n      // The next sibling should be checked, focused and dispatch a change event\n      nextSibling.checked = true;\n      nextSibling.tabIndex = 0;\n      nextSibling.focus();\n      // Fire a change event since the change is triggered by a user action.\n      // This matches native <input type=\"radio\"> behavior.\n      nextSibling.dispatchEvent(new Event('change', {bubbles: true}));\n\n      break;\n    }\n  };\n}\n"]}