{"version": 3, "file": "secondary-tab.js", "sourceRoot": "", "sources": ["secondary-tab.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,YAAY,EAAC,MAAM,6BAA6B,CAAC;AACzD,OAAO,EAAC,MAAM,IAAI,eAAe,EAAC,MAAM,oCAAoC,CAAC;AAC7E,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,0BAA0B,CAAC;AAQhE,8BAA8B;AAC9B;;;;;GAKG;AAEI,IAAM,cAAc,GAApB,MAAM,cAAe,SAAQ,YAAY;;AAC9B,qBAAM,GAAwB,CAAC,YAAY,EAAE,eAAe,CAAC,AAAvD,CAAwD;AADnE,cAAc;IAD1B,aAAa,CAAC,kBAAkB,CAAC;GACrB,cAAc,CAE1B", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {SecondaryTab} from './internal/secondary-tab.js';\nimport {styles as secondaryStyles} from './internal/secondary-tab-styles.js';\nimport {styles as sharedStyles} from './internal/tab-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-secondary-tab': MdSecondaryTab;\n  }\n}\n\n// TODO(b/267336507): add docs\n/**\n * @summary Tab allow users to display a tab within a Tabs.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-secondary-tab')\nexport class MdSecondaryTab extends SecondaryTab {\n  static override styles: CSSResultOrNative[] = [sharedStyles, secondaryStyles];\n}\n"]}