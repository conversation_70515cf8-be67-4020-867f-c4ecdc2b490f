{"version": 3, "file": "outlined-field.js", "sourceRoot": "", "sources": ["outlined-field.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,aAAa,EAAC,MAAM,8BAA8B,CAAC;AAC3D,OAAO,EAAC,MAAM,IAAI,cAAc,EAAC,MAAM,+BAA+B,CAAC;AACvE,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAQnE;;;;GAIG;AAEI,IAAM,eAAe,GAArB,MAAM,eAAgB,SAAQ,aAAa;;AAChC,sBAAM,GAAwB,CAAC,YAAY,EAAE,cAAc,CAAC,AAAtD,CAAuD;AADlE,eAAe;IAD3B,aAAa,CAAC,mBAAmB,CAAC;GACtB,eAAe,CAE3B", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {OutlinedField} from './internal/outlined-field.js';\nimport {styles as outlinedStyles} from './internal/outlined-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-outlined-field': MdOutlinedField;\n  }\n}\n\n/**\n * TODO(b/228525797): add docs\n * @final\n * @suppress {visibility}\n */\n@customElement('md-outlined-field')\nexport class MdOutlinedField extends OutlinedField {\n  static override styles: CSSResultOrNative[] = [sharedStyles, outlinedStyles];\n}\n"]}