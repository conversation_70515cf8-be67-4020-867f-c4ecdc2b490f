{"version": 3, "file": "sub-menu.js", "sourceRoot": "", "sources": ["sub-menu.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAC,MAAM,KAAK,CAAC;AAC/C,OAAO,EAAC,QAAQ,EAAE,qBAAqB,EAAC,MAAM,mBAAmB,CAAC;AAElE,OAAO,EACL,0BAA0B,EAC1B,4BAA4B,EAC5B,oBAAoB,EACpB,uBAAuB,GACxB,MAAM,mDAAmD,CAAC;AAE3D,OAAO,EAEL,WAAW,EACX,4BAA4B,EAC5B,8BAA8B,EAC9B,eAAe,EAEf,YAAY,EACZ,YAAY,GACb,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAC,MAAM,EAAC,MAAM,YAAY,CAAC;AAElC;;;;;;;;;GASG;AACH,MAAM,OAAO,OAAQ,SAAQ,UAAU;IA0BrC,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;IAC/B,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;IAC/B,CAAC;IAWD;QACE,KAAK,EAAE,CAAC;QA3CV;;WAEG;QAEH,iBAAY,GAAW,MAAM,CAAC,SAAS,CAAC;QACxC;;WAEG;QACmC,eAAU,GAAW,MAAM,CAAC,WAAW,CAAC;QAC9E;;WAEG;QACsD,mBAAc,GAAG,GAAG,CAAC;QAC9E;;WAEG;QAEH,oBAAe,GAAG,GAAG,CAAC;QAEtB;;WAEG;QAEH,cAAS,GAAG,IAAI,CAAC;QAgBT,wBAAmB,GAAG,CAAC,CAAC;QACxB,yBAAoB,GAAG,CAAC,CAAC;QA4JjC;;;;;;;;WAQG;QACO,iBAAY,GAAG,GAAG,EAAE;YAC5B,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACvC,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACxC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI;gBAAE,OAAO;YAE5B,4DAA4D;YAC5D,iCAAiC;YACjC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACzB,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,GAAG,EAAE;oBACzC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACd,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC;QAEF;;;;;;;;WAQG;QACO,iBAAY,GAAG,GAAG,EAAE;YAC5B,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACxC,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEvC,6DAA6D;YAC7D,iCAAiC;YACjC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC1B,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC,GAAG,EAAE;oBAC1C,IAAI,CAAC,KAAK,EAAE,CAAC;gBACf,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC;QAtMA,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YACvD,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEQ,MAAM;QACb,OAAO,IAAI,CAAA;;;iBAGE,IAAI,CAAC,OAAO;mBACV,IAAI,CAAC,SAAS;sBACX,IAAI,CAAC,YAAY;;;;mBAIpB,IAAI,CAAC,gBAAgB;sBAClB,IAAI,CAAC,cAAc;sBACnB,IAAI,CAAC,YAAY;;KAElC,CAAC;IACJ,CAAC;IAEkB,YAAY;QAC7B,yDAAyD;QACzD,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;YAAE,OAAO;QAE/B,qEAAqE;QACrE,wEAAwE;QACxE,mDAAmD;QACnD,EAAE;QACF,uEAAuE;QACvE,mEAAmE;QACnE,uEAAuE;QACvE,uBAAuB;QACvB,IAAI,CAAC,gBAAgB,CACnB,QAAQ,EACR,GAAG,EAAE;YACH,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;YACjC,IAAI,CAAC,aAAa,CAAC,4BAA4B,EAAE,CAAC,CAAC;YACnD,IAAI,CAAC,aAAa,CAAC,0BAA0B,EAAE,CAAC,CAAC;YACjD,qEAAqE;YACrE,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;QAC3B,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,EAAC,CACb,CAAC;QAEF,wEAAwE;QACxE,sEAAsE;QACtE,uEAAuE;QACvE,qEAAqE;QACrE,uEAAuE;QACvE,sCAAsC;QACtC,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EAAE,CAAC;YACpC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAChC,CAAC;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,0EAA0E;QAC1E,gBAAgB;QAChB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACtC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC;QAC/B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,2EAA2E;QAC3E,gEAAgE;QAChE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QACpC,0EAA0E;QAC1E,qEAAqE;QACrE,yEAAyE;QACzE,sEAAsE;QACtE,uEAAuE;QACvE,uEAAuE;QACvE,gBAAgB;QAChB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAE9B,4DAA4D;QAC5D,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAChC,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,sEAAsE;QACtE,uDAAuD;QACvD,IAAI,CAAC,aAAa,CAAC,0BAA0B,EAAE,CAAC,CAAC;QACjD,IAAI,CAAC,aAAa,CAAC,8BAA8B,EAAE,CAAC,CAAC;QACrD,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAE1B,yEAAyE;QACzE,aAAa;QACb,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,IAAI,IAAI,GAAG,CAAC,KAAc,EAAE,EAAE,GAAE,CAAC,CAAC;YAClC,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBACrC,IAAI,GAAG,OAAO,CAAC;YACjB,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC;YACpD,MAAM,MAAM,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,OAAO;QAEhC,IAAI,CAAC,aAAa,CAAC,4BAA4B,EAAE,CAAC,CAAC;QACnD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,aAAa,CAAC,0BAA0B,EAAE,CAAC,CAAC;QACjD,IAAI,KAAK,GAAG,CAAC,KAAc,EAAE,EAAE,GAAE,CAAC,CAAC;QACnC,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACrC,KAAK,GAAG,OAAO,CAAC;QAClB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC;QACrD,MAAM,MAAM,CAAC;IACf,CAAC;IAES,YAAY;QACpB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QAED,wDAAwD;QACxD,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAChC,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;YAClB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAE1B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,2EAA2E;QAC3E,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;IAC3B,CAAC;IAmDS,OAAO;QACf,IAAI,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,SAAS,CAAC,KAAoB;QAC5C,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAE5D,IAAI,KAAK,CAAC,gBAAgB;YAAE,OAAO;QAEnC,MAAM,YAAY,GAChB,iBAAiB;YACjB,CAAC,YAAY,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC;QAE1E,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,CAAC,KAAK,IAAI,YAAY,EAAE,CAAC;YACtD,0EAA0E;YAC1E,uEAAuE;YACvE,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,IAAI,YAAY,EAAE,CAAC;gBACjB,KAAK,CAAC,eAAe,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;QACnC,MAAM,oBAAoB,GAAG,uBAAuB,CAAC,YAAY,CAAC,CAAC;QAEnE,IAAI,oBAAoB,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAElB,oBAAoB,CAAC,QAAQ,GAAG,CAAC,CAAC;YAClC,oBAAoB,CAAC,KAAK,EAAE,CAAC;YAE7B,OAAO;QACT,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,KAAqB;QAC1C,MAAM,EAAC,QAAQ,EAAE,MAAM,EAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QACxC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEzB,IAAI,CAAC,aAAa,CAAC,4BAA4B,EAAE,CAAC,CAAC;QACnD,yEAAyE;QACzE,gDAAgD;QAChD,IACE,MAAM,CAAC,IAAI,KAAK,WAAW,CAAC,OAAO;YACnC,MAAM,CAAC,GAAG,KAAK,eAAe,CAAC,MAAM,EACrC,CAAC;YACD,KAAK,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,4BAA4B,EAAE,CAAC,CAAC;YACxD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,0BAA0B,EAAE,CAAC,CAAC;IACnD,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,KAAoB;QACjD,IAAI,KAAK,CAAC,gBAAgB;YAAE,OAAO;QACnC,MAAM,EAAC,KAAK,EAAE,WAAW,EAAE,OAAO,EAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzE,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,0EAA0E;QAC1E,0EAA0E;QAC1E,uEAAuE;QACvE,KAAK,CAAC,cAAc,EAAE,CAAC;QAEvB,IAAI,OAAO,KAAK,YAAY,CAAC,IAAI,IAAI,OAAO,KAAK,YAAY,CAAC,KAAK,EAAE,CAAC;YACpE,wCAAwC;YACxC,KAAK,CAAC,eAAe,EAAE,CAAC;QAC1B,CAAC;QAED,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QAEnB,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC;IAED;;;;;;OAMG;IACK,gBAAgB,CAAC,IAAY;QACnC,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC;QACzD,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC;QACrE,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,aAAa,CAAC;YACnB,KAAK,YAAY,CAAC,KAAK,CAAC;YACxB,KAAK,YAAY,CAAC,KAAK;gBACrB,OAAO,IAAI,CAAC;YACd;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACK,iBAAiB,CAAC,IAAY;QACpC,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC;QACzD,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;QACrE,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,aAAa,CAAC;YACnB,KAAK,eAAe,CAAC,MAAM;gBACzB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAU,CAAC;YAC/C;gBACE,OAAO,EAAC,KAAK,EAAE,KAAK,EAAU,CAAC;QACnC,CAAC;IACH,CAAC;CACF;AA/WC;IADC,QAAQ,CAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAC;6CACC;AAIF;IAArC,QAAQ,CAAC,EAAC,SAAS,EAAE,aAAa,EAAC,CAAC;2CAAyC;AAIrB;IAAxD,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,kBAAkB,EAAC,CAAC;+CAAsB;AAK9E;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,mBAAmB,EAAC,CAAC;gDACnC;AAMtB;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAC,CAAC;0CAClD;AAWA;IADhB,qBAAqB,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;sCACjB;AAGnB;IADhB,qBAAqB,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;sCACrB", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, isServer, LitElement} from 'lit';\nimport {property, queryAssignedElements} from 'lit/decorators.js';\n\nimport {\n  createDeactivateItemsEvent,\n  createRequestActivationEvent,\n  deactivateActiveItem,\n  getFirstActivatableItem,\n} from '../../../list/internal/list-navigation-helpers.js';\nimport {MenuItem} from '../controllers/menuItemController.js';\nimport {\n  CloseMenuEvent,\n  CloseReason,\n  createActivateTypeaheadEvent,\n  createDeactivateTypeaheadEvent,\n  KeydownCloseKey,\n  Menu,\n  NavigableKey,\n  SelectionKey,\n} from '../controllers/shared.js';\nimport {Corner} from '../menu.js';\n\n/**\n * @fires deactivate-items {Event} Requests the parent menu to deselect other\n * items when a submenu opens. --bubbles --composed\n * @fires request-activation {Event} Requests the parent to make the slotted item\n * focusable and focus the item. --bubbles --composed\n * @fires deactivate-typeahead {Event} Requests the parent menu to deactivate\n * the typeahead functionality when a submenu opens. --bubbles --composed\n * @fires activate-typeahead {Event} Requests the parent menu to activate the\n * typeahead functionality when a submenu closes. --bubbles --composed\n */\nexport class SubMenu extends LitElement {\n  /**\n   * The anchorCorner to set on the submenu.\n   */\n  @property({attribute: 'anchor-corner'})\n  anchorCorner: Corner = Corner.START_END;\n  /**\n   * The menuCorner to set on the submenu.\n   */\n  @property({attribute: 'menu-corner'}) menuCorner: Corner = Corner.START_START;\n  /**\n   * The delay between mouseenter and submenu opening.\n   */\n  @property({type: Number, attribute: 'hover-open-delay'}) hoverOpenDelay = 400;\n  /**\n   * The delay between ponterleave and the submenu closing.\n   */\n  @property({type: Number, attribute: 'hover-close-delay'})\n  hoverCloseDelay = 400;\n\n  /**\n   * READONLY: self-identifies as a menu item and sets its identifying attribute\n   */\n  @property({type: Boolean, reflect: true, attribute: 'md-sub-menu'})\n  isSubMenu = true;\n\n  get item() {\n    return this.items[0] ?? null;\n  }\n\n  get menu() {\n    return this.menus[0] ?? null;\n  }\n\n  @queryAssignedElements({slot: 'item', flatten: true})\n  private readonly items!: MenuItem[];\n\n  @queryAssignedElements({slot: 'menu', flatten: true})\n  private readonly menus!: Menu[];\n\n  private previousOpenTimeout = 0;\n  private previousCloseTimeout = 0;\n\n  constructor() {\n    super();\n\n    if (!isServer) {\n      this.addEventListener('mouseenter', this.onMouseenter);\n      this.addEventListener('mouseleave', this.onMouseleave);\n    }\n  }\n\n  override render() {\n    return html`\n      <slot\n        name=\"item\"\n        @click=${this.onClick}\n        @keydown=${this.onKeydown}\n        @slotchange=${this.onSlotchange}>\n      </slot>\n      <slot\n        name=\"menu\"\n        @keydown=${this.onSubMenuKeydown}\n        @close-menu=${this.onCloseSubmenu}\n        @slotchange=${this.onSlotchange}>\n      </slot>\n    `;\n  }\n\n  protected override firstUpdated() {\n    // slotchange is not fired if the contents have been SSRd\n    this.onSlotchange();\n  }\n\n  /**\n   * Shows the submenu.\n   */\n  async show() {\n    const menu = this.menu;\n    if (!menu || menu.open) return;\n\n    // Ensures that we deselect items when the menu closes and reactivate\n    // typeahead when the menu closes, so that we do not have dirty state of\n    // `sub-menu > menu-item[selected]` when we reopen.\n    //\n    // This cannot happen in `close()` because the menu may close via other\n    // means Additionally, this cannot happen in onCloseSubmenu because\n    // `close-menu` may not be called via focusout of outside click and not\n    // triggered by an item\n    menu.addEventListener(\n      'closed',\n      () => {\n        this.item.ariaExpanded = 'false';\n        this.dispatchEvent(createActivateTypeaheadEvent());\n        this.dispatchEvent(createDeactivateItemsEvent());\n        // aria-hidden required so ChromeVox doesn't announce the closed menu\n        menu.ariaHidden = 'true';\n      },\n      {once: true},\n    );\n\n    // Parent menu is `position: absolute` – this creates a new CSS relative\n    // positioning context (similar to doing `position: relative`), so the\n    // submenu's `<md-menu slot=\"submenu\" positioning=\"document\">` would be\n    // wrong even if we change `md-sub-menu` from `position: relative` to\n    // `position: static` because the submenu it would still be positioning\n    // itself relative to the parent menu.\n    if (menu.positioning === 'document') {\n      menu.positioning = 'absolute';\n    }\n    menu.quick = true;\n    // Submenus are in overflow when not fixed. Can remove once we have native\n    // popup support\n    menu.hasOverflow = true;\n    menu.anchorCorner = this.anchorCorner;\n    menu.menuCorner = this.menuCorner;\n    menu.anchorElement = this.item;\n    menu.defaultFocus = 'first-item';\n    // aria-hidden management required so ChromeVox doesn't announce the closed\n    // menu. Remove it here since we are about to show and focus it.\n    menu.removeAttribute('aria-hidden');\n    // This is required in the case where we have a leaf menu open and and the\n    // user hovers a parent menu's item which is not an md-sub-menu item.\n    // If this were set to true, then the menu would close and focus would be\n    // lost. That means the focusout event would have a `relatedTarget` of\n    // `null` since nothing in the menu would be focused anymore due to the\n    // leaf menu closing. restoring focus ensures that we keep focus in the\n    // submenu tree.\n    menu.skipRestoreFocus = false;\n\n    // Menu could already be opened because of mouse interaction\n    const menuAlreadyOpen = menu.open;\n    menu.show();\n    this.item.ariaExpanded = 'true';\n    this.item.ariaHasPopup = 'menu';\n    if (menu.id) {\n      this.item.setAttribute('aria-controls', menu.id);\n    }\n\n    // Deactivate other items. This can be the case if the user has tabbed\n    // around the menu and then mouses over an md-sub-menu.\n    this.dispatchEvent(createDeactivateItemsEvent());\n    this.dispatchEvent(createDeactivateTypeaheadEvent());\n    this.item.selected = true;\n\n    // This is the case of mouse hovering when already opened via keyboard or\n    // vice versa\n    if (!menuAlreadyOpen) {\n      let open = (value: unknown) => {};\n      const opened = new Promise((resolve) => {\n        open = resolve;\n      });\n      menu.addEventListener('opened', open, {once: true});\n      await opened;\n    }\n  }\n\n  /**\n   * Closes the submenu.\n   */\n  async close() {\n    const menu = this.menu;\n    if (!menu || !menu.open) return;\n\n    this.dispatchEvent(createActivateTypeaheadEvent());\n    menu.quick = true;\n    menu.close();\n    this.dispatchEvent(createDeactivateItemsEvent());\n    let close = (value: unknown) => {};\n    const closed = new Promise((resolve) => {\n      close = resolve;\n    });\n    menu.addEventListener('closed', close, {once: true});\n    await closed;\n  }\n\n  protected onSlotchange() {\n    if (!this.item) {\n      return;\n    }\n\n    // TODO(b/301296618): clean up old aria values on change\n    this.item.ariaExpanded = 'false';\n    this.item.ariaHasPopup = 'menu';\n    if (this.menu?.id) {\n      this.item.setAttribute('aria-controls', this.menu.id);\n    }\n    this.item.keepOpen = true;\n\n    const menu = this.menu;\n    if (!menu) return;\n\n    menu.isSubmenu = true;\n    // Required for ChromeVox to not linearly navigate to the menu while closed\n    menu.ariaHidden = 'true';\n  }\n\n  /**\n   * Starts the default 400ms countdown to open the submenu.\n   *\n   * NOTE: We explicitly use mouse events and not pointer events because\n   * pointer events apply to touch events. And if a user were to tap a\n   * sub-menu, it would fire the \"pointerenter\", \"pointerleave\", \"click\" events\n   * which would open the menu on click, and then set the timeout to close the\n   * menu due to pointerleave.\n   */\n  protected onMouseenter = () => {\n    clearTimeout(this.previousOpenTimeout);\n    clearTimeout(this.previousCloseTimeout);\n    if (this.menu?.open) return;\n\n    // Open synchronously if delay is 0. (screenshot tests infra\n    // would never resolve otherwise)\n    if (!this.hoverOpenDelay) {\n      this.show();\n    } else {\n      this.previousOpenTimeout = setTimeout(() => {\n        this.show();\n      }, this.hoverOpenDelay);\n    }\n  };\n\n  /**\n   * Starts the default 400ms countdown to close the submenu.\n   *\n   * NOTE: We explicitly use mouse events and not pointer events because\n   * pointer events apply to touch events. And if a user were to tap a\n   * sub-menu, it would fire the \"pointerenter\", \"pointerleave\", \"click\" events\n   * which would open the menu on click, and then set the timeout to close the\n   * menu due to pointerleave.\n   */\n  protected onMouseleave = () => {\n    clearTimeout(this.previousCloseTimeout);\n    clearTimeout(this.previousOpenTimeout);\n\n    // Close synchronously if delay is 0. (screenshot tests infra\n    // would never resolve otherwise)\n    if (!this.hoverCloseDelay) {\n      this.close();\n    } else {\n      this.previousCloseTimeout = setTimeout(() => {\n        this.close();\n      }, this.hoverCloseDelay);\n    }\n  };\n\n  protected onClick() {\n    this.show();\n  }\n\n  /**\n   * On item keydown handles opening the submenu.\n   */\n  protected async onKeydown(event: KeyboardEvent) {\n    const shouldOpenSubmenu = this.isSubmenuOpenKey(event.code);\n\n    if (event.defaultPrevented) return;\n\n    const openedWithLR =\n      shouldOpenSubmenu &&\n      (NavigableKey.LEFT === event.code || NavigableKey.RIGHT === event.code);\n\n    if (event.code === SelectionKey.SPACE || openedWithLR) {\n      // prevent space from scrolling and Left + Right from selecting previous /\n      // next items or opening / closing parent menus. Only open the submenu.\n      event.preventDefault();\n\n      if (openedWithLR) {\n        event.stopPropagation();\n      }\n    }\n\n    if (!shouldOpenSubmenu) {\n      return;\n    }\n\n    const submenu = this.menu;\n    if (!submenu) return;\n\n    const submenuItems = submenu.items;\n    const firstActivatableItem = getFirstActivatableItem(submenuItems);\n\n    if (firstActivatableItem) {\n      await this.show();\n\n      firstActivatableItem.tabIndex = 0;\n      firstActivatableItem.focus();\n\n      return;\n    }\n  }\n\n  private onCloseSubmenu(event: CloseMenuEvent) {\n    const {itemPath, reason} = event.detail;\n    itemPath.push(this.item);\n\n    this.dispatchEvent(createActivateTypeaheadEvent());\n    // Escape should only close one menu not all of the menus unlike space or\n    // click selection which should close all menus.\n    if (\n      reason.kind === CloseReason.KEYDOWN &&\n      reason.key === KeydownCloseKey.ESCAPE\n    ) {\n      event.stopPropagation();\n      this.item.dispatchEvent(createRequestActivationEvent());\n      return;\n    }\n\n    this.dispatchEvent(createDeactivateItemsEvent());\n  }\n\n  private async onSubMenuKeydown(event: KeyboardEvent) {\n    if (event.defaultPrevented) return;\n    const {close: shouldClose, keyCode} = this.isSubmenuCloseKey(event.code);\n    if (!shouldClose) return;\n\n    // Communicate that it's handled so that we don't accidentally close every\n    // parent menu. Additionally, we want to isolate things like the typeahead\n    // keydowns from bubbling up to the parent menu and confounding things.\n    event.preventDefault();\n\n    if (keyCode === NavigableKey.LEFT || keyCode === NavigableKey.RIGHT) {\n      // Prevent this from bubbling to parents\n      event.stopPropagation();\n    }\n\n    await this.close();\n\n    deactivateActiveItem(this.menu.items);\n    this.item?.focus();\n    this.item.tabIndex = 0;\n    this.item.focus();\n  }\n\n  /**\n   * Determines whether the given KeyboardEvent code is one that should open\n   * the submenu. This is RTL-aware. By default, left, right, space, or enter.\n   *\n   * @param code The native KeyboardEvent code.\n   * @return Whether or not the key code should open the submenu.\n   */\n  private isSubmenuOpenKey(code: string) {\n    const isRtl = getComputedStyle(this).direction === 'rtl';\n    const arrowEnterKey = isRtl ? NavigableKey.LEFT : NavigableKey.RIGHT;\n    switch (code) {\n      case arrowEnterKey:\n      case SelectionKey.SPACE:\n      case SelectionKey.ENTER:\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  /**\n   * Determines whether the given KeyboardEvent code is one that should close\n   * the submenu. This is RTL-aware. By default right, left, or escape.\n   *\n   * @param code The native KeyboardEvent code.\n   * @return Whether or not the key code should close the submenu.\n   */\n  private isSubmenuCloseKey(code: string) {\n    const isRtl = getComputedStyle(this).direction === 'rtl';\n    const arrowEnterKey = isRtl ? NavigableKey.RIGHT : NavigableKey.LEFT;\n    switch (code) {\n      case arrowEnterKey:\n      case KeydownCloseKey.ESCAPE:\n        return {close: true, keyCode: code} as const;\n      default:\n        return {close: false} as const;\n    }\n  }\n}\n"]}