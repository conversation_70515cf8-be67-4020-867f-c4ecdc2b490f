//
// Copyright 2022 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:list';
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use '../../ripple/ripple';
@use '../../tokens';
// go/keep-sorted end

@mixin theme($tokens) {
  $supported-tokens: tokens.$md-comp-filled-icon-button-supported-tokens;
  @each $token, $value in $tokens {
    @if list.index($supported-tokens, $token) == null {
      @error 'Token `#{$token}` is not a supported token.';
    }

    @if $value {
      --md-filled-icon-button-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $tokens: tokens.md-comp-filled-icon-button-values();

  :host {
    // Only use the logical properties.
    $tokens: map.remove($tokens, 'container-shape');
    @each $token, $value in $tokens {
      --_#{$token}: #{$value};
    }
  }

  .icon-button {
    color: var(--_icon-color);

    &:hover {
      color: var(--_hover-icon-color);
    }

    &:focus {
      color: var(--_focus-icon-color);
    }

    &:active {
      color: var(--_pressed-icon-color);
    }

    &:is(:disabled, [aria-disabled='true']) {
      color: var(--_disabled-icon-color);
    }

    @include ripple.theme(
      (
        hover-color: var(--_hover-state-layer-color),
        hover-opacity: var(--_hover-state-layer-opacity),
        pressed-color: var(--_pressed-state-layer-color),
        pressed-opacity: var(--_pressed-state-layer-opacity),
      )
    );
  }

  .icon-button::before {
    // Background color, separate node for opacity changes
    background-color: var(--_container-color);
    border-radius: inherit;
    content: '';
    inset: 0;
    position: absolute;
    z-index: -1; // place behind content
  }

  .icon-button:is(:disabled, [aria-disabled='true'])::before {
    background-color: var(--_disabled-container-color);
    opacity: var(--_disabled-container-opacity);
  }

  .icon-button:is(:disabled, [aria-disabled='true']) .icon {
    opacity: var(--_disabled-icon-opacity);
  }

  .toggle-filled {
    &:not(:disabled, [aria-disabled='true']) {
      color: var(--_toggle-icon-color);

      &:hover {
        color: var(--_toggle-hover-icon-color);
      }

      &:focus {
        color: var(--_toggle-focus-icon-color);
      }

      &:active {
        color: var(--_toggle-pressed-icon-color);
      }
    }

    @include ripple.theme(
      (
        hover-color: var(--_toggle-hover-state-layer-color),
        pressed-color: var(--_toggle-pressed-state-layer-color),
      )
    );
  }

  .toggle-filled:not(:disabled, [aria-disabled='true'])::before {
    // Note: filled icon buttons have three container colors,
    // "container-color" for regular, then selected/unselected for toggle.
    background-color: var(--_unselected-container-color);
  }

  .selected {
    &:not(:disabled, [aria-disabled='true']) {
      color: var(--_toggle-selected-icon-color);

      &:hover {
        color: var(--_toggle-selected-hover-icon-color);
      }

      &:focus {
        color: var(--_toggle-selected-focus-icon-color);
      }

      &:active {
        color: var(--_toggle-selected-pressed-icon-color);
      }
    }

    @include ripple.theme(
      (
        hover-color: var(--_toggle-selected-hover-state-layer-color),
        pressed-color: var(--_toggle-selected-pressed-state-layer-color),
      )
    );
  }

  .selected:not(:disabled, [aria-disabled='true'])::before {
    background-color: var(--_selected-container-color);
  }
}
