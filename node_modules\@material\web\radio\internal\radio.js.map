{"version": 3, "file": "radio.js", "sourceRoot": "", "sources": ["radio.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;AAEH,OAAO,8BAA8B,CAAC;AACtC,OAAO,wBAAwB,CAAC;AAEhC,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAC,MAAM,KAAK,CAAC;AAC/C,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAC,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAErD,OAAO,EAAC,iBAAiB,EAAC,MAAM,gDAAgD,CAAC;AACjF,OAAO,EACL,eAAe,EACf,iBAAiB,EACjB,yBAAyB,GAC1B,MAAM,+CAA+C,CAAC;AACvD,OAAO,EACL,SAAS,EACT,qBAAqB,GACtB,MAAM,2CAA2C,CAAC;AACnD,OAAO,EAAC,cAAc,EAAC,MAAM,mCAAmC,CAAC;AACjE,OAAO,EACL,YAAY,EACZ,YAAY,EACZ,mBAAmB,GACpB,MAAM,yCAAyC,CAAC;AACjD,OAAO,EAAC,cAAc,EAAC,MAAM,oDAAoD,CAAC;AAElF,OAAO,EAAC,yBAAyB,EAAC,MAAM,kCAAkC,CAAC;AAE3E,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAClC,IAAI,MAAM,GAAG,CAAC,CAAC;AAEf,wCAAwC;AACxC,MAAM,cAAc,GAAG,yBAAyB,CAC9C,mBAAmB,CAAC,qBAAqB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,CACvE,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,OAAO,KAAM,SAAQ,cAAc;IAKvC;;OAEG;IAEH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,OAAO,CAAC,OAAgB;QAC1B,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC;QAChC,IAAI,UAAU,KAAK,OAAO,EAAE,CAAC;YAC3B,OAAO;QACT,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QAC1C,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,CAAC;IACjD,CAAC;IAkBD;QACE,KAAK,EAAE,CAAC;QAvCV,yEAAyE;QACzE,uEAAuE;QACtD,WAAM,GAAG,SAAS,EAAE,MAAM,EAAE,CAAC;QAoB9C,QAAS,GAAG,KAAK,CAAC;QAElB;;;WAGG;QACwB,aAAQ,GAAG,KAAK,CAAC;QAE5C;;WAEG;QACS,UAAK,GAAG,IAAI,CAAC;QAGR,wBAAmB,GAAG,IAAI,yBAAyB,CAAC,IAAI,CAAC,CAAC;QAIzE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC;YAC/B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAEkB,MAAM;QACvB,MAAM,OAAO,GAAG,EAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC;QAC1C,OAAO,IAAI,CAAA;8BACe,QAAQ,CAAC,OAAO,CAAC;;;qBAG1B,IAAI;sBACH,IAAI,CAAC,QAAQ;oDACiB,IAAI;;sBAElC,IAAI,CAAC,MAAM;;;;;;;;;yBASR,IAAI,CAAC,MAAM;;;;;;KAM/B,CAAC;IACJ,CAAC;IAEkB,OAAO;QACxB,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACrD,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,KAAY;QACpC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QAED,2DAA2D;QAC3D,MAAM,CAAC,CAAC;QACR,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAC3B,OAAO;QACT,CAAC;QAED,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC;QAED,yDAAyD;QACzD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,aAAa,CAChB,IAAI,UAAU,CAAC,OAAO,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CACzD,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,KAAoB;QAC9C,2DAA2D;QAC3D,MAAM,CAAC,CAAC;QACR,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAChD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAMQ,OA/FR,OAAO,EA+FE,YAAY,EAAC;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1C,CAAC;IAEQ,CAAC,YAAY,CAAC;QACrB,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAEQ,iBAAiB;QACxB,0EAA0E;QAC1E,mDAAmD;QACnD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IAC9C,CAAC;IAEQ,wBAAwB,CAAC,KAAa;QAC7C,IAAI,CAAC,OAAO,GAAG,KAAK,KAAK,MAAM,CAAC;IAClC,CAAC;IAEQ,CAAC,eAAe,CAAC;QACxB,OAAO,IAAI,cAAc,CAAC,GAAG,EAAE;YAC7B,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC9B,sEAAsE;gBACtE,2DAA2D;gBAC3D,OAAO,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC;YAED,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAA+B,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC;IAEQ,CAAC,iBAAiB,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;CACF;AA9IC;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;oCAGzB;AAkB0B;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;uCAAkB;AAKhC;IAAX,QAAQ,EAAE;oCAAc;AAEa;IAArC,KAAK,CAAC,YAAY,CAAC;wCAA0C", "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, isServer, LitElement} from 'lit';\nimport {property, query} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {isActivationClick} from '../../internal/events/form-label-activation.js';\nimport {\n  createValidator,\n  getValidityAnchor,\n  mixinConstraintValidation,\n} from '../../labs/behaviors/constraint-validation.js';\nimport {\n  internals,\n  mixinElementInternals,\n} from '../../labs/behaviors/element-internals.js';\nimport {mixinFocusable} from '../../labs/behaviors/focusable.js';\nimport {\n  getFormState,\n  getFormValue,\n  mixinFormAssociated,\n} from '../../labs/behaviors/form-associated.js';\nimport {RadioValidator} from '../../labs/behaviors/validators/radio-validator.js';\n\nimport {SingleSelectionController} from './single-selection-controller.js';\n\nconst CHECKED = Symbol('checked');\nlet maskId = 0;\n\n// Separate variable needed for closure.\nconst radioBaseClass = mixinConstraintValidation(\n  mixinFormAssociated(mixinElementInternals(mixinFocusable(LitElement))),\n);\n\n/**\n * A radio component.\n *\n * @fires input {InputEvent} Dispatched when the value changes from user\n * interaction. --bubbles\n * @fires change {Event} Dispatched when the value changes from user\n * interaction. --bubbles --composed\n */\nexport class Radio extends radioBaseClass {\n  // Unique maskId is required because of a Safari bug that fail to persist\n  // reference to the mask. This should be removed once the bug is fixed.\n  private readonly maskId = `cutout${++maskId}`;\n\n  /**\n   * Whether or not the radio is selected.\n   */\n  @property({type: Boolean})\n  get checked() {\n    return this[CHECKED];\n  }\n  set checked(checked: boolean) {\n    const wasChecked = this.checked;\n    if (wasChecked === checked) {\n      return;\n    }\n\n    this[CHECKED] = checked;\n    this.requestUpdate('checked', wasChecked);\n    this.selectionController.handleCheckedChange();\n  }\n\n  [CHECKED] = false;\n\n  /**\n   * Whether or not the radio is required. If any radio is required in a group,\n   * all radios are implicitly required.\n   */\n  @property({type: Boolean}) required = false;\n\n  /**\n   * The element value to use in form submission when checked.\n   */\n  @property() value = 'on';\n\n  @query('.container') private readonly container!: HTMLElement;\n  private readonly selectionController = new SingleSelectionController(this);\n\n  constructor() {\n    super();\n    this.addController(this.selectionController);\n    if (!isServer) {\n      this[internals].role = 'radio';\n      this.addEventListener('click', this.handleClick.bind(this));\n      this.addEventListener('keydown', this.handleKeydown.bind(this));\n    }\n  }\n\n  protected override render() {\n    const classes = {'checked': this.checked};\n    return html`\n      <div class=\"container ${classMap(classes)}\" aria-hidden=\"true\">\n        <md-ripple\n          part=\"ripple\"\n          .control=${this}\n          ?disabled=${this.disabled}></md-ripple>\n        <md-focus-ring part=\"focus-ring\" .control=${this}></md-focus-ring>\n        <svg class=\"icon\" viewBox=\"0 0 20 20\">\n          <mask id=\"${this.maskId}\">\n            <rect width=\"100%\" height=\"100%\" fill=\"white\" />\n            <circle cx=\"10\" cy=\"10\" r=\"8\" fill=\"black\" />\n          </mask>\n          <circle\n            class=\"outer circle\"\n            cx=\"10\"\n            cy=\"10\"\n            r=\"10\"\n            mask=\"url(#${this.maskId})\" />\n          <circle class=\"inner circle\" cx=\"10\" cy=\"10\" r=\"5\" />\n        </svg>\n\n        <div class=\"touch-target\"></div>\n      </div>\n    `;\n  }\n\n  protected override updated() {\n    this[internals].ariaChecked = String(this.checked);\n  }\n\n  private async handleClick(event: Event) {\n    if (this.disabled) {\n      return;\n    }\n\n    // allow event to propagate to user code after a microtask.\n    await 0;\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    if (isActivationClick(event)) {\n      this.focus();\n    }\n\n    // Per spec, clicking on a radio input always selects it.\n    this.checked = true;\n    this.dispatchEvent(new Event('change', {bubbles: true}));\n    this.dispatchEvent(\n      new InputEvent('input', {bubbles: true, composed: true}),\n    );\n  }\n\n  private async handleKeydown(event: KeyboardEvent) {\n    // allow event to propagate to user code after a microtask.\n    await 0;\n    if (event.key !== ' ' || event.defaultPrevented) {\n      return;\n    }\n\n    this.click();\n  }\n\n  // Writable mixin properties for lit-html binding, needed for lit-analyzer\n  declare disabled: boolean;\n  declare name: string;\n\n  override [getFormValue]() {\n    return this.checked ? this.value : null;\n  }\n\n  override [getFormState]() {\n    return String(this.checked);\n  }\n\n  override formResetCallback() {\n    // The checked property does not reflect, so the original attribute set by\n    // the user is used to determine the default value.\n    this.checked = this.hasAttribute('checked');\n  }\n\n  override formStateRestoreCallback(state: string) {\n    this.checked = state === 'true';\n  }\n\n  override [createValidator]() {\n    return new RadioValidator(() => {\n      if (!this.selectionController) {\n        // Validation runs on superclass construction, so selection controller\n        // might not actually be ready until this class constructs.\n        return [this];\n      }\n\n      return this.selectionController.controls as [Radio, ...Radio[]];\n    });\n  }\n\n  override [getValidityAnchor]() {\n    return this.container;\n  }\n}\n"]}