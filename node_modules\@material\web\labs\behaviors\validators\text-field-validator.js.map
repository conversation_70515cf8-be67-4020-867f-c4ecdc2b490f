{"version": 3, "file": "text-field-validator.js", "sourceRoot": "", "sources": ["text-field-validator.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,SAAS,EAAC,MAAM,gBAAgB,CAAC;AAoGzC;;;GAGG;AACH,MAAM,OAAO,kBAAmB,SAAQ,SAAyB;IAI5C,eAAe,CAAC,EAAC,KAAK,EAAE,eAAe,EAAiB;QACzE,IAAI,eAAe,GAAG,eAAe,CAAC;QACtC,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC5C,mCAAmC;YACnC,eAAe,GAAG,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACvE,4CAA4C;YAC5C,IAAI,CAAC,YAAY,GAAG,eAAe,CAAC;QACtC,CAAC;aAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAC5B,sCAAsC;YACtC,eAAe;gBACb,IAAI,CAAC,eAAe,IAAI,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAC7D,+CAA+C;YAC/C,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACzC,CAAC;QAED,sEAAsE;QACtE,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;YAC/B,CAAC,CAAE,eAAoC;YACvC,CAAC,CAAC,IAAI,CAAC;QAET,uEAAuE;QACvE,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QAC1B,CAAC;QAED,IAAI,eAAe,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC;YAC1C,0EAA0E;YAC1E,yEAAyE;YACzE,0EAA0E;YAC1E,+DAA+D;YAC/D,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACtC,CAAC;QAED,eAAe,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QAE1C,0EAA0E;QAC1E,qEAAqE;QACrE,4EAA4E;QAC5E,cAAc;QACd,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,UAAU,GAAG,KAAmB,CAAC;YACvC,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gBACvB,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YACnC,CAAC;YAED,IAAI,UAAU,CAAC,GAAG,EAAE,CAAC;gBACnB,KAAK,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;YAC7B,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC;YAED,IAAI,UAAU,CAAC,GAAG,EAAE,CAAC;gBACnB,KAAK,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;YAC7B,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC;YAED,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;YAC/B,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QAED,oEAAoE;QACpE,wEAAwE;QACxE,yBAAyB;QACzB,EAAE;QACF,oEAAoE;QACpE,0EAA0E;QAC1E,mBAAmB;QACnB,EAAE;QACF,uEAAuE;QACvE,kEAAkE;QAClE,gEAAgE;QAChE,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YACjC,eAAe,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;QACrE,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YACjC,eAAe,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;QACrE,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,eAAe,CAAC,QAAQ;YAClC,iBAAiB,EAAE,eAAe,CAAC,iBAAiB;SACrD,CAAC;IACJ,CAAC;IAEkB,MAAM,CACvB,EAAC,KAAK,EAAE,IAAI,EAAiB,EAC7B,EAAC,KAAK,EAAE,IAAI,EAAiB;QAE7B,6CAA6C;QAC7C,MAAM,oBAAoB,GACxB,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;YACvB,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK;YACzB,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ;YAC/B,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS;YACjC,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC;QAEpC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/C,yDAAyD;YACzD,OAAO,oBAAoB,CAAC;QAC9B,CAAC;QAED,8CAA8C;QAC9C,OAAO,CACL,oBAAoB;YACpB,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO;YAC7B,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG;YACrB,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG;YACrB,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CACxB,CAAC;IACJ,CAAC;IAEkB,IAAI,CAAC,EAAC,KAAK,EAAiB;QAC7C,uEAAuE;QACvE,mDAAmD;QACnD,OAAO;YACL,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC;gBACxB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;gBACvB,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;YAC5B,eAAe,EAAE,IAAI;SACtB,CAAC;IACJ,CAAC;IAEO,SAAS,CAAC,KAAiB;QACjC,MAAM,EAAC,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAC,GAAG,KAAK,CAAC;QAC9C,OAAO;YACL,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;YAC9B,IAAI;YACJ,OAAO;YACP,GAAG;YACH,GAAG;YACH,IAAI;SACL,CAAC;IACJ,CAAC;IAEO,YAAY,CAAC,KAAoB;QACvC,OAAO;YACL,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;YAC9B,IAAI,EAAE,KAAK,CAAC,IAAI;SACjB,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,EACtB,KAAK,EACL,QAAQ,EACR,SAAS,EACT,SAAS,GACmB;QAC5B,OAAO,EAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAC,CAAC;IACjD,CAAC;CACF;AAED,SAAS,YAAY,CAAC,KAAiC;IACrD,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC;AACnC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {Validator} from './validator.js';\n\n/**\n * Constraint validation for a text field.\n */\nexport interface TextFieldState {\n  /**\n   * The input or textarea state to validate.\n   */\n  state: InputState | TextAreaState;\n\n  /**\n   * The `<input>` or `<textarea>` that is rendered on the page.\n   *\n   * `minlength` and `maxlength` validation do not apply until a user has\n   * interacted with the control and the element is internally marked as dirty.\n   * This is a spec quirk, the two properties behave differently from other\n   * constraint validation.\n   *\n   * This means we need an actual rendered element instead of a virtual one,\n   * since the virtual element will never be marked as dirty.\n   *\n   * This can be `null` if the element has not yet rendered, and the validator\n   * will fall back to virtual elements for other constraint validation\n   * properties, which do apply even if the control is not dirty.\n   */\n  renderedControl: HTMLInputElement | HTMLTextAreaElement | null;\n}\n\n/**\n * Constraint validation properties for an `<input>`.\n */\nexport interface InputState extends SharedInputAndTextAreaState {\n  /**\n   * The `<input>` type.\n   *\n   * Not all constraint validation properties apply to every type. See\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Constraint_validation#validation-related_attributes\n   * for which properties will apply to which types.\n   */\n  readonly type: string;\n\n  /**\n   * The regex pattern a value must match.\n   */\n  readonly pattern: string;\n\n  /**\n   * The minimum value.\n   */\n  readonly min: string;\n\n  /**\n   * The maximum value.\n   */\n  readonly max: string;\n\n  /**\n   * The step interval of the value.\n   */\n  readonly step: string;\n}\n\n/**\n * Constraint validation properties for a `<textarea>`.\n */\nexport interface TextAreaState extends SharedInputAndTextAreaState {\n  /**\n   * The type, must be \"textarea\" to inform the validator to use `<textarea>`\n   * instead of `<input>`.\n   */\n  readonly type: 'textarea';\n}\n\n/**\n * Constraint validation properties shared between an `<input>` and\n * `<textarea>`.\n */\ninterface SharedInputAndTextAreaState {\n  /**\n   * The current value.\n   */\n  readonly value: string;\n\n  /**\n   * Whether the textarea is required.\n   */\n  readonly required: boolean;\n\n  /**\n   * The minimum length of the value.\n   */\n  readonly minLength: number;\n\n  /**\n   * The maximum length of the value.\n   */\n  readonly maxLength: number;\n}\n\n/**\n * A validator that provides constraint validation that emulates `<input>` and\n * `<textarea>` validation.\n */\nexport class TextFieldValidator extends Validator<TextFieldState> {\n  private inputControl?: HTMLInputElement;\n  private textAreaControl?: HTMLTextAreaElement;\n\n  protected override computeValidity({state, renderedControl}: TextFieldState) {\n    let inputOrTextArea = renderedControl;\n    if (isInputState(state) && !inputOrTextArea) {\n      // Get cached <input> or create it.\n      inputOrTextArea = this.inputControl || document.createElement('input');\n      // Cache the <input> to re-use it next time.\n      this.inputControl = inputOrTextArea;\n    } else if (!inputOrTextArea) {\n      // Get cached <textarea> or create it.\n      inputOrTextArea =\n        this.textAreaControl || document.createElement('textarea');\n      // Cache the <textarea> to re-use it next time.\n      this.textAreaControl = inputOrTextArea;\n    }\n\n    // Set this variable so we can check it for input-specific properties.\n    const input = isInputState(state)\n      ? (inputOrTextArea as HTMLInputElement)\n      : null;\n\n    // Set input's \"type\" first, since this can change the other properties\n    if (input) {\n      input.type = state.type;\n    }\n\n    if (inputOrTextArea.value !== state.value) {\n      // Only programmatically set the value if there's a difference. When using\n      // the rendered control, the value will always be up to date. Setting the\n      // property (even if it's the same string) will reset the internal <input>\n      // dirty flag, making minlength and maxlength validation reset.\n      inputOrTextArea.value = state.value;\n    }\n\n    inputOrTextArea.required = state.required;\n\n    // The following IDLAttribute properties will always hydrate an attribute,\n    // even if set to a the default value ('' or -1). The presence of the\n    // attribute triggers constraint validation, so we must remove the attribute\n    // when empty.\n    if (input) {\n      const inputState = state as InputState;\n      if (inputState.pattern) {\n        input.pattern = inputState.pattern;\n      } else {\n        input.removeAttribute('pattern');\n      }\n\n      if (inputState.min) {\n        input.min = inputState.min;\n      } else {\n        input.removeAttribute('min');\n      }\n\n      if (inputState.max) {\n        input.max = inputState.max;\n      } else {\n        input.removeAttribute('max');\n      }\n\n      if (inputState.step) {\n        input.step = inputState.step;\n      } else {\n        input.removeAttribute('step');\n      }\n    }\n\n    // Use -1 to represent no minlength and maxlength, which is what the\n    // platform input returns. However, it will throw an error if you try to\n    // manually set it to -1.\n    //\n    // While the type is `number`, it may actually be `null` at runtime.\n    // `null > -1` is true since `null` coerces to `0`, so we default null and\n    // undefined to -1.\n    //\n    // We set attributes instead of properties since setting a property may\n    // throw an out of bounds error in relation to the other property.\n    // Attributes will not throw errors while the state is updating.\n    if ((state.minLength ?? -1) > -1) {\n      inputOrTextArea.setAttribute('minlength', String(state.minLength));\n    } else {\n      inputOrTextArea.removeAttribute('minlength');\n    }\n\n    if ((state.maxLength ?? -1) > -1) {\n      inputOrTextArea.setAttribute('maxlength', String(state.maxLength));\n    } else {\n      inputOrTextArea.removeAttribute('maxlength');\n    }\n\n    return {\n      validity: inputOrTextArea.validity,\n      validationMessage: inputOrTextArea.validationMessage,\n    };\n  }\n\n  protected override equals(\n    {state: prev}: TextFieldState,\n    {state: next}: TextFieldState,\n  ) {\n    // Check shared input and textarea properties\n    const inputOrTextAreaEqual =\n      prev.type === next.type &&\n      prev.value === next.value &&\n      prev.required === next.required &&\n      prev.minLength === next.minLength &&\n      prev.maxLength === next.maxLength;\n\n    if (!isInputState(prev) || !isInputState(next)) {\n      // Both are textareas, all relevant properties are equal.\n      return inputOrTextAreaEqual;\n    }\n\n    // Check additional input-specific properties.\n    return (\n      inputOrTextAreaEqual &&\n      prev.pattern === next.pattern &&\n      prev.min === next.min &&\n      prev.max === next.max &&\n      prev.step === next.step\n    );\n  }\n\n  protected override copy({state}: TextFieldState): TextFieldState {\n    // Don't hold a reference to the rendered control when copying since we\n    // don't use it when checking if the state changed.\n    return {\n      state: isInputState(state)\n        ? this.copyInput(state)\n        : this.copyTextArea(state),\n      renderedControl: null,\n    };\n  }\n\n  private copyInput(state: InputState): InputState {\n    const {type, pattern, min, max, step} = state;\n    return {\n      ...this.copySharedState(state),\n      type,\n      pattern,\n      min,\n      max,\n      step,\n    };\n  }\n\n  private copyTextArea(state: TextAreaState): TextAreaState {\n    return {\n      ...this.copySharedState(state),\n      type: state.type,\n    };\n  }\n\n  private copySharedState({\n    value,\n    required,\n    minLength,\n    maxLength,\n  }: SharedInputAndTextAreaState): SharedInputAndTextAreaState {\n    return {value, required, minLength, maxLength};\n  }\n}\n\nfunction isInputState(state: InputState | TextAreaState): state is InputState {\n  return state.type !== 'textarea';\n}\n"]}