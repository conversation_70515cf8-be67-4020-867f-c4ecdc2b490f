//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:list';
// go/keep-sorted end
// go/keep-sorted start
@use '../../../tokens';
// go/keep-sorted end

@mixin theme($tokens) {
  $supported-tokens: tokens.$md-comp-outlined-card-supported-tokens;

  @each $token, $value in $tokens {
    @if list.index($supported-tokens, $token) == null {
      @error 'Outlined card `#{$token}` is not a supported token.';
    }

    @if $value {
      --md-outlined-card-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $tokens: tokens.md-comp-outlined-card-values();

  :host {
    @each $token, $value in $tokens {
      --_#{$token}: #{$value};
    }
  }

  .outline {
    border-color: var(--_outline-color);
    border-width: var(--_outline-width);
  }
}
