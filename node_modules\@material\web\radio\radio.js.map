{"version": 3, "file": "radio.js", "sourceRoot": "", "sources": ["radio.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,KAAK,EAAC,MAAM,qBAAqB,CAAC;AAC1C,OAAO,EAAC,MAAM,EAAC,MAAM,4BAA4B,CAAC;AAQlD;;;;;;;;;;;;;;;GAeG;AAEI,IAAM,OAAO,GAAb,MAAM,OAAQ,SAAQ,KAAK;;AAChB,cAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAD5C,OAAO;IADnB,aAAa,CAAC,UAAU,CAAC;GACb,OAAO,CAEnB", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Radio} from './internal/radio.js';\nimport {styles} from './internal/radio-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-radio': MdRadio;\n  }\n}\n\n/**\n * @summary Radio buttons allow users to select one option from a set.\n *\n * @description\n * Radio buttons are the recommended way to allow users to make a single\n * selection from a list of options.\n *\n * Only one radio button can be selected at a time.\n *\n * Use radio buttons to:\n * - Select a single option from a set\n * - Expose all available options\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-radio')\nexport class MdRadio extends Radio {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}