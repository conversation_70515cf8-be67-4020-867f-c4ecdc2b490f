{"version": 3, "file": "tab.js", "sourceRoot": "", "sources": ["tab.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;AAEH,OAAO,8BAA8B,CAAC;AACtC,OAAO,8BAA8B,CAAC;AACtC,OAAO,wBAAwB,CAAC;AAEhC,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAC,MAAM,KAAK,CAAC;AACxD,OAAO,EACL,QAAQ,EACR,KAAK,EACL,qBAAqB,EACrB,kBAAkB,EAClB,KAAK,GACN,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAY,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAEhE,OAAO,EAAC,MAAM,EAAC,MAAM,oCAAoC,CAAC;AAC1D,OAAO,EAAC,cAAc,EAAC,MAAM,mCAAmC,CAAC;AAEjE;;;GAGG;AACH,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;AAEtC;;;GAGG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAE5D,wCAAwC;AACxC,MAAM,YAAY,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC;AAEhD;;GAEG;AACH,MAAM,OAAO,GAAI,SAAQ,YAAY;IAcnC;;OAEG;IAEH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IACD,IAAI,QAAQ,CAAC,MAAe;QAC1B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAsBD;QACE,KAAK,EAAE,CAAC;QA7CV;;;;WAIG;QAEM,UAAK,GAAG,IAAI,CAAC;QAEtB;;YAEI;QACsC,WAAM,GAAG,KAAK,CAAC;QAazD;;WAEG;QAC+C,YAAO,GAAG,KAAK,CAAC;QAElE;;WAEG;QACgD,aAAQ,GAAG,KAAK,CAAC;QAGjD,uBAAkB,GAAG,KAAK,CAAC;QAK7B,cAAS;QACxB,0BAA0B;QACzB,IAAoB,CAAC,eAAe,EAAE,CAAC;QAIxC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAEkB,MAAM;QACvB,MAAM,SAAS,GAAG,IAAI,CAAA,+BAA+B,CAAC;QACtD,OAAO,IAAI,CAAA;;;eAGA,IAAI,CAAC,kBAAkB;yDACmB,IAAI;;4BAEjC,IAAI;;yBAEP,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;;wCAEnB,IAAI,CAAC,oBAAoB;4BACrC,IAAI,CAAC,gBAAgB;UACvC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;;QAE/C,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO;WAC1C,CAAC;IACV,CAAC;IAES,iBAAiB;QACzB,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,OAAO;YACxB,WAAW,EAAE,CAAC,IAAI,CAAC,QAAQ;SAC5B,CAAC;IACJ,CAAC;IAEkB,OAAO;QACxB,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACpD,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,KAAoB;QAC9C,yBAAyB;QACzB,MAAM,CAAC,CAAC;QACR,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAC3B,OAAO;QACT,CAAC;QAED,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;YAC/C,qEAAqE;YACrE,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,KAAY;QACrC,4EAA4E;QAC5E,0DAA0D;QAC1D,KAAK,CAAC,eAAe,EAAE,CAAC;QACxB,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAED,OAtE+B,SAAS,EAsEvC,iBAAiB,EAAC,CAAC,WAAgB;QAClC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAC5C,CAAC,CAAC,MAAM,EAAE,CAAC;QACb,CAAC,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAC9C,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE;gBAC9B,QAAQ,EAAE,GAAG;gBACb,MAAM,EAAE,MAAM,CAAC,UAAU;aAC1B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,WAAgB;QACnC,MAAM,YAAY,GAAG,kBAAkB,EAAE,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC,EAAC,SAAS,EAAE,CAAC,EAAC,EAAE,EAAC,WAAW,EAAE,MAAM,EAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACvE,CAAC;QAED,MAAM,IAAI,GAAa,EAAE,CAAC;QAC1B,MAAM,QAAQ,GACZ,WAAW,CAAC,SAAS,CAAC,EAAE,qBAAqB,EAAE,IAAK,EAAc,CAAC;QACrE,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC;QAC9B,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAE,CAAC,qBAAqB,EAAE,CAAC;QACxD,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;QAC1B,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC;QAC9B,MAAM,KAAK,GAAG,UAAU,GAAG,QAAQ,CAAC;QACpC,IACE,CAAC,YAAY;YACb,OAAO,KAAK,SAAS;YACrB,KAAK,KAAK,SAAS;YACnB,CAAC,KAAK,CAAC,KAAK,CAAC,EACb,CAAC;YACD,IAAI,CAAC,WAAW,CAAC,GAAG,cAAc,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,OAAO,CACzD,CAAC,CACF,cAAc,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QACD,kEAAkE;QAClE,+BAA+B;QAC/B,OAAO,CAAC,IAAI,EAAE,EAAC,WAAW,EAAE,MAAM,EAAC,CAAC,CAAC;IACvC,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,0EAA0E;QAC1E,WAAW;QACX,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC7C,MAAM,cAAc,GAClB,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS;gBAChC,CAAC,CAAE,IAAa,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzC,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,IAAI,cAAc,EAAE,CAAC;gBAC1D,OAAO;YACT,CAAC;QACH,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAEO,oBAAoB;QAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/C,CAAC;CACF;AAtKU;IADR,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAC,CAAC;kCACxC;AAKoB;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;mCAAgB;AAMzD;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;mCAGzB;AAQiD;IAAjD,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAC,CAAC;oCAAiB;AAKf;IAAlD,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAC,CAAC;qCAAkB;AAEtC;IAA7B,KAAK,CAAC,YAAY,CAAC;6BAA2C;AAC5C;IAAlB,KAAK,EAAE;+CAAsC;AAE7B;IADhB,kBAAkB,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;iDACW;AAE9B;IADhB,qBAAqB,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;0CACN;AAuIjD,SAAS,kBAAkB;IACzB,OAAO,MAAM,CAAC,UAAU,CAAC,kCAAkC,CAAC,CAAC,OAAO,CAAC;AACvE,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../elevation/elevation.js';\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, isServer, LitElement, nothing} from 'lit';\nimport {\n  property,\n  query,\n  queryAssignedElements,\n  queryAssignedNodes,\n  state,\n} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\n\nimport {EASING} from '../../internal/motion/animation.js';\nimport {mixinFocusable} from '../../labs/behaviors/focusable.js';\n\n/**\n * Symbol for tabs to use to animate their indicators based off another tab's\n * indicator.\n */\nconst INDICATOR = Symbol('indicator');\n\n/**\n * Symbol used by the tab bar to request a tab to animate its indicator from a\n * previously selected tab.\n */\nexport const ANIMATE_INDICATOR = Symbol('animateIndicator');\n\n// Separate variable needed for closure.\nconst tabBaseClass = mixinFocusable(LitElement);\n\n/**\n * Tab component.\n */\nexport class Tab extends tabBaseClass {\n  /**\n   * The attribute `md-tab` indicates that the element is a tab for the parent\n   * element, `<md-tabs>`. Make sure if you're implementing your own `md-tab`\n   * component that you have an `md-tab` attribute set.\n   */\n  @property({type: Boolean, reflect: true, attribute: 'md-tab'})\n  readonly isTab = true;\n\n  /**\n   * Whether or not the tab is selected.\n   **/\n  @property({type: Boolean, reflect: true}) active = false;\n\n  /**\n   * @deprecated use `active`\n   */\n  @property({type: Boolean})\n  get selected() {\n    return this.active;\n  }\n  set selected(active: boolean) {\n    this.active = active;\n  }\n\n  /**\n   * In SSR, set this to true when an icon is present.\n   */\n  @property({type: Boolean, attribute: 'has-icon'}) hasIcon = false;\n\n  /**\n   * In SSR, set this to true when there is no label and only an icon.\n   */\n  @property({type: Boolean, attribute: 'icon-only'}) iconOnly = false;\n\n  @query('.indicator') readonly [INDICATOR]!: HTMLElement | null;\n  @state() protected fullWidthIndicator = false;\n  @queryAssignedNodes({flatten: true})\n  private readonly assignedDefaultNodes!: Node[];\n  @queryAssignedElements({slot: 'icon', flatten: true})\n  private readonly assignedIcons!: HTMLElement[];\n  private readonly internals =\n    // Cast needed for closure\n    (this as HTMLElement).attachInternals();\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.internals.role = 'tab';\n      this.addEventListener('keydown', this.handleKeydown.bind(this));\n    }\n  }\n\n  protected override render() {\n    const indicator = html`<div class=\"indicator\"></div>`;\n    return html`<div\n      class=\"button\"\n      role=\"presentation\"\n      @click=${this.handleContentClick}>\n      <md-focus-ring part=\"focus-ring\" inward .control=${this}></md-focus-ring>\n      <md-elevation part=\"elevation\"></md-elevation>\n      <md-ripple .control=${this}></md-ripple>\n      <div\n        class=\"content ${classMap(this.getContentClasses())}\"\n        role=\"presentation\">\n        <slot name=\"icon\" @slotchange=${this.handleIconSlotChange}></slot>\n        <slot @slotchange=${this.handleSlotChange}></slot>\n        ${this.fullWidthIndicator ? nothing : indicator}\n      </div>\n      ${this.fullWidthIndicator ? indicator : nothing}\n    </div>`;\n  }\n\n  protected getContentClasses(): ClassInfo {\n    return {\n      'has-icon': this.hasIcon,\n      'has-label': !this.iconOnly,\n    };\n  }\n\n  protected override updated() {\n    this.internals.ariaSelected = String(this.active);\n  }\n\n  private async handleKeydown(event: KeyboardEvent) {\n    // Allow event to bubble.\n    await 0;\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    if (event.key === 'Enter' || event.key === ' ') {\n      // Prevent default behavior such as scrolling when pressing spacebar.\n      event.preventDefault();\n      this.click();\n    }\n  }\n\n  private handleContentClick(event: Event) {\n    // Ensure the \"click\" target is always the tab, and not content, by stopping\n    // propagation of content clicks and re-clicking the host.\n    event.stopPropagation();\n    this.click();\n  }\n\n  [ANIMATE_INDICATOR](previousTab: Tab) {\n    if (!this[INDICATOR]) {\n      return;\n    }\n\n    this[INDICATOR].getAnimations().forEach((a) => {\n      a.cancel();\n    });\n    const frames = this.getKeyframes(previousTab);\n    if (frames !== null) {\n      this[INDICATOR].animate(frames, {\n        duration: 250,\n        easing: EASING.EMPHASIZED,\n      });\n    }\n  }\n\n  private getKeyframes(previousTab: Tab) {\n    const reduceMotion = shouldReduceMotion();\n    if (!this.active) {\n      return reduceMotion ? [{'opacity': 1}, {'transform': 'none'}] : null;\n    }\n\n    const from: Keyframe = {};\n    const fromRect =\n      previousTab[INDICATOR]?.getBoundingClientRect() ?? ({} as DOMRect);\n    const fromPos = fromRect.left;\n    const fromExtent = fromRect.width;\n    const toRect = this[INDICATOR]!.getBoundingClientRect();\n    const toPos = toRect.left;\n    const toExtent = toRect.width;\n    const scale = fromExtent / toExtent;\n    if (\n      !reduceMotion &&\n      fromPos !== undefined &&\n      toPos !== undefined &&\n      !isNaN(scale)\n    ) {\n      from['transform'] = `translateX(${(fromPos - toPos).toFixed(\n        4,\n      )}px) scaleX(${scale.toFixed(4)})`;\n    } else {\n      from['opacity'] = 0;\n    }\n    // note, including `transform: none` avoids quirky Safari behavior\n    // that can hide the animation.\n    return [from, {'transform': 'none'}];\n  }\n\n  private handleSlotChange() {\n    this.iconOnly = false;\n    // Check if there's any label text or elements. If not, then there is only\n    // an icon.\n    for (const node of this.assignedDefaultNodes) {\n      const hasTextContent =\n        node.nodeType === Node.TEXT_NODE &&\n        !!(node as Text).wholeText.match(/\\S/);\n      if (node.nodeType === Node.ELEMENT_NODE || hasTextContent) {\n        return;\n      }\n    }\n\n    this.iconOnly = true;\n  }\n\n  private handleIconSlotChange() {\n    this.hasIcon = this.assignedIcons.length > 0;\n  }\n}\n\nfunction shouldReduceMotion() {\n  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;\n}\n"]}