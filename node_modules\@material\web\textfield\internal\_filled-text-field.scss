//
// Copyright 2021 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:list';
@use 'sass:map';
@use 'sass:meta';
// go/keep-sorted end
// go/keep-sorted start
@use '../../field/filled-field';
@use '../../tokens';
// go/keep-sorted end

@mixin theme($tokens) {
  $supported-tokens: tokens.$md-comp-filled-text-field-supported-tokens;
  @each $token, $value in $tokens {
    @if list.index($supported-tokens, $token) == null {
      @error 'Token `#{$token}` is not a supported token.';
    }

    @if $token == 'container-shape' and meta.type-of($value) == 'list' {
      @error 'Filled text field `container-shape` may not be specified as a list. Use logical (`*-start-start`) tokens instead.';
    }

    @if $value {
      --md-filled-text-field-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $tokens: tokens.md-comp-filled-text-field-values();

  :host {
    // Only use the logical properties.
    $tokens: map.remove($tokens, 'container-shape');
    @each $token, $value in $tokens {
      --_#{$token}: #{$value};
    }

    @include filled-field.theme(
      (
        // go/keep-sorted start
        'active-indicator-color': var(--_active-indicator-color),
        'active-indicator-height': var(--_active-indicator-height),
        'bottom-space': var(--_bottom-space),
        'container-color': var(--_container-color),
        'container-shape-end-end': var(--_container-shape-end-end),
        'container-shape-end-start': var(--_container-shape-end-start),
        'container-shape-start-end': var(--_container-shape-start-end),
        'container-shape-start-start': var(--_container-shape-start-start),
        'content-color': var(--_input-text-color),
        'content-font': var(--_input-text-font),
        'content-line-height': var(--_input-text-line-height),
        'content-size': var(--_input-text-size),
        'content-space': var(--_icon-input-space),
        'content-weight': var(--_input-text-weight),
        'disabled-active-indicator-color':
          var(--_disabled-active-indicator-color),
        'disabled-active-indicator-height':
          var(--_disabled-active-indicator-height),
        'disabled-active-indicator-opacity':
          var(--_disabled-active-indicator-opacity),
        'disabled-container-color': var(--_disabled-container-color),
        'disabled-container-opacity': var(--_disabled-container-opacity),
        'disabled-content-color': var(--_disabled-input-text-color),
        'disabled-content-opacity': var(--_disabled-input-text-opacity),
        'disabled-label-text-color': var(--_disabled-label-text-color),
        'disabled-label-text-opacity': var(--_disabled-label-text-opacity),
        'disabled-leading-content-color': var(--_disabled-leading-icon-color),
        'disabled-leading-content-opacity':
          var(--_disabled-leading-icon-opacity),
        'disabled-supporting-text-color': var(--_disabled-supporting-text-color),
        'disabled-supporting-text-opacity':
          var(--_disabled-supporting-text-opacity),
        'disabled-trailing-content-color': var(--_disabled-trailing-icon-color),
        'disabled-trailing-content-opacity':
          var(--_disabled-trailing-icon-opacity),
        'error-active-indicator-color': var(--_error-active-indicator-color),
        'error-content-color': var(--_error-input-text-color),
        'error-focus-active-indicator-color':
          var(--_error-focus-active-indicator-color),
        'error-focus-content-color': var(--_error-focus-input-text-color),
        'error-focus-label-text-color': var(--_error-focus-label-text-color),
        'error-focus-leading-content-color':
          var(--_error-focus-leading-icon-color),
        'error-focus-supporting-text-color':
          var(--_error-focus-supporting-text-color),
        'error-focus-trailing-content-color':
          var(--_error-focus-trailing-icon-color),
        'error-hover-active-indicator-color':
          var(--_error-hover-active-indicator-color),
        'error-hover-content-color': var(--_error-hover-input-text-color),
        'error-hover-label-text-color': var(--_error-hover-label-text-color),
        'error-hover-leading-content-color':
          var(--_error-hover-leading-icon-color),
        'error-hover-state-layer-color': var(--_error-hover-state-layer-color),
        'error-hover-state-layer-opacity':
          var(--_error-hover-state-layer-opacity),
        'error-hover-supporting-text-color':
          var(--_error-hover-supporting-text-color),
        'error-hover-trailing-content-color':
          var(--_error-hover-trailing-icon-color),
        'error-label-text-color': var(--_error-label-text-color),
        'error-leading-content-color': var(--_error-leading-icon-color),
        'error-supporting-text-color': var(--_error-supporting-text-color),
        'error-trailing-content-color': var(--_error-trailing-icon-color),
        'focus-active-indicator-color': var(--_focus-active-indicator-color),
        'focus-active-indicator-height': var(--_focus-active-indicator-height),
        'focus-content-color': var(--_focus-input-text-color),
        'focus-label-text-color': var(--_focus-label-text-color),
        'focus-leading-content-color': var(--_focus-leading-icon-color),
        'focus-supporting-text-color': var(--_focus-supporting-text-color),
        'focus-trailing-content-color': var(--_focus-trailing-icon-color),
        'hover-active-indicator-color': var(--_hover-active-indicator-color),
        'hover-active-indicator-height': var(--_hover-active-indicator-height),
        'hover-content-color': var(--_hover-input-text-color),
        'hover-label-text-color': var(--_hover-label-text-color),
        'hover-leading-content-color': var(--_hover-leading-icon-color),
        'hover-state-layer-color': var(--_hover-state-layer-color),
        'hover-state-layer-opacity': var(--_hover-state-layer-opacity),
        'hover-supporting-text-color': var(--_hover-supporting-text-color),
        'hover-trailing-content-color': var(--_hover-trailing-icon-color),
        'label-text-color': var(--_label-text-color),
        'label-text-font': var(--_label-text-font),
        'label-text-line-height': var(--_label-text-line-height),
        'label-text-populated-line-height':
          var(--_label-text-populated-line-height),
        'label-text-populated-size': var(--_label-text-populated-size),
        'label-text-size': var(--_label-text-size),
        'label-text-weight': var(--_label-text-weight),
        'leading-content-color': var(--_leading-icon-color),
        'leading-space': var(--_leading-space),
        'supporting-text-color': var(--_supporting-text-color),
        'supporting-text-font': var(--_supporting-text-font),
        'supporting-text-line-height': var(--_supporting-text-line-height),
        'supporting-text-size': var(--_supporting-text-size),
        'supporting-text-weight': var(--_supporting-text-weight),
        'top-space': var(--_top-space),
        'trailing-content-color': var(--_trailing-icon-color),
        'trailing-space': var(--_trailing-space),
        'with-label-bottom-space': var(--_with-label-bottom-space),
        'with-label-top-space': var(--_with-label-top-space),
        'with-leading-content-leading-space':
          var(--_with-leading-icon-leading-space),
        'with-trailing-content-trailing-space':
          var(--_with-trailing-icon-trailing-space),
        // go/keep-sorted end
      )
    );
  }
}
