{"version": 3, "file": "list-item.js", "sourceRoot": "", "sources": ["list-item.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,UAAU,IAAI,QAAQ,EAAC,MAAM,kCAAkC,CAAC;AACxE,OAAO,EAAC,MAAM,EAAC,MAAM,yCAAyC,CAAC;AAU/D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCG;AAEI,IAAM,UAAU,GAAhB,MAAM,UAAW,SAAQ,QAAQ;;AACtB,iBAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAD5C,UAAU;IADtB,aAAa,CAAC,cAAc,CAAC;GACjB,UAAU,CAEtB", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {ListItemEl as ListItem} from './internal/listitem/list-item.js';\nimport {styles} from './internal/listitem/list-item-styles.js';\n\nexport {type ListItemType} from './internal/listitem/list-item.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-list-item': MdListItem;\n  }\n}\n\n/**\n * @summary\n * Lists are continuous, vertical indexes of text or images. Items are placed\n * inside the list.\n *\n * @description\n * Lists consist of one or more list items, and can contain actions represented\n * by icons and text. List items come in three sizes: one-line, two-line, and\n * three-line.\n *\n * __Takeaways:__\n *\n * - Lists should be sorted in logical ways that make content easy to scan, such\n *   as alphabetical, numerical, chronological, or by user preference.\n * - Lists present content in a way that makes it easy to identify a specific\n *   item in a collection and act on it.\n * - Lists should present icons, text, and actions in a consistent format.\n *\n * Acceptable slot child variants are:\n *\n * - `img[slot=end]`\n * - `img[slot=start]`\n *\n *  @example\n * ```html\n * <md-list-item\n *     headline=\"User Name\"\n *     supportingText=\"<EMAIL>\">\n *   <md-icon slot=\"start\">account_circle</md-icon>\n *   <md-icon slot=\"end\">check</md-icon>\n * </md-list-item>\n * ```\n *\n * @example\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-list-item')\nexport class MdListItem extends ListItem {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}