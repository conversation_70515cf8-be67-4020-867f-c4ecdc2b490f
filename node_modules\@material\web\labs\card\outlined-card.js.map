{"version": 3, "file": "outlined-card.js", "sourceRoot": "", "sources": ["outlined-card.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,IAAI,EAAC,MAAM,oBAAoB,CAAC;AACxC,OAAO,EAAC,MAAM,IAAI,cAAc,EAAC,MAAM,+BAA+B,CAAC;AACvE,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAQnE;;;GAGG;AAEI,IAAM,cAAc,GAApB,MAAM,cAAe,SAAQ,IAAI;;AACtB,qBAAM,GAAwB,CAAC,YAAY,EAAE,cAAc,CAAC,AAAtD,CAAuD;AADlE,cAAc;IAD1B,aAAa,CAAC,kBAAkB,CAAC;GACrB,cAAc,CAE1B", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Card} from './internal/card.js';\nimport {styles as outlinedStyles} from './internal/outlined-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-outlined-card': MdOutlinedCard;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-outlined-card')\nexport class MdOutlinedCard extends Card {\n  static override styles: CSSResultOrNative[] = [sharedStyles, outlinedStyles];\n}\n"]}