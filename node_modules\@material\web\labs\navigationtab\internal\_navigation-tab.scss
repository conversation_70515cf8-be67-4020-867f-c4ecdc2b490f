//
// Copyright 2022 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// stylelint-disable selector-class-pattern --
// Selector '.md3-*' should only be used in this project.

// go/keep-sorted start
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use '../../../focus/focus-ring';
@use '../../../internal/motion/animation';
@use '../../../ripple/ripple';
@use '../../../tokens';
@use '../../badge/badge';
// go/keep-sorted end

$animation-duration: 100ms;

@mixin theme($tokens) {
  // $supported-tokens: tokens.$md-comp-navigation-bar-supported-tokens;

  @each $token, $value in $tokens {
    // @if list.index($supported-tokens, $token) == null {
    //   @error 'Token `#{$token}` is not a supported token.';
    // }

    @if $value {
      --md-navigation-bar-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $tokens: tokens.md-comp-navigation-bar-values();

  :host {
    @each $token, $value in $tokens {
      --_#{$token}: #{$value};
    }

    display: flex;
    flex-grow: 1;
  }

  md-focus-ring {
    @include focus-ring.theme(
      (
        'shape': map.get(tokens.md-sys-shape-values(), 'corner-small'),
        'inward-offset': -1px,
      )
    );
  }

  .md3-navigation-tab {
    align-items: center;
    appearance: none;
    background: none;
    border: none;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: center;
    min-height: 48px;
    min-width: 48px;
    outline: none;
    padding: 8px 0px 12px;
    position: relative;
    text-align: center;
    width: 100%;
    font-family: var(--_label-text-font);
    font-size: var(--_label-text-size);
    line-height: var(--_label-text-line-height);
    font-weight: var(--_label-text-weight);
    // Override the user-agent text-transform: none of <button>
    text-transform: inherit;

    // Firefox draws a dotted border around focused buttons unless specifically overridden.
    &::-moz-focus-inner {
      border: 0;
      padding: 0;
    }
  }

  .md3-navigation-tab__icon-content {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    position: relative;
    z-index: 1;
  }

  .md3-navigation-tab__label-text {
    height: 16px;
    margin-top: 4px;
    opacity: 1;
    transition: animation.standard(opacity, $animation-duration),
      animation.standard(height, $animation-duration);
    z-index: 1;

    .md3-navigation-tab--hide-inactive-label:not(.md3-navigation-tab--active)
      & {
      height: 0;
      opacity: 0;
    }
  }

  .md3-navigation-tab__active-indicator {
    display: flex;
    justify-content: center;
    opacity: 0;
    position: absolute;
    transition: animation.standard(width, $animation-duration),
      animation.standard(opacity, $animation-duration);
    width: 32px;
    background-color: var(--_active-indicator-color);
    border-radius: var(--_active-indicator-shape);

    .md3-navigation-tab--active & {
      opacity: 1;
    }
  }

  // Keep icon-content in sync with active-indicator size.
  .md3-navigation-tab__active-indicator,
  .md3-navigation-tab__icon-content {
    height: var(--_active-indicator-height);
  }

  // Keep icon-content in sync with active-indicator size.
  .md3-navigation-tab--active .md3-navigation-tab__active-indicator,
  .md3-navigation-tab__icon-content {
    width: var(--_active-indicator-width);
  }

  .md3-navigation-tab__icon {
    fill: currentColor;
    align-self: center;
    display: inline-block;
    position: relative;
    width: var(--_icon-size);
    height: var(--_icon-size);
    font-size: var(--_icon-size);

    &.md3-navigation-tab__icon--active {
      display: none;
    }

    .md3-navigation-tab--active & {
      display: none;

      &.md3-navigation-tab__icon--active {
        display: inline-block;
      }
    }
  }

  .md3-navigation-tab__ripple {
    z-index: 0;
  }

  .md3-navigation-tab--active {
    .md3-navigation-tab__icon {
      color: var(--_active-icon-color);
    }

    .md3-navigation-tab__label-text {
      color: var(--_active-label-text-color);
    }

    &:hover {
      .md3-navigation-tab__icon {
        color: var(--_active-hover-icon-color);
      }

      .md3-navigation-tab__label-text {
        color: var(--_active-hover-label-text-color);
      }
    }

    &:focus {
      .md3-navigation-tab__icon {
        color: var(--_active-focus-icon-color);
      }

      .md3-navigation-tab__label-text {
        color: var(--_active-focus-label-text-color);
      }
    }

    &:active {
      .md3-navigation-tab__icon {
        color: var(--_active-pressed-icon-color);
      }

      .md3-navigation-tab__label-text {
        color: var(--_active-pressed-label-text-color);
      }
    }

    @include ripple.theme(
      (
        hover-color: var(--_active-hover-state-layer-color),
        pressed-color: var(--_active-pressed-state-layer-color),
        hover-opacity: var(--_hover-state-layer-opacity),
        pressed-opacity: var(--_pressed-state-layer-opacity),
      )
    );
  }

  .md3-navigation-tab:not(.md3-navigation-tab--active) {
    .md3-navigation-tab__icon {
      color: var(--_inactive-icon-color);
    }

    .md3-navigation-tab__label-text {
      color: var(--_inactive-label-text-color);
    }

    &:hover {
      .md3-navigation-tab__icon {
        color: var(--_inactive-hover-icon-color);
      }

      .md3-navigation-tab__label-text {
        color: var(--_inactive-hover-label-text-color);
      }
    }

    &:focus {
      .md3-navigation-tab__icon {
        color: var(--_inactive-focus-icon-color);
      }

      .md3-navigation-tab__label-text {
        color: var(--_inactive-focus-label-text-color);
      }
    }

    &:active {
      .md3-navigation-tab__icon {
        color: var(--_inactive-pressed-icon-color);
      }

      .md3-navigation-tab__label-text {
        color: var(--_inactive-pressed-label-text-color);
      }
    }

    @include ripple.theme(
      (
        hover-color: var(--_inactive-hover-state-layer-color),
        pressed-color: var(--_inactive-pressed-state-layer-color),
        hover-opacity: var(--_hover-state-layer-opacity),
        pressed-opacity: var(--_pressed-state-layer-opacity),
      )
    );
  }
}
