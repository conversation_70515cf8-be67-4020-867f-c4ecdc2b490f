{"version": 3, "file": "selectOptionController.js", "sourceRoot": "", "sources": ["selectOptionController.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EACL,kBAAkB,GAEnB,MAAM,0DAA0D,CAAC;AAGlE;;;GAGG;AACH,MAAM,UAAU,2BAA2B;IACzC,OAAO,IAAI,KAAK,CAAC,mBAAmB,EAAE;QACpC,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;AACL,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,6BAA6B;IAC3C,OAAO,IAAI,KAAK,CAAC,qBAAqB,EAAE;QACtC,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;AACL,CAAC;AAOD;;;GAGG;AACH,MAAM,OAAO,sBAAsB;IAMjC;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;IACtC,CAAC;IAED;;;;;OAKG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC;IAC/C,CAAC;IAED,gBAAgB,CAAC,IAAY;QAC3B,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;OAMG;IACH,IAAI,WAAW;QACb,IAAI,IAAI,CAAC,mBAAmB,KAAK,IAAI,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC,mBAAmB,CAAC;QAClC,CAAC;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC;IAC/C,CAAC;IAED,cAAc,CAAC,IAAY;QACzB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,YACmB,IAA2C,EAC5D,MAA0B;QADT,SAAI,GAAJ,IAAI,CAAuC;QAjDtD,wBAAmB,GAAkB,IAAI,CAAC;QAE1C,gBAAW,GAAG,IAAI,CAAC;QA+E3B;;;WAGG;QACH,YAAO,GAAG,GAAG,EAAE;YACb,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QACpC,CAAC,CAAC;QAEF;;;WAGG;QACH,cAAS,GAAG,CAAC,CAAgB,EAAE,EAAE;YAC/B,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC;QA3CA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC/D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,UAAU;QACR,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC7C,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;QACjE,CAAC;IACH,CAAC;IAED,WAAW;QACT,mDAAmD;QACnD,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAClE,uEAAuE;YACvE,qEAAqE;YACrE,0DAA0D;YAC1D,6BAA6B;YAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACvB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,2BAA2B,EAAE,CAAC,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,6BAA6B,EAAE,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QACvC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;CAiBF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {Reactive<PERSON>ontroller, ReactiveControllerHost} from 'lit';\n\nimport {\n  MenuItemController,\n  MenuItemControllerConfig,\n} from '../../../menu/internal/controllers/menuItemController.js';\nimport {SelectOption} from './select-option.js';\n\n/**\n * Creates an event fired by a SelectOption to request selection from md-select.\n * Typically fired after `selected` changes from `false` to `true`.\n */\nexport function createRequestSelectionEvent() {\n  return new Event('request-selection', {\n    bubbles: true,\n    composed: true,\n  });\n}\n\n/**\n * Creates an event fired by a SelectOption to request deselection from\n * md-select. Typically fired after `selected` changes from `true` to `false`.\n */\nexport function createRequestDeselectionEvent() {\n  return new Event('request-deselection', {\n    bubbles: true,\n    composed: true,\n  });\n}\n\n/**\n * The options used to inialize SelectOptionController.\n */\nexport type SelectOptionConfig = MenuItemControllerConfig;\n\n/**\n * A controller that provides most functionality and md-select compatibility for\n * an element that implements the SelectOption interface.\n */\nexport class SelectOptionController implements ReactiveController {\n  private readonly menuItemController: MenuItemController;\n  private internalDisplayText: string | null = null;\n  private lastSelected: boolean;\n  private firstUpdate = true;\n\n  /**\n   * The recommended role of the select option.\n   */\n  get role() {\n    return this.menuItemController.role;\n  }\n\n  /**\n   * The text that is selectable via typeahead. If not set, defaults to the\n   * innerText of the item slotted into the `\"headline\"` slot, and if there are\n   * no slotted elements into headline, then it checks the _default_ slot, and\n   * then the `\"supporting-text\"` slot if nothing is in _default_.\n   */\n  get typeaheadText() {\n    return this.menuItemController.typeaheadText;\n  }\n\n  setTypeaheadText(text: string) {\n    this.menuItemController.setTypeaheadText(text);\n  }\n\n  /**\n   * The text that is displayed in the select field when selected. If not set,\n   * defaults to the textContent of the item slotted into the `\"headline\"` slot,\n   * and if there are no slotted elements into headline, then it checks the\n   * _default_ slot, and then the `\"supporting-text\"` slot if nothing is in\n   * _default_.\n   */\n  get displayText() {\n    if (this.internalDisplayText !== null) {\n      return this.internalDisplayText;\n    }\n\n    return this.menuItemController.typeaheadText;\n  }\n\n  setDisplayText(text: string) {\n    this.internalDisplayText = text;\n  }\n\n  /**\n   * @param host The SelectOption in which to attach this controller to.\n   * @param config The object that configures this controller's behavior.\n   */\n  constructor(\n    private readonly host: ReactiveControllerHost & SelectOption,\n    config: SelectOptionConfig,\n  ) {\n    this.lastSelected = this.host.selected;\n    this.menuItemController = new MenuItemController(host, config);\n    host.addController(this);\n  }\n\n  hostUpdate() {\n    if (this.lastSelected !== this.host.selected) {\n      this.host.ariaSelected = this.host.selected ? 'true' : 'false';\n    }\n  }\n\n  hostUpdated() {\n    // Do not dispatch event on first update / boot-up.\n    if (this.lastSelected !== this.host.selected && !this.firstUpdate) {\n      // This section is really useful for when the user sets selected on the\n      // option programmatically. Most other cases (click and keyboard) are\n      // handled by md-select because it needs to coordinate the\n      // single-selection behavior.\n      if (this.host.selected) {\n        this.host.dispatchEvent(createRequestSelectionEvent());\n      } else {\n        this.host.dispatchEvent(createRequestDeselectionEvent());\n      }\n    }\n\n    this.lastSelected = this.host.selected;\n    this.firstUpdate = false;\n  }\n\n  /**\n   * Bind this click listener to the interactive element. Handles closing the\n   * menu.\n   */\n  onClick = () => {\n    this.menuItemController.onClick();\n  };\n\n  /**\n   * Bind this click listener to the interactive element. Handles closing the\n   * menu.\n   */\n  onKeydown = (e: KeyboardEvent) => {\n    this.menuItemController.onKeydown(e);\n  };\n}\n"]}