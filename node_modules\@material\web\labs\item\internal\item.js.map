{"version": 3, "file": "item.js", "sourceRoot": "", "sources": ["item.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,EAAC,IAAI,EAAE,UAAU,EAAC,MAAM,KAAK,CAAC;AACrC,OAAO,EAAC,QAAQ,EAAE,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAErD;;GAEG;AACH,MAAM,OAAO,IAAK,SAAQ,UAAU;IAApC;;QACE;;;;;;WAMG;QACuC,cAAS,GAAG,KAAK,CAAC;IAyC9D,CAAC;IArCU,MAAM;QACb,OAAO,IAAI,CAAA;;;;4CAI6B,IAAI,CAAC,oBAAoB;;;wBAG7C,IAAI,CAAC,oBAAoB;4CACL,IAAI,CAAC,oBAAoB;;;wBAG7C,IAAI,CAAC,oBAAoB;;;;KAI5C,CAAC;IACJ,CAAC;IAEO,oBAAoB;QAC1B,4EAA4E;QAC5E,sEAAsE;QACtE,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAClC,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzB,gBAAgB,IAAI,CAAC,CAAC;YACxB,CAAC;YAED,IAAI,gBAAgB,GAAG,CAAC,EAAE,CAAC;gBACzB,WAAW,GAAG,IAAI,CAAC;gBACnB,MAAM;YACR,CAAC;QACH,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC;IAC/B,CAAC;CACF;AAzC2C;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;uCAAmB;AAEnB;IAAxC,QAAQ,CAAC,YAAY,CAAC;uCAAgD;AAyCzE,SAAS,cAAc,CAAC,IAAqB;IAC3C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,EAAE,CAAC;QACvD,0DAA0D;QAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,CAAC;QACtD,oEAAoE;QACpE,kBAAkB;QAClB,MAAM,iBAAiB,GACrB,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QACpE,IAAI,SAAS,IAAI,iBAAiB,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, LitElement} from 'lit';\nimport {property, queryAll} from 'lit/decorators.js';\n\n/**\n * An item layout component.\n */\nexport class Item extends LitElement {\n  /**\n   * Only needed for SSR.\n   *\n   * Add this attribute when an item has two lines to avoid a Flash Of Unstyled\n   * Content. This attribute is not needed for single line items or items with\n   * three or more lines.\n   */\n  @property({type: Boolean, reflect: true}) multiline = false;\n\n  @queryAll('.text slot') private readonly textSlots!: HTMLSlotElement[];\n\n  override render() {\n    return html`\n      <slot name=\"container\"></slot>\n      <slot class=\"non-text\" name=\"start\"></slot>\n      <div class=\"text\">\n        <slot name=\"overline\" @slotchange=${this.handleTextSlotChange}></slot>\n        <slot\n          class=\"default-slot\"\n          @slotchange=${this.handleTextSlotChange}></slot>\n        <slot name=\"headline\" @slotchange=${this.handleTextSlotChange}></slot>\n        <slot\n          name=\"supporting-text\"\n          @slotchange=${this.handleTextSlotChange}></slot>\n      </div>\n      <slot class=\"non-text\" name=\"trailing-supporting-text\"></slot>\n      <slot class=\"non-text\" name=\"end\"></slot>\n    `;\n  }\n\n  private handleTextSlotChange() {\n    // Check if there's more than one text slot with content. If so, the item is\n    // multiline, which has a different min-height than single line items.\n    let isMultiline = false;\n    let slotsWithContent = 0;\n    for (const slot of this.textSlots) {\n      if (slotHasContent(slot)) {\n        slotsWithContent += 1;\n      }\n\n      if (slotsWithContent > 1) {\n        isMultiline = true;\n        break;\n      }\n    }\n\n    this.multiline = isMultiline;\n  }\n}\n\nfunction slotHasContent(slot: HTMLSlotElement) {\n  for (const node of slot.assignedNodes({flatten: true})) {\n    // Assume there's content if there's an element slotted in\n    const isElement = node.nodeType === Node.ELEMENT_NODE;\n    // If there's only text nodes for the default slot, check if there's\n    // non-whitespace.\n    const isTextWithContent =\n      node.nodeType === Node.TEXT_NODE && node.textContent?.match(/\\S/);\n    if (isElement || isTextWithContent) {\n      return true;\n    }\n  }\n\n  return false;\n}\n"]}