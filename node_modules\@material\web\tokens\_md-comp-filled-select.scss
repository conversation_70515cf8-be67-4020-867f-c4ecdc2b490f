//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
@use 'sass:string';
// go/keep-sorted end
// go/keep-sorted start
@use './internal/shape';
@use './internal/validate';
@use './md-sys-color';
@use './md-sys-elevation';
@use './md-sys-shape';
@use './md-sys-state';
@use './md-sys-typescale';
@use './v0_192/md-comp-filled-select';
// go/keep-sorted end

$supported-tokens: (
  // go/keep-sorted start
  'text-field-active-indicator-color',
  'text-field-active-indicator-height',
  'text-field-container-color',
  'text-field-container-shape',
  'text-field-container-shape-end-end',
  'text-field-container-shape-end-start',
  'text-field-container-shape-start-end',
  'text-field-container-shape-start-start',
  'text-field-disabled-active-indicator-color',
  'text-field-disabled-active-indicator-height',
  'text-field-disabled-active-indicator-opacity',
  'text-field-disabled-container-color',
  'text-field-disabled-container-opacity',
  'text-field-disabled-input-text-color',
  'text-field-disabled-input-text-opacity',
  'text-field-disabled-label-text-color',
  'text-field-disabled-label-text-opacity',
  'text-field-disabled-leading-icon-color',
  'text-field-disabled-leading-icon-opacity',
  'text-field-disabled-supporting-text-color',
  'text-field-disabled-supporting-text-opacity',
  'text-field-disabled-trailing-icon-color',
  'text-field-disabled-trailing-icon-opacity',
  'text-field-error-active-indicator-color',
  'text-field-error-focus-active-indicator-color',
  'text-field-error-focus-input-text-color',
  'text-field-error-focus-label-text-color',
  'text-field-error-focus-leading-icon-color',
  'text-field-error-focus-supporting-text-color',
  'text-field-error-focus-trailing-icon-color',
  'text-field-error-hover-active-indicator-color',
  'text-field-error-hover-input-text-color',
  'text-field-error-hover-label-text-color',
  'text-field-error-hover-leading-icon-color',
  'text-field-error-hover-state-layer-color',
  'text-field-error-hover-state-layer-opacity',
  'text-field-error-hover-supporting-text-color',
  'text-field-error-hover-trailing-icon-color',
  'text-field-error-input-text-color',
  'text-field-error-label-text-color',
  'text-field-error-leading-icon-color',
  'text-field-error-supporting-text-color',
  'text-field-error-trailing-icon-color',
  'text-field-focus-active-indicator-color',
  'text-field-focus-active-indicator-height',
  'text-field-focus-input-text-color',
  'text-field-focus-label-text-color',
  'text-field-focus-leading-icon-color',
  'text-field-focus-supporting-text-color',
  'text-field-focus-trailing-icon-color',
  'text-field-hover-active-indicator-color',
  'text-field-hover-active-indicator-height',
  'text-field-hover-input-text-color',
  'text-field-hover-label-text-color',
  'text-field-hover-leading-icon-color',
  'text-field-hover-state-layer-color',
  'text-field-hover-state-layer-opacity',
  'text-field-hover-supporting-text-color',
  'text-field-hover-trailing-icon-color',
  'text-field-input-text-color',
  'text-field-input-text-font',
  'text-field-input-text-line-height',
  'text-field-input-text-size',
  'text-field-input-text-weight',
  'text-field-label-text-color',
  'text-field-label-text-font',
  'text-field-label-text-line-height',
  'text-field-label-text-populated-line-height',
  'text-field-label-text-populated-size',
  'text-field-label-text-size',
  'text-field-label-text-weight',
  'text-field-leading-icon-color',
  'text-field-leading-icon-size',
  'text-field-supporting-text-color',
  'text-field-supporting-text-font',
  'text-field-supporting-text-line-height',
  'text-field-supporting-text-size',
  'text-field-supporting-text-weight',
  'text-field-trailing-icon-color',
  'text-field-trailing-icon-size',
  // go/keep-sorted end
);

$unsupported-tokens: (
  // go/keep-sorted start
  'menu-cascading-menu-indicator-icon-color',
  'menu-cascading-menu-indicator-icon-size',
  'menu-container-color',
  'menu-container-elevation',
  'menu-container-shadow-color',
  'menu-container-shape',
  'menu-divider-color',
  'menu-divider-height',
  'menu-list-item-container-height',
  'menu-list-item-label-text-color',
  'menu-list-item-label-text-font',
  'menu-list-item-label-text-line-height',
  'menu-list-item-label-text-size',
  'menu-list-item-label-text-tracking',
  'menu-list-item-label-text-type',
  'menu-list-item-label-text-weight',
  'menu-list-item-selected-container-color',
  'menu-list-item-with-leading-icon-leading-icon-color',
  'menu-list-item-with-leading-icon-leading-icon-size',
  'menu-list-item-with-trailing-icon-trailing-icon-color',
  'menu-list-item-with-trailing-icon-trailing-icon-size',
  'text-field-input-text-tracking',
  'text-field-input-text-type',
  'text-field-label-text-tracking',
  'text-field-label-text-type',
  'text-field-supporting-text-tracking',
  'text-field-supporting-text-type',
  // go/keep-sorted end
);

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function _get-override-tokens($exclude-hardcoded-values) {
  // Values pulled from b/198759625 spreadsheet
  @return (
    // go/keep-sorted start
    // TODO(b/259455114): remove when focus tokens update to 3px
    'text-field-focus-active-indicator-height':
      if($exclude-hardcoded-values, null, 3px),
    // go/keep-sorted end
  );
}

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $original-tokens: md-comp-filled-select.values(
    $deps,
    $exclude-hardcoded-values
  );
  $original-tokens: map.merge(
    $original-tokens,
    _get-override-tokens($exclude-hardcoded-values)
  );
  $new-tokens: shape.get-new-logical-shape-tokens(
    $original-tokens,
    'text-field-container-shape'
  );

  $tokens: validate.values(
    $original-tokens,
    $supported-tokens: $supported-tokens,
    $unsupported-tokens: $unsupported-tokens,
    $new-tokens: $new-tokens
  );

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      @if string.index($token, 'text-field-container-shape-') == 1 {
        // Add fallback to shorthand for logical shape properties.
        $value: var(--md-filled-select-text-field-container-shape, #{$value});
      }

      $tokens: map.set(
        $tokens,
        $token,
        var(--md-filled-select-#{$token}, #{$value})
      );
    }
  }

  @return $tokens;
}
