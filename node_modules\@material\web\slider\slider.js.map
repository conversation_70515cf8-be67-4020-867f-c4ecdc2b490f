{"version": 3, "file": "slider.js", "sourceRoot": "", "sources": ["slider.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,MAAM,IAAI,kBAAkB,EAAC,MAAM,oCAAoC,CAAC;AAChF,OAAO,EAAC,MAAM,EAAC,MAAM,sBAAsB,CAAC;AAC5C,OAAO,EAAC,MAAM,EAAC,MAAM,6BAA6B,CAAC;AAQnD;;;;;;;;;;;;;;;;GAgBG;AAEI,IAAM,QAAQ,GAAd,MAAM,QAAS,SAAQ,MAAM;;AAClB,eAAM,GAAwB,CAAC,MAAM,EAAE,kBAAkB,CAAC,AAApD,CAAqD;AADhE,QAAQ;IADpB,aAAa,CAAC,WAAW,CAAC;GACd,QAAQ,CAEpB", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {styles as forcedColorsStyles} from './internal/forced-colors-styles.js';\nimport {Slider} from './internal/slider.js';\nimport {styles} from './internal/slider-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-slider': MdSlider;\n  }\n}\n\n/**\n * @summary Sliders allow users to view and select a value (or range) along\n * a track.\n *\n * @description\n * Changes made with sliders are immediate, allowing the user to make slider\n * adjustments while determining a selection. Sliders shouldn’t be used to\n * adjust settings with any delay in providing user feedback. Sliders reflect\n * the current state of the settings they control.\n *\n * __Example usages:__\n * - Sliders are ideal for adjusting settings such as volume and brightness, or\n * for applying image filters.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-slider')\nexport class MdSlider extends Slider {\n  static override styles: CSSResultOrNative[] = [styles, forcedColorsStyles];\n}\n"]}