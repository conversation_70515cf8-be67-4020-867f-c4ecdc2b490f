//
// Copyright 2021 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:list';
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use '../../field/outlined-field';
@use '../../tokens';
// go/keep-sorted end

@mixin theme($tokens) {
  $supported-tokens: tokens.$md-comp-outlined-text-field-supported-tokens;
  @each $token, $value in $tokens {
    @if list.index($supported-tokens, $token) == null {
      @error 'Token `#{$token}` is not a supported token.';
    }

    @if $value {
      --md-outlined-text-field-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $tokens: tokens.md-comp-outlined-text-field-values();

  :host {
    // Only use the logical properties.
    $tokens: map.remove($tokens, 'container-shape');
    @each $token, $value in $tokens {
      --_#{$token}: #{$value};
    }

    @include outlined-field.theme(
      (
        // go/keep-sorted start
        'bottom-space': var(--_bottom-space),
        'container-shape-end-end': var(--_container-shape-end-end),
        'container-shape-end-start': var(--_container-shape-end-start),
        'container-shape-start-end': var(--_container-shape-start-end),
        'container-shape-start-start': var(--_container-shape-start-start),
        'content-color': var(--_input-text-color),
        'content-font': var(--_input-text-font),
        'content-line-height': var(--_input-text-line-height),
        'content-size': var(--_input-text-size),
        'content-space': var(--_icon-input-space),
        'content-weight': var(--_input-text-weight),
        'disabled-content-color': var(--_disabled-input-text-color),
        'disabled-content-opacity': var(--_disabled-input-text-opacity),
        'disabled-label-text-color': var(--_disabled-label-text-color),
        'disabled-label-text-opacity': var(--_disabled-label-text-opacity),
        'disabled-leading-content-color': var(--_disabled-leading-icon-color),
        'disabled-leading-content-opacity':
          var(--_disabled-leading-icon-opacity),
        'disabled-outline-color': var(--_disabled-outline-color),
        'disabled-outline-opacity': var(--_disabled-outline-opacity),
        'disabled-outline-width': var(--_disabled-outline-width),
        'disabled-supporting-text-color': var(--_disabled-supporting-text-color),
        'disabled-supporting-text-opacity':
          var(--_disabled-supporting-text-opacity),
        'disabled-trailing-content-color': var(--_disabled-trailing-icon-color),
        'disabled-trailing-content-opacity':
          var(--_disabled-trailing-icon-opacity),
        'error-content-color': var(--_error-input-text-color),
        'error-focus-content-color': var(--_error-focus-input-text-color),
        'error-focus-label-text-color': var(--_error-focus-label-text-color),
        'error-focus-leading-content-color':
          var(--_error-focus-leading-icon-color),
        'error-focus-outline-color': var(--_error-focus-outline-color),
        'error-focus-supporting-text-color':
          var(--_error-focus-supporting-text-color),
        'error-focus-trailing-content-color':
          var(--_error-focus-trailing-icon-color),
        'error-hover-content-color': var(--_error-hover-input-text-color),
        'error-hover-label-text-color': var(--_error-hover-label-text-color),
        'error-hover-leading-content-color':
          var(--_error-hover-leading-icon-color),
        'error-hover-outline-color': var(--_error-hover-outline-color),
        'error-hover-supporting-text-color':
          var(--_error-hover-supporting-text-color),
        'error-hover-trailing-content-color':
          var(--_error-hover-trailing-icon-color),
        'error-label-text-color': var(--_error-label-text-color),
        'error-leading-content-color': var(--_error-leading-icon-color),
        'error-outline-color': var(--_error-outline-color),
        'error-supporting-text-color': var(--_error-supporting-text-color),
        'error-trailing-content-color': var(--_error-trailing-icon-color),
        'focus-content-color': var(--_focus-input-text-color),
        'focus-label-text-color': var(--_focus-label-text-color),
        'focus-leading-content-color': var(--_focus-leading-icon-color),
        'focus-outline-color': var(--_focus-outline-color),
        'focus-outline-width': var(--_focus-outline-width),
        'focus-supporting-text-color': var(--_focus-supporting-text-color),
        'focus-trailing-content-color': var(--_focus-trailing-icon-color),
        'hover-content-color': var(--_hover-input-text-color),
        'hover-label-text-color': var(--_hover-label-text-color),
        'hover-leading-content-color': var(--_hover-leading-icon-color),
        'hover-outline-color': var(--_hover-outline-color),
        'hover-outline-width': var(--_hover-outline-width),
        'hover-supporting-text-color': var(--_hover-supporting-text-color),
        'hover-trailing-content-color': var(--_hover-trailing-icon-color),
        'label-text-color': var(--_label-text-color),
        'label-text-font': var(--_label-text-font),
        'label-text-line-height': var(--_label-text-line-height),
        'label-text-populated-line-height':
          var(--_label-text-populated-line-height),
        'label-text-populated-size': var(--_label-text-populated-size),
        'label-text-size': var(--_label-text-size),
        'label-text-weight': var(--_label-text-weight),
        'leading-content-color': var(--_leading-icon-color),
        'leading-space': var(--_leading-space),
        'outline-color': var(--_outline-color),
        'outline-width': var(--_outline-width),
        'supporting-text-color': var(--_supporting-text-color),
        'supporting-text-font': var(--_supporting-text-font),
        'supporting-text-line-height': var(--_supporting-text-line-height),
        'supporting-text-size': var(--_supporting-text-size),
        'supporting-text-weight': var(--_supporting-text-weight),
        'top-space': var(--_top-space),
        'trailing-content-color': var(--_trailing-icon-color),
        'trailing-space': var(--_trailing-space),
        'with-leading-content-leading-space':
          var(--_with-leading-icon-leading-space),
        'with-trailing-content-trailing-space':
          var(--_with-trailing-icon-trailing-space),
        // go/keep-sorted end
      )
    );
  }
}
