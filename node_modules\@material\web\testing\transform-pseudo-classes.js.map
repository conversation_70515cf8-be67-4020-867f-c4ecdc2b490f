{"version": 3, "file": "transform-pseudo-classes.js", "sourceRoot": "", "sources": ["transform-pseudo-classes.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;;;GAIG;AACH,MAAM,CAAC,MAAM,6BAA6B,GAAG;IAC3C,SAAS;IACT,WAAW;IACX,QAAQ;IACR,gBAAgB;IAChB,eAAe;IACf,QAAQ;IACR,UAAU;IACV,OAAO;IACP,SAAS;IACT,UAAU;IACV,eAAe;IACf,QAAQ;IACR,UAAU;CACX,CAAC;AAEF;;;;;GAKG;AACH,MAAM,UAAU,yBAAyB,CAAC,WAAmB;IAC3D,OAAO,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;AACxC,CAAC;AAED;;;GAGG;AACH,MAAM,sBAAsB,GAAG,IAAI,OAAO,EAAiB,CAAC;AAE5D;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,sBAAsB,CACpC,WAAoC,EACpC,aAAa,GAAG,6BAA6B;IAE7C,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;QACrC,IAAI,sBAAsB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,SAAS;QACX,CAAC;QAED,IAAI,KAAkB,CAAC;QACvB,IAAI,CAAC;YACH,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC;QAC9B,CAAC;QAAC,MAAM,CAAC;YACP,SAAS;QACX,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3C,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;QACpD,CAAC;QAED,sBAAsB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAS,iBAAiB,CAAC,IAAa;IACtC,OAAO,CACL,CAAC,CAAE,IAAwB,EAAE,QAAQ;QACrC,CAAE,IAAqB,CAAC,YAAY,CACrC,CAAC;AACJ,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,SAAS,CAChB,IAAa,EACb,UAA2C,EAC3C,KAAa,EACb,aAAuB;IAEvB,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACnD,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;QACtD,CAAC;QACD,OAAO;IACT,CAAC;IAED,IAAI,CAAC,CAAC,IAAI,YAAY,YAAY,CAAC,EAAE,CAAC;QACpC,OAAO;IACT,CAAC;IAED,IAAI,CAAC;QACH,IAAI,EAAC,YAAY,EAAC,GAAG,IAAI,CAAC;QAC1B,gEAAgE;QAChE,0CAA0C;QAC1C,MAAM,KAAK,GAAG,wBAAwB,CAAC;QACvC,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;YACxE,yCAAyC;YACzC,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;gBACjE,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,OAAO,CAAC,OAAO,EAAE,CAAC;QAClB,YAAY,GAAG,uBAAuB,CAAC,YAAY,CAAC,CAAC;QACrD,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC5B,YAAY;gBACV,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,KAAM,CAAC;oBACvC,IAAI,yBAAyB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;oBACzC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,KAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,GAAG,GAAG,GAAG,YAAY,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC;QACtD,UAAU,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;IACxC,CAAC;IAAC,OAAO,KAAc,EAAE,CAAC;QACxB,0DAA0D;QAC1D,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;AACH,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAS,uBAAuB,CAAC,YAAoB;IACnD,MAAM,2BAA2B,GAAG,KAAK,CAAC,IAAI,CAC5C,YAAY,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAClD,CAAC;IACF,2BAA2B,CAAC,OAAO,EAAE,CAAC;IACtC,KAAK,MAAM,KAAK,IAAI,2BAA2B,EAAE,CAAC;QAChD,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,kBAAkB,GAAG,KAAK,CAAC,KAAM,CAAC;QACxC,MAAM,qBAAqB,GAAG,YAAY;aACvC,SAAS,CAAC,kBAAkB,CAAC;aAC7B,KAAK,CAAC,yBAAyB,CAAE,CAAC;QACrC,MAAM,KAAK,GAAG,qBAAqB,CAAC,KAAM,GAAG,kBAAkB,CAAC;QAChE,YAAY;YACV,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;gBAChC,aAAa;gBACb,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAChC,YAAY;YACV,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,kBAAkB,CAAC;gBAC7C,YAAY,CAAC,SAAS,CAAC,kBAAkB,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;IACtE,CAAC;IAED,OAAO,YAAY,CAAC;AACtB,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * Array of pseudo classes to transform by default. These pseudo classes\n * represent state interactions from the user (such as :hover) or the browser\n * (such as :autofill) that cannot be reproduced with HTML markup.\n */\nexport const defaultTransformPseudoClasses = [\n  ':active',\n  ':autofill',\n  ':focus',\n  ':focus-visible',\n  ':focus-within',\n  ':hover',\n  ':invalid',\n  ':link',\n  ':paused',\n  ':playing',\n  ':user-invalid',\n  ':valid',\n  ':visited',\n];\n\n/**\n * Retrieves the transformed class name for a given pseudo class.\n *\n * @param pseudoClass The pseudo class to transform.\n * @return The transform pseudo class string.\n */\nexport function getTransformedPseudoClass(pseudoClass: string) {\n  return `_${pseudoClass.substring(1)}`;\n}\n\n/**\n * A weak set of stylesheets to use as reference for whether or not a stylesheet\n * has been transformed.\n */\nconst transformedStyleSheets = new WeakSet<CSSStyleSheet>();\n\n/**\n * Transforms a document's stylesheets' pseudo classes into normal classes with\n * a new stylesheet.\n *\n * Pseudo classes are given an underscore in their transformation. For example,\n * `:hover` transforms to `._hover`.\n *\n * ```css\n * .mdc-foo:hover {\n *   color: teal;\n * }\n * ```\n * ```css\n * .mdc-foo._hover {\n *   color: teal;\n * }\n * ```\n *\n * @param pseudoClasses An optional array of pseudo class names to transform.\n */\nexport function transformPseudoClasses(\n  stylesheets: Iterable<CSSStyleSheet>,\n  pseudoClasses = defaultTransformPseudoClasses,\n) {\n  for (const stylesheet of stylesheets) {\n    if (transformedStyleSheets.has(stylesheet)) {\n      continue;\n    }\n\n    let rules: CSSRuleList;\n    try {\n      rules = stylesheet.cssRules;\n    } catch {\n      continue;\n    }\n\n    for (let j = rules.length - 1; j >= 0; j--) {\n      visitRule(rules[j], stylesheet, j, pseudoClasses);\n    }\n\n    transformedStyleSheets.add(stylesheet);\n  }\n}\n\n/**\n * Determines whether or not the CSSRule is a CSSGroupingRule.\n *\n * Cannot check instanceof because FF treats a CSSStyleRule as a subclass of\n * CSSGroupingRule unlike Chrome and Safari\n */\nfunction isCSSGroupingRule(rule: CSSRule): rule is CSSGroupingRule {\n  return (\n    !!(rule as CSSGroupingRule)?.cssRules &&\n    !(rule as CSSStyleRule).selectorText\n  );\n}\n\n/**\n * Visits a rule for the given stylesheet and adds a rule that replaces any\n * pseudo classes with a regular transformed class for simulation styling.\n *\n * @param rule The CSS rule to transform.\n * @param stylesheet The rule's parent stylesheet to update.\n * @param index The index of the rule in the parent stylesheet.\n * @param pseudoClasses An array of pseudo classes to search for and replace.\n */\nfunction visitRule(\n  rule: CSSRule,\n  stylesheet: CSSStyleSheet | CSSGroupingRule,\n  index: number,\n  pseudoClasses: string[],\n) {\n  if (isCSSGroupingRule(rule)) {\n    for (let i = rule.cssRules.length - 1; i >= 0; i--) {\n      visitRule(rule.cssRules[i], rule, i, pseudoClasses);\n    }\n    return;\n  }\n\n  if (!(rule instanceof CSSStyleRule)) {\n    return;\n  }\n\n  try {\n    let {selectorText} = rule;\n    // match :foo, ensuring that it does not have a paren at the end\n    // (no pseudo class functions like :foo())\n    const regex = /(:(?![\\w-]+\\()[\\w-]+)/g;\n    const matches = Array.from(selectorText.matchAll(regex)).filter((match) => {\n      // don't match pseudo elements like ::foo\n      if (match.index != null && selectorText[match.index - 1] === ':') {\n        return false;\n      }\n      return pseudoClasses.includes(match[1]);\n    });\n\n    if (!matches.length) {\n      return;\n    }\n\n    matches.reverse();\n    selectorText = rearrangePseudoElements(selectorText);\n    for (const match of matches) {\n      selectorText =\n        selectorText.substring(0, match.index!) +\n        `.${getTransformedPseudoClass(match[1])}` +\n        selectorText.substring(match.index! + match[1].length);\n    }\n\n    const css = `${selectorText} {${rule.style.cssText}}`;\n    stylesheet.insertRule(css, index + 1);\n  } catch (error: unknown) {\n    // Catch exception to skip the rule that cannot be parsed.\n    console.error(error);\n  }\n}\n\n/**\n * Re-arranges a selector's pseudo elements to appear at the end of the\n * selector. This prevents invalid CSS when replacing pseudo classes that\n * appear after a pseudo element.\n *\n * @example\n * // '.foo::before:hover' -> '.foo::before._hover' is invalid\n *\n * rearrangePseudoElements('.foo::before:hover'); // '.foo:hover::before'\n * // '.foo:hover::before' -> '.foo._hover::before' is valid\n *\n * @param selectorText The selector text string to re-arrange.\n * @return The re-arranged selector text.\n */\nfunction rearrangePseudoElements(selectorText: string) {\n  const pseudoElementsBeforeClasses = Array.from(\n    selectorText.matchAll(/(?:::[\\w-]+)+(?=:[\\w-])/g),\n  );\n  pseudoElementsBeforeClasses.reverse();\n  for (const match of pseudoElementsBeforeClasses) {\n    const pseudoElement = match[0];\n    const pseudoElementIndex = match.index!;\n    const endOfCompoundSelector = selectorText\n      .substring(pseudoElementIndex)\n      .match(/(\\s(?!([^\\s].)*\\))|,|$)/)!;\n    const index = endOfCompoundSelector.index! + pseudoElementIndex;\n    selectorText =\n      selectorText.substring(0, index) +\n      pseudoElement +\n      selectorText.substring(index);\n    selectorText =\n      selectorText.substring(0, pseudoElementIndex) +\n      selectorText.substring(pseudoElementIndex + pseudoElement.length);\n  }\n\n  return selectorText;\n}\n"]}