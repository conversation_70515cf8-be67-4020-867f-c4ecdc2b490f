//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:list';
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use '../../tokens';
// go/keep-sorted end

@mixin theme($tokens) {
  $supported-tokens: tokens.$md-comp-divider-supported-tokens;

  @each $token, $value in $tokens {
    @if list.index($supported-tokens, $token) == null {
      @error 'Token `#{$token}` is not a supported token.';
    }

    @if $value {
      --md-divider-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $tokens: tokens.md-comp-divider-values();

  :host {
    box-sizing: border-box;
    color: map.get($tokens, 'color');
    display: flex;
    height: map.get($tokens, 'thickness');
    width: 100%;
  }

  :host([inset]),
  :host([inset-start]) {
    padding-inline-start: 16px;
  }

  :host([inset]),
  :host([inset-end]) {
    padding-inline-end: 16px;
  }

  :host::before {
    background: currentColor;
    content: '';
    height: 100%;
    width: 100%;
  }

  @media (forced-colors: active) {
    :host::before {
      background: CanvasText;
    }
  }
}
