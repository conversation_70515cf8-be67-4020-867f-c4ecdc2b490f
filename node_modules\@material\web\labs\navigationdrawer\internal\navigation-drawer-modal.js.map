{"version": 3, "file": "navigation-drawer-modal.js", "sourceRoot": "", "sources": ["navigation-drawer-modal.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,EAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAiB,MAAM,KAAK,CAAC;AAC9D,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAGrD,OAAO,EAAC,kBAAkB,EAAC,MAAM,oCAAoC,CAAC;AAEtE,wCAAwC;AACxC,MAAM,8BAA8B,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAEtE;;;;;GAKG;AACH,MAAM,OAAO,qBAAsB,SAAQ,8BAA8B;IAAzE;;QAC6B,WAAM,GAAG,KAAK,CAAC;QAC9B,UAAK,GAAoB,KAAK,CAAC;IAkE7C,CAAC;IAhEoB,MAAM;QACvB,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;QACpD,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;QACnD,iCAAiC;QACjC,MAAM,EAAC,SAAS,EAAE,SAAS,EAAC,GAAG,IAAuB,CAAC;QACvD,OAAO,IAAI,CAAA;;oDAEqC,IAAI,CAAC,eAAe,EAAE;kBACxD,IAAI,CAAC,gBAAgB;;;wBAGf,YAAY;sBACd,UAAU;qBACX,SAAS,IAAI,OAAO;qBACpB,SAAS,IAAI,OAAO;6CACI,IAAI,CAAC,gBAAgB,EAAE;oBAChD,IAAI,CAAC,aAAa;;;;;;;KAOjC,CAAC;IACJ,CAAC;IAEO,eAAe;QACrB,OAAO,QAAQ,CAAC;YACd,4CAA4C,EAAE,IAAI,CAAC,MAAM;SAC1D,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,OAAO,QAAQ,CAAC;YACd,qCAAqC,EAAE,IAAI,CAAC,MAAM;YAClD,6CAA6C,EAAE,IAAI,CAAC,KAAK,KAAK,OAAO;SACtE,CAAC,CAAC;IACL,CAAC;IAEkB,OAAO,CACxB,iBAAwD;QAExD,IAAI,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpC,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,2BAA2B,EAAE;oBAC3C,MAAM,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAC;oBAC7B,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,IAAI;iBACf,CAAC,CACH,CAAC;YACJ,CAAC,EAAE,GAAG,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,KAAoB;QACxC,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC5B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACtB,CAAC;IACH,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;IAC7B,CAAC;CACF;AAnE4B;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;qDAAgB;AAC9B;IAAX,QAAQ,EAAE;oDAAgC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, LitElement, nothing, PropertyValues} from 'lit';\nimport {property} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\n\n// Separate variable needed for closure.\nconst navigationDrawerModalBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * b/265346501 - add docs\n *\n * @fires navigation-drawer-changed {CustomEvent<{opened: boolean}>}\n * Dispatched whenever the drawer opens or closes --bubbles --composed\n */\nexport class NavigationDrawerModal extends navigationDrawerModalBaseClass {\n  @property({type: Boolean}) opened = false;\n  @property() pivot: 'start' | 'end' = 'end';\n\n  protected override render() {\n    const ariaExpanded = this.opened ? 'true' : 'false';\n    const ariaHidden = !this.opened ? 'true' : 'false';\n    // Needed for closure conformance\n    const {ariaLabel, ariaModal} = this as ARIAMixinStrict;\n    return html`\n      <div\n        class=\"md3-navigation-drawer-modal__scrim ${this.getScrimClasses()}\"\n        @click=\"${this.handleScrimClick}\">\n      </div>\n      <div\n        aria-expanded=${ariaExpanded}\n        aria-hidden=${ariaHidden}\n        aria-label=${ariaLabel || nothing}\n        aria-modal=${ariaModal || nothing}\n        class=\"md3-navigation-drawer-modal ${this.getRenderClasses()}\"\n        @keydown=\"${this.handleKeyDown}\"\n        role=\"dialog\"\n        ><div class=\"md3-elevation-overlay\"></div>\n        <div class=\"md3-navigation-drawer-modal__slot-content\">\n          <slot></slot>\n        </div>\n      </div>\n    `;\n  }\n\n  private getScrimClasses() {\n    return classMap({\n      'md3-navigation-drawer-modal--scrim-visible': this.opened,\n    });\n  }\n\n  private getRenderClasses() {\n    return classMap({\n      'md3-navigation-drawer-modal--opened': this.opened,\n      'md3-navigation-drawer-modal--pivot-at-start': this.pivot === 'start',\n    });\n  }\n\n  protected override updated(\n    changedProperties: PropertyValues<NavigationDrawerModal>,\n  ) {\n    if (changedProperties.has('opened')) {\n      setTimeout(() => {\n        this.dispatchEvent(\n          new CustomEvent('navigation-drawer-changed', {\n            detail: {opened: this.opened},\n            bubbles: true,\n            composed: true,\n          }),\n        );\n      }, 250);\n    }\n  }\n\n  private handleKeyDown(event: KeyboardEvent) {\n    if (event.code === 'Escape') {\n      this.opened = false;\n    }\n  }\n\n  private handleScrimClick() {\n    this.opened = !this.opened;\n  }\n}\n"]}