{"version": 3, "file": "sub-menu.js", "sourceRoot": "", "sources": ["sub-menu.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,OAAO,EAAC,MAAM,gCAAgC,CAAC;AACvD,OAAO,EAAC,MAAM,EAAC,MAAM,uCAAuC,CAAC;AAQ7D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+CG;AAEI,IAAM,SAAS,GAAf,MAAM,SAAU,SAAQ,OAAO;;AACpB,gBAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAD5C,SAAS;IADrB,aAAa,CAAC,aAAa,CAAC;GAChB,SAAS,CAErB", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {SubMenu} from './internal/submenu/sub-menu.js';\nimport {styles} from './internal/submenu/sub-menu-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-sub-menu': MdSubMenu;\n  }\n}\n\n/**\n * @summary Menus display a list of choices on a temporary surface.\n *\n * @description\n * Menu items are the selectable choices within the menu. Menu items must\n * implement the `Menu` interface and also have the `md-menu`\n * attribute. Additionally menu items are list items so they must also have the\n * `md-list-item` attribute.\n *\n * Menu items can control a menu by selectively firing the `close-menu` and\n * `deselect-items` events.\n *\n * This menu item will open a sub-menu that is slotted in the `submenu` slot.\n * Additionally, the containing menu must either have `has-overflow` or\n * `positioning=fixed` set to `true` in order to display the containing menu\n * properly.\n *\n * @example\n * ```html\n * <div style=\"position:relative;\">\n *   <button\n *       id=\"anchor\"\n *       @click=${() => this.menuRef.value.show()}>\n *     Click to open menu\n *   </button>\n *   <!--\n *     `has-overflow` is required when using a submenu which overflows the\n *     menu's contents\n *   -->\n *   <md-menu anchor=\"anchor\" has-overflow ${ref(menuRef)}>\n *     <md-menu-item headline=\"This is a headline\"></md-menu-item>\n *     <md-sub-menu>\n *       <md-menu-item\n *           slot=\"item\"\n *           headline=\"this is a submenu item\">\n *       </md-menu-item>\n *       <md-menu slot=\"menu\">\n *         <md-menu-item headline=\"This is an item inside a submenu\">\n *         </md-menu-item>\n *       </md-menu>\n *     </md-sub-menu>\n *   </md-menu>\n * </div>\n * ```\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-sub-menu')\nexport class MdSubMenu extends SubMenu {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}