//
// Copyright 2021 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:list';
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use '../../tokens';
// go/keep-sorted end

@mixin theme($tokens) {
  $supported-tokens: tokens.$md-comp-outlined-button-supported-tokens;
  @each $token, $value in $tokens {
    @if list.index($supported-tokens, $token) == null {
      @error 'Token `#{$token}` is not a supported token.';
    }

    @if $value {
      --md-outlined-button-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $tokens: tokens.md-comp-outlined-button-values();

  :host {
    // Only use the logical properties.
    $tokens: map.remove($tokens, 'container-shape');
    @each $token, $value in $tokens {
      --_#{$token}: #{$value};
    }

    --_container-color: none;
    --_disabled-container-color: none;
    --_disabled-container-opacity: 0;
  }

  .outline {
    inset: 0;
    border-style: solid;
    position: absolute;
    box-sizing: border-box;
    border-color: var(--_outline-color);
    border-start-start-radius: var(--_container-shape-start-start);
    border-start-end-radius: var(--_container-shape-start-end);
    border-end-start-radius: var(--_container-shape-end-start);
    border-end-end-radius: var(--_container-shape-end-end);
  }

  :host(:active) .outline {
    border-color: var(--_pressed-outline-color);
  }

  :host(:is([disabled], [soft-disabled])) .outline {
    border-color: var(--_disabled-outline-color);
    opacity: var(--_disabled-outline-opacity);
  }

  @media (forced-colors: active) {
    :host(:is([disabled], [soft-disabled])) .background {
      // Only outlined buttons change their border when disabled to distinguish
      // them from other buttons that add a border for increased visibility in
      // HCM.
      border-color: GrayText;
    }

    :host(:is([disabled], [soft-disabled])) .outline {
      opacity: 1;
    }
  }

  .outline,
  md-ripple {
    border-width: var(--_outline-width);
  }

  md-ripple {
    inline-size: calc(100% - 2 * var(--_outline-width));
    block-size: calc(100% - 2 * var(--_outline-width));
    border-style: solid;
    border-color: transparent;
  }
}
