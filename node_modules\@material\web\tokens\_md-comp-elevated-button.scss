//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
@use 'sass:string';
// go/keep-sorted end
// go/keep-sorted start
@use './internal/shape';
@use './internal/validate';
@use './md-sys-color';
@use './md-sys-elevation';
@use './md-sys-shape';
@use './md-sys-state';
@use './md-sys-typescale';
@use './v0_192/md-comp-elevated-button';
// go/keep-sorted end

$supported-tokens: (
  // go/keep-sorted start
  'container-color',
  'container-elevation',
  'container-height',
  'container-shadow-color',
  'container-shape',
  'container-shape-end-end',
  'container-shape-end-start',
  'container-shape-start-end',
  'container-shape-start-start',
  'disabled-container-color',
  'disabled-container-elevation',
  'disabled-container-opacity',
  'disabled-icon-color',
  'disabled-icon-opacity',
  'disabled-label-text-color',
  'disabled-label-text-opacity',
  'focus-container-elevation',
  'focus-icon-color',
  'focus-label-text-color',
  'hover-container-elevation',
  'hover-icon-color',
  'hover-label-text-color',
  'hover-state-layer-color',
  'hover-state-layer-opacity',
  'icon-color',
  'icon-size',
  'label-text-color',
  'label-text-font',
  'label-text-line-height',
  'label-text-size',
  'label-text-weight',
  'leading-space',
  'pressed-container-elevation',
  'pressed-icon-color',
  'pressed-label-text-color',
  'pressed-state-layer-color',
  'pressed-state-layer-opacity',
  'trailing-space',
  'with-leading-icon-leading-space',
  'with-leading-icon-trailing-space',
  'with-trailing-icon-leading-space',
  'with-trailing-icon-trailing-space',
  // go/keep-sorted end
);

$unsupported-tokens: (
  // go/keep-sorted start
  'focus-state-layer-color',
  'focus-state-layer-opacity',
  'label-text-tracking',
  'label-text-type',
  // go/keep-sorted end
);

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $tokens: md-comp-elevated-button.values($deps, $exclude-hardcoded-values);
  $new-tokens: map.merge(
    shape.get-new-logical-shape-tokens($tokens, 'container-shape'),
    (
      // b/198759625 - Remove once spacing tokens are formally added
      // go/keep-sorted start
      'leading-space': if($exclude-hardcoded-values, null, 24px),
      'trailing-space': if($exclude-hardcoded-values, null, 24px),
      'with-leading-icon-leading-space':
        if($exclude-hardcoded-values, null, 16px),
      'with-leading-icon-trailing-space':
        if($exclude-hardcoded-values, null, 24px),
      'with-trailing-icon-leading-space':
        if($exclude-hardcoded-values, null, 24px),
      'with-trailing-icon-trailing-space':
        if($exclude-hardcoded-values, null, 16px),
      // go/keep-sorted end
    )
  );

  $tokens: validate.values(
    $tokens,
    $supported-tokens: $supported-tokens,
    $unsupported-tokens: $unsupported-tokens,
    $new-tokens: $new-tokens,
    $renamed-tokens: (
      // Remove "with-*" prefixes (b/273534858)
      'with-icon-disabled-icon-color': 'disabled-icon-color',
      'with-icon-disabled-icon-opacity': 'disabled-icon-opacity',
      'with-icon-focus-icon-color': 'focus-icon-color',
      'with-icon-hover-icon-color': 'hover-icon-color',
      'with-icon-icon-color': 'icon-color',
      'with-icon-icon-size': 'icon-size',
      'with-icon-pressed-icon-color': 'pressed-icon-color'
    )
  );

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      @if string.index($token, 'container-shape-') == 1 {
        // Add fallback to shorthand for logical shape properties.
        $value: var(--md-elevated-button-container-shape, #{$value});
      }

      $tokens: map.set(
        $tokens,
        $token,
        var(--md-elevated-button-#{$token}, #{$value})
      );
    }
  }

  @return $tokens;
}
