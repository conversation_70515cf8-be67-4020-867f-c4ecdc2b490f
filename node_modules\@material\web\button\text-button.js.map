{"version": 3, "file": "text-button.js", "sourceRoot": "", "sources": ["text-button.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AACnE,OAAO,EAAC,UAAU,EAAC,MAAM,2BAA2B,CAAC;AACrD,OAAO,EAAC,MAAM,IAAI,UAAU,EAAC,MAAM,2BAA2B,CAAC;AAQ/D;;;;;;;;;;;;;;;;;;;GAmBG;AAEI,IAAM,YAAY,GAAlB,MAAM,YAAa,SAAQ,UAAU;;AAC1B,mBAAM,GAAwB,CAAC,YAAY,EAAE,UAAU,CAAC,AAAlD,CAAmD;AAD9D,YAAY;IADxB,aAAa,CAAC,gBAAgB,CAAC;GACnB,YAAY,CAExB", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {styles as sharedStyles} from './internal/shared-styles.js';\nimport {TextButton} from './internal/text-button.js';\nimport {styles as textStyles} from './internal/text-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-text-button': MdTextButton;\n  }\n}\n\n/**\n * @summary Buttons help people take action, such as sending an email, sharing a\n * document, or liking a comment.\n *\n * @description\n * __Emphasis:__ Low emphasis – For optional or supplementary actions with the\n * least amount of prominence\n *\n * __Rationale:__ Text buttons have less visual prominence, so should be used\n * for low emphasis actions, such as an alternative option.\n *\n * __Example usages:__\n * - Learn more\n * - View all\n * - Change account\n * - Turn on\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-text-button')\nexport class MdTextButton extends TextButton {\n  static override styles: CSSResultOrNative[] = [sharedStyles, textStyles];\n}\n"]}