{"version": 3, "file": "typeaheadController.js", "sourceRoot": "", "sources": ["typeaheadController.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AA8BH;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG;IAC9B,KAAK,EAAE,CAAC;IACR,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;CACC,CAAC;AAEX;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAM,OAAO,mBAAmB;IAsB9B;;;;;;;;;OASG;IACH,YACmB,aAAkD;QAAlD,kBAAa,GAAb,aAAa,CAAqC;QAhCrE;;WAEG;QACK,qBAAgB,GAAsB,EAAE,CAAC;QACjD;;WAEG;QACK,mBAAc,GAAG,EAAE,CAAC;QAC5B;;WAEG;QACK,2BAAsB,GAAG,CAAC,CAAC;QACnC;;WAEG;QACH,kBAAa,GAAG,KAAK,CAAC;QACtB;;WAEG;QACH,qBAAgB,GAA2B,IAAI,CAAC;QAwBhD;;;;;WAKG;QACM,cAAS,GAAG,CAAC,KAAoB,EAAE,EAAE;YAC5C,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC;QAsLF;;WAEG;QACc,iBAAY,GAAG,GAAG,EAAE;YACnC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;YACzB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC7B,CAAC,CAAC;IAnNC,CAAC;IAEJ,IAAY,KAAK;QACf,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,QAAQ,EAAE,CAAC;IACzC,CAAC;IAED,IAAY,MAAM;QAChB,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC;IACrC,CAAC;IAgBD;;OAEG;IACK,cAAc,CAAC,KAAoB;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QAED,2EAA2E;QAC3E,yEAAyE;QACzE,wBAAwB;QACxB,IACE,KAAK,CAAC,IAAI,KAAK,OAAO;YACtB,KAAK,CAAC,IAAI,KAAK,OAAO;YACtB,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YAC9B,KAAK,CAAC,IAAI,KAAK,QAAQ,EACvB,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,4EAA4E;QAC5E,2BAA2B;QAC3B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC;YACpD,KAAK;YACL,EAAE;YACF,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE;SACtC,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB;YACnB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CACxB,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,KAAK,CAAC,CACzD,IAAI,IAAI,CAAC;QACZ,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IACK,SAAS,CAAC,KAAoB;QACpC,IAAI,KAAK,CAAC,gBAAgB;YAAE,OAAO;QACnC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC1C,0EAA0E;QAC1E,qBAAqB;QACrB,IACE,KAAK,CAAC,IAAI,KAAK,OAAO;YACtB,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YAC9B,KAAK,CAAC,IAAI,KAAK,QAAQ,EACvB,CAAC;YACD,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;YAC7D,CAAC;YACD,OAAO;QACT,CAAC;QAED,sEAAsE;QACtE,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC3B,KAAK,CAAC,cAAc,EAAE,CAAC;QACzB,CAAC;QAED,0CAA0C;QAC1C,IAAI,CAAC,sBAAsB,GAAG,UAAU,CACtC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,aAAa,EAAE,CAAC,mBAAmB,CACzC,CAAC;QAEF,IAAI,CAAC,cAAc,IAAI,KAAK,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;QAE/C,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB;YAC3C,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,CAAC;YAC/C,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;QAEhD;;;;;;;;;;;;;;;;;;;;;WAqBG;QACH,MAAM,mBAAmB,GAAG,CAAC,MAAuB,EAAE,EAAE;YACtD,OAAO,CACL,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,eAAe,CAAC;gBAC/D,UAAU,CACX,CAAC;QACJ,CAAC,CAAC;QAEF,qEAAqE;QACrE,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB;aAC1C,MAAM,CACL,CAAC,MAAM,EAAE,EAAE,CACT,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ;YACvC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAChE;aACA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;QAEnE,sEAAsE;QACtE,0EAA0E;QAC1E,+CAA+C;QAC/C,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1C,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;YAC7D,CAAC;YACD,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,CAAC;QACpD,IAAI,UAA2B,CAAC;QAEhC,4EAA4E;QAC5E,0CAA0C;QAC1C,IAAI,IAAI,CAAC,gBAAgB,KAAK,eAAe,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE,CAAC;YAC/D,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC;QACnC,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;QAC/C,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;QAC1C,OAAO;IACT,CAAC;CAUF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {MenuItem} from './menuItemController.js';\n\n/**\n * The options that are passed to the typeahead controller.\n */\nexport interface TypeaheadControllerProperties {\n  /**\n   * A function that returns an array of menu items to be searched.\n   * @return An array of menu items to be searched by typing.\n   */\n  getItems: () => MenuItem[];\n  /**\n   * The maximum time between each keystroke to keep the current type buffer\n   * alive.\n   */\n  typeaheadBufferTime: number;\n  /**\n   * Whether or not the typeahead should listen for keystrokes or not.\n   */\n  active: boolean;\n}\n\n/**\n * Data structure tuple that helps with indexing.\n *\n * [index, item, normalized header text]\n */\ntype TypeaheadRecord = [number, MenuItem, string];\n/**\n * Indicies to access the TypeaheadRecord tuple type.\n */\nexport const TYPEAHEAD_RECORD = {\n  INDEX: 0,\n  ITEM: 1,\n  TEXT: 2,\n} as const;\n\n/**\n * This controller listens to `keydown` events and searches the header text of\n * an array of `MenuItem`s with the corresponding entered keys within the buffer\n * time and activates the item.\n *\n * @example\n * ```ts\n * const typeaheadController = new TypeaheadController(() => ({\n *   typeaheadBufferTime: 50,\n *   getItems: () => Array.from(document.querySelectorAll('md-menu-item'))\n * }));\n * html`\n *   <div\n *       @keydown=${typeaheadController.onKeydown}\n *       tabindex=\"0\"\n *       class=\"activeItemText\">\n *     <!-- focusable element that will receive keydown events -->\n *     Apple\n *   </div>\n *   <div>\n *     <md-menu-item active header=\"Apple\"></md-menu-item>\n *     <md-menu-item header=\"Apricot\"></md-menu-item>\n *     <md-menu-item header=\"Banana\"></md-menu-item>\n *     <md-menu-item header=\"Olive\"></md-menu-item>\n *     <md-menu-item header=\"Orange\"></md-menu-item>\n *   </div>\n * `;\n * ```\n */\nexport class TypeaheadController {\n  /**\n   * Array of tuples that helps with indexing.\n   */\n  private typeaheadRecords: TypeaheadRecord[] = [];\n  /**\n   * Currently-typed text since last buffer timeout\n   */\n  private typaheadBuffer = '';\n  /**\n   * The timeout id from the current buffer's setTimeout\n   */\n  private cancelTypeaheadTimeout = 0;\n  /**\n   * If we are currently \"typing\"\n   */\n  isTypingAhead = false;\n  /**\n   * The record of the last active item.\n   */\n  lastActiveRecord: TypeaheadRecord | null = null;\n\n  /**\n   * @param getProperties A function that returns the options of the typeahead\n   * controller:\n   *\n   * {\n   *   getItems: A function that returns an array of menu items to be searched.\n   *   typeaheadBufferTime: The maximum time between each keystroke to keep the\n   *       current type buffer alive.\n   * }\n   */\n  constructor(\n    private readonly getProperties: () => TypeaheadControllerProperties,\n  ) {}\n\n  private get items() {\n    return this.getProperties().getItems();\n  }\n\n  private get active() {\n    return this.getProperties().active;\n  }\n\n  /**\n   * Apply this listener to the element that will receive `keydown` events that\n   * should trigger this controller.\n   *\n   * @param event The native browser `KeyboardEvent` from the `keydown` event.\n   */\n  readonly onKeydown = (event: KeyboardEvent) => {\n    if (this.isTypingAhead) {\n      this.typeahead(event);\n    } else {\n      this.beginTypeahead(event);\n    }\n  };\n\n  /**\n   * Sets up typingahead\n   */\n  private beginTypeahead(event: KeyboardEvent) {\n    if (!this.active) {\n      return;\n    }\n\n    // We don't want to typeahead if the _beginning_ of the typeahead is a menu\n    // navigation, or a selection. We will handle \"Space\" only if it's in the\n    // middle of a typeahead\n    if (\n      event.code === 'Space' ||\n      event.code === 'Enter' ||\n      event.code.startsWith('Arrow') ||\n      event.code === 'Escape'\n    ) {\n      return;\n    }\n\n    this.isTypingAhead = true;\n    // Generates the record array data structure which is the index, the element\n    // and a normalized header.\n    this.typeaheadRecords = this.items.map((el, index) => [\n      index,\n      el,\n      el.typeaheadText.trim().toLowerCase(),\n    ]);\n    this.lastActiveRecord =\n      this.typeaheadRecords.find(\n        (record) => record[TYPEAHEAD_RECORD.ITEM].tabIndex === 0,\n      ) ?? null;\n    if (this.lastActiveRecord) {\n      this.lastActiveRecord[TYPEAHEAD_RECORD.ITEM].tabIndex = -1;\n    }\n    this.typeahead(event);\n  }\n\n  /**\n   * Performs the typeahead. Based on the normalized items and the current text\n   * buffer, finds the _next_ item with matching text and activates it.\n   *\n   * @example\n   *\n   * items: Apple, Banana, Olive, Orange, Cucumber\n   * buffer: ''\n   * user types: o\n   *\n   * activates Olive\n   *\n   * @example\n   *\n   * items: Apple, Banana, Olive (active), Orange, Cucumber\n   * buffer: 'o'\n   * user types: l\n   *\n   * activates Olive\n   *\n   * @example\n   *\n   * items: Apple, Banana, Olive (active), Orange, Cucumber\n   * buffer: ''\n   * user types: o\n   *\n   * activates Orange\n   *\n   * @example\n   *\n   * items: Apple, Banana, Olive, Orange (active), Cucumber\n   * buffer: ''\n   * user types: o\n   *\n   * activates Olive\n   */\n  private typeahead(event: KeyboardEvent) {\n    if (event.defaultPrevented) return;\n    clearTimeout(this.cancelTypeaheadTimeout);\n    // Stop typingahead if one of the navigation or selection keys (except for\n    // Space) are pressed\n    if (\n      event.code === 'Enter' ||\n      event.code.startsWith('Arrow') ||\n      event.code === 'Escape'\n    ) {\n      this.endTypeahead();\n      if (this.lastActiveRecord) {\n        this.lastActiveRecord[TYPEAHEAD_RECORD.ITEM].tabIndex = -1;\n      }\n      return;\n    }\n\n    // If Space is pressed, prevent it from selecting and closing the menu\n    if (event.code === 'Space') {\n      event.preventDefault();\n    }\n\n    // Start up a new keystroke buffer timeout\n    this.cancelTypeaheadTimeout = setTimeout(\n      this.endTypeahead,\n      this.getProperties().typeaheadBufferTime,\n    );\n\n    this.typaheadBuffer += event.key.toLowerCase();\n\n    const lastActiveIndex = this.lastActiveRecord\n      ? this.lastActiveRecord[TYPEAHEAD_RECORD.INDEX]\n      : -1;\n    const numRecords = this.typeaheadRecords.length;\n\n    /**\n     * Sorting function that will resort the items starting with the given index\n     *\n     * @example\n     *\n     * this.typeaheadRecords =\n     * 0: [0, <reference>, 'apple']\n     * 1: [1, <reference>, 'apricot']\n     * 2: [2, <reference>, 'banana']\n     * 3: [3, <reference>, 'olive'] <-- lastActiveIndex\n     * 4: [4, <reference>, 'orange']\n     * 5: [5, <reference>, 'strawberry']\n     *\n     * this.typeaheadRecords.sort((a,b) => rebaseIndexOnActive(a)\n     *                                       - rebaseIndexOnActive(b)) ===\n     * 0: [3, <reference>, 'olive'] <-- lastActiveIndex\n     * 1: [4, <reference>, 'orange']\n     * 2: [5, <reference>, 'strawberry']\n     * 3: [0, <reference>, 'apple']\n     * 4: [1, <reference>, 'apricot']\n     * 5: [2, <reference>, 'banana']\n     */\n    const rebaseIndexOnActive = (record: TypeaheadRecord) => {\n      return (\n        (record[TYPEAHEAD_RECORD.INDEX] + numRecords - lastActiveIndex) %\n        numRecords\n      );\n    };\n\n    // records filtered and sorted / rebased around the last active index\n    const matchingRecords = this.typeaheadRecords\n      .filter(\n        (record) =>\n          !record[TYPEAHEAD_RECORD.ITEM].disabled &&\n          record[TYPEAHEAD_RECORD.TEXT].startsWith(this.typaheadBuffer),\n      )\n      .sort((a, b) => rebaseIndexOnActive(a) - rebaseIndexOnActive(b));\n\n    // Just leave if there's nothing that matches. Native select will just\n    // choose the first thing that starts with the next letter in the alphabet\n    // but that's out of scope and hard to localize\n    if (matchingRecords.length === 0) {\n      clearTimeout(this.cancelTypeaheadTimeout);\n      if (this.lastActiveRecord) {\n        this.lastActiveRecord[TYPEAHEAD_RECORD.ITEM].tabIndex = -1;\n      }\n      this.endTypeahead();\n      return;\n    }\n\n    const isNewQuery = this.typaheadBuffer.length === 1;\n    let nextRecord: TypeaheadRecord;\n\n    // This is likely the case that someone is trying to \"tab\" through different\n    // entries that start with the same letter\n    if (this.lastActiveRecord === matchingRecords[0] && isNewQuery) {\n      nextRecord = matchingRecords[1] ?? matchingRecords[0];\n    } else {\n      nextRecord = matchingRecords[0];\n    }\n\n    if (this.lastActiveRecord) {\n      this.lastActiveRecord[TYPEAHEAD_RECORD.ITEM].tabIndex = -1;\n    }\n\n    this.lastActiveRecord = nextRecord;\n    nextRecord[TYPEAHEAD_RECORD.ITEM].tabIndex = 0;\n    nextRecord[TYPEAHEAD_RECORD.ITEM].focus();\n    return;\n  }\n\n  /**\n   * Ends the current typeahead and clears the buffer.\n   */\n  private readonly endTypeahead = () => {\n    this.isTypingAhead = false;\n    this.typaheadBuffer = '';\n    this.typeaheadRecords = [];\n  };\n}\n"]}