//
// Copyright 2022 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use '../../../tokens';
// go/keep-sorted end

@mixin theme($tokens) {
  $supported-tokens: list.join(
    tokens.$md-comp-outlined-segmented-button-supported-tokens,
    ('shape-start-start', 'shape-start-end', 'shape-end-end', 'shape-end-start')
  );

  @each $token, $value in $tokens {
    @if list.index($supported-tokens, $token) == null {
      @error 'Token `#{$token}` is not a supported token.';
    }

    @if $value {
      --md-outlined-segmented-button-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $tokens: tokens.md-comp-outlined-segmented-button-values();

  :host {
    // Only use the logical properties.
    $tokens: map.remove($tokens, 'shape');
    @each $token, $value in $tokens {
      --_#{$token}: #{$value};
    }
  }
}
