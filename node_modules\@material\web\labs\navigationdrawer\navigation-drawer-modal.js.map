{"version": 3, "file": "navigation-drawer-modal.js", "sourceRoot": "", "sources": ["navigation-drawer-modal.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,qBAAqB,EAAC,MAAM,uCAAuC,CAAC;AAC5E,OAAO,EAAC,MAAM,EAAC,MAAM,8CAA8C,CAAC;AACpE,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAQnE;;;GAGG;AAEI,IAAM,uBAAuB,GAA7B,MAAM,uBAAwB,SAAQ,qBAAqB;;AACvC,8BAAM,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,AAAzB,CAA0B;AAD9C,uBAAuB;IADnC,aAAa,CAAC,4BAA4B,CAAC;GAC/B,uBAAuB,CAEnC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {customElement} from 'lit/decorators.js';\n\nimport {NavigationDrawerModal} from './internal/navigation-drawer-modal.js';\nimport {styles} from './internal/navigation-drawer-modal-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-navigation-drawer-modal': MdNavigationDrawerModal;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-navigation-drawer-modal')\nexport class MdNavigationDrawerModal extends NavigationDrawerModal {\n  static override readonly styles = [sharedStyles, styles];\n}\n"]}