{"version": 3, "file": "progress.js", "sourceRoot": "", "sources": ["progress.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,EAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAiB,MAAM,KAAK,CAAC;AAC9D,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAGrD,OAAO,EAAC,kBAAkB,EAAC,MAAM,iCAAiC,CAAC;AAEnE,wCAAwC;AACxC,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAEzD;;GAEG;AACH,MAAM,OAAgB,QAAS,SAAQ,iBAAiB;IAAxD;;QACE;;WAEG;QACuB,UAAK,GAAG,CAAC,CAAC;QAEpC;;WAEG;QACuB,QAAG,GAAG,CAAC,CAAC;QAElC;;;WAGG;QACwB,kBAAa,GAAG,KAAK,CAAC;QAEjD;;WAEG;QACiD,cAAS,GAAG,KAAK,CAAC;IA0BxE,CAAC;IAxBoB,MAAM;QACvB,iCAAiC;QACjC,MAAM,EAAC,SAAS,EAAC,GAAG,IAAuB,CAAC;QAC5C,OAAO,IAAI,CAAA;;0BAEW,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;;sBAErC,SAAS,IAAI,OAAO;;wBAElB,IAAI,CAAC,GAAG;wBACR,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK;WACtD,IAAI,CAAC,eAAe,EAAE;;KAE5B,CAAC;IACJ,CAAC;IAES,gBAAgB;QACxB,OAAO;YACL,eAAe,EAAE,IAAI,CAAC,aAAa;YACnC,YAAY,EAAE,IAAI,CAAC,SAAS;SAC7B,CAAC;IACJ,CAAC;CAGF;AA1C2B;IAAzB,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;uCAAW;AAKV;IAAzB,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;qCAAS;AAMP;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;+CAAuB;AAKG;IAAnD,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAC,CAAC;2CAAmB", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, LitElement, nothing, TemplateResult} from 'lit';\nimport {property} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\n\n// Separate variable needed for closure.\nconst progressBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * A progress component.\n */\nexport abstract class Progress extends progressBaseClass {\n  /**\n   * Progress to display, a fraction between 0 and `max`.\n   */\n  @property({type: Number}) value = 0;\n\n  /**\n   * Maximum progress to display, defaults to 1.\n   */\n  @property({type: Number}) max = 1;\n\n  /**\n   * Whether or not to display indeterminate progress, which gives no indication\n   * to how long an activity will take.\n   */\n  @property({type: Boolean}) indeterminate = false;\n\n  /**\n   * Whether or not to render indeterminate mode using 4 colors instead of one.\n   */\n  @property({type: Boolean, attribute: 'four-color'}) fourColor = false;\n\n  protected override render() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`\n      <div\n        class=\"progress ${classMap(this.getRenderClasses())}\"\n        role=\"progressbar\"\n        aria-label=\"${ariaLabel || nothing}\"\n        aria-valuemin=\"0\"\n        aria-valuemax=${this.max}\n        aria-valuenow=${this.indeterminate ? nothing : this.value}\n        >${this.renderIndicator()}</div\n      >\n    `;\n  }\n\n  protected getRenderClasses() {\n    return {\n      'indeterminate': this.indeterminate,\n      'four-color': this.fourColor,\n    };\n  }\n\n  protected abstract renderIndicator(): TemplateResult;\n}\n"]}