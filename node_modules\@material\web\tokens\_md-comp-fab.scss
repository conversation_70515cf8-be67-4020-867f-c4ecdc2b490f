//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
@use 'sass:string';
// go/keep-sorted end
// go/keep-sorted start
@use './internal/shape';
@use './internal/validate';
@use './md-sys-color';
@use './md-sys-elevation';
@use './md-sys-shape';
@use './md-sys-state';
@use './md-sys-typescale';
@use './v0_192/md-comp-extended-fab-primary';
@use './v0_192/md-comp-extended-fab-secondary';
@use './v0_192/md-comp-extended-fab-surface';
@use './v0_192/md-comp-extended-fab-tertiary';
@use './v0_192/md-comp-fab-primary';
@use './v0_192/md-comp-fab-secondary';
@use './v0_192/md-comp-fab-surface';
@use './v0_192/md-comp-fab-surface-large';
@use './v0_192/md-comp-fab-surface-small';
@use './v0_192/md-comp-fab-tertiary';
// go/keep-sorted end

$supported-tokens: (
  // go/keep-sorted start
  'container-color',
  'container-elevation',
  'container-height',
  'container-shadow-color',
  'container-shape',
  'container-shape-end-end',
  'container-shape-end-start',
  'container-shape-start-end',
  'container-shape-start-start',
  'container-width',
  'focus-container-elevation',
  'focus-icon-color',
  'focus-label-text-color',
  'hover-container-elevation',
  'hover-icon-color',
  'hover-label-text-color',
  'hover-state-layer-color',
  'hover-state-layer-opacity',
  'icon-color',
  'icon-size',
  'label-text-color',
  'label-text-font',
  'label-text-line-height',
  'label-text-size',
  'label-text-weight',
  'large-container-height',
  'large-container-shape',
  'large-container-shape-end-end',
  'large-container-shape-end-start',
  'large-container-shape-start-end',
  'large-container-shape-start-start',
  'large-container-width',
  'large-icon-size',
  'lowered-container-color',
  'lowered-container-elevation',
  'lowered-focus-container-elevation',
  'lowered-hover-container-elevation',
  'lowered-pressed-container-elevation',
  'pressed-container-elevation',
  'pressed-icon-color',
  'pressed-label-text-color',
  'pressed-state-layer-color',
  'pressed-state-layer-opacity',
  'primary-container-color',
  'primary-focus-icon-color',
  'primary-focus-label-text-color',
  'primary-hover-icon-color',
  'primary-hover-label-text-color',
  'primary-hover-state-layer-color',
  'primary-icon-color',
  'primary-label-text-color',
  'primary-pressed-icon-color',
  'primary-pressed-label-text-color',
  'primary-pressed-state-layer-color',
  'secondary-container-color',
  'secondary-focus-icon-color',
  'secondary-focus-label-text-color',
  'secondary-hover-icon-color',
  'secondary-hover-label-text-color',
  'secondary-hover-state-layer-color',
  'secondary-icon-color',
  'secondary-label-text-color',
  'secondary-pressed-icon-color',
  'secondary-pressed-label-text-color',
  'secondary-pressed-state-layer-color',
  'small-container-height',
  'small-container-shape',
  'small-container-shape-end-end',
  'small-container-shape-end-start',
  'small-container-shape-start-end',
  'small-container-shape-start-start',
  'small-container-width',
  'small-icon-size',
  'tertiary-container-color',
  'tertiary-focus-icon-color',
  'tertiary-focus-label-text-color',
  'tertiary-hover-icon-color',
  'tertiary-hover-label-text-color',
  'tertiary-hover-state-layer-color',
  'tertiary-icon-color',
  'tertiary-label-text-color',
  'tertiary-pressed-icon-color',
  'tertiary-pressed-label-text-color',
  'tertiary-pressed-state-layer-color',
  // go/keep-sorted end
);

$unsupported-tokens: (
  // go/keep-sorted start
  'focus-state-layer-color',
  'focus-state-layer-opacity',
  // go/keep-sorted end
);

@function _get-new-tokens($deps, $exclude-hardcoded-values) {
  $large-tokens: md-comp-fab-surface-large.values(
    $deps,
    $exclude-hardcoded-values
  );
  $small-tokens: md-comp-fab-surface-small.values(
    $deps,
    $exclude-hardcoded-values
  );
  $primary-tokens: md-comp-fab-primary.values($deps, $exclude-hardcoded-values);
  $secondary-tokens: md-comp-fab-secondary.values(
    $deps,
    $exclude-hardcoded-values
  );
  $tertiary-tokens: md-comp-fab-tertiary.values(
    $deps,
    $exclude-hardcoded-values
  );
  $extended-surface-tokens: md-comp-extended-fab-surface.values(
    $deps,
    $exclude-hardcoded-values
  );
  $extended-primary-tokens: md-comp-extended-fab-primary.values(
    $deps,
    $exclude-hardcoded-values
  );
  $extended-secondary-tokens: md-comp-extended-fab-secondary.values(
    $deps,
    $exclude-hardcoded-values
  );
  $extended-tertiary-tokens: md-comp-extended-fab-tertiary.values(
    $deps,
    $exclude-hardcoded-values
  );

  @return (
    // go/keep-sorted start
    'focus-label-text-color':
      map.get($extended-surface-tokens, 'focus-label-text-color'),
    'hover-label-text-color':
      map.get($extended-surface-tokens, 'hover-label-text-color'),
    'label-text-color': map.get($extended-surface-tokens, 'label-text-color'),
    'label-text-font': map.get($extended-surface-tokens, 'label-text-font'),
    'label-text-line-height':
      map.get($extended-surface-tokens, 'label-text-line-height'),
    'label-text-size': map.get($extended-surface-tokens, 'label-text-size'),
    'label-text-weight': map.get($extended-surface-tokens, 'label-text-weight'),
    'large-container-height': map.get($large-tokens, 'container-height'),
    'large-container-shape': map.get($large-tokens, 'container-shape'),
    'large-container-width': map.get($large-tokens, 'container-width'),
    'large-icon-size': map.get($large-tokens, 'icon-size'),
    'pressed-label-text-color':
      map.get($extended-surface-tokens, 'pressed-label-text-color'),
    'primary-container-color': map.get($primary-tokens, 'container-color'),
    'primary-focus-icon-color': map.get($primary-tokens, 'focus-icon-color'),
    'primary-focus-label-text-color':
      map.get($extended-primary-tokens, 'focus-label-text-color'),
    'primary-hover-icon-color': map.get($primary-tokens, 'hover-icon-color'),
    'primary-hover-label-text-color':
      map.get($extended-primary-tokens, 'hover-label-text-color'),
    'primary-hover-state-layer-color':
      map.get($primary-tokens, 'hover-state-layer-color'),
    'primary-icon-color': map.get($primary-tokens, 'icon-color'),
    'primary-label-text-color':
      map.get($extended-primary-tokens, 'label-text-color'),
    'primary-pressed-icon-color': map.get($primary-tokens, 'pressed-icon-color'),
    'primary-pressed-label-text-color':
      map.get($extended-primary-tokens, 'pressed-label-text-color'),
    'primary-pressed-state-layer-color':
      map.get($primary-tokens, 'pressed-state-layer-color'),
    'secondary-container-color': map.get($secondary-tokens, 'container-color'),
    'secondary-focus-icon-color': map.get($secondary-tokens, 'focus-icon-color'),
    'secondary-focus-label-text-color':
      map.get($extended-secondary-tokens, 'focus-label-text-color'),
    'secondary-hover-icon-color': map.get($secondary-tokens, 'hover-icon-color'),
    'secondary-hover-label-text-color':
      map.get($extended-secondary-tokens, 'hover-label-text-color'),
    'secondary-hover-state-layer-color':
      map.get($secondary-tokens, 'hover-state-layer-color'),
    'secondary-icon-color': map.get($secondary-tokens, 'icon-color'),
    'secondary-label-text-color':
      map.get($extended-secondary-tokens, 'label-text-color'),
    'secondary-pressed-icon-color':
      map.get($secondary-tokens, 'pressed-icon-color'),
    'secondary-pressed-label-text-color':
      map.get($extended-secondary-tokens, 'pressed-label-text-color'),
    'secondary-pressed-state-layer-color':
      map.get($secondary-tokens, 'pressed-state-layer-color'),
    'small-container-height': map.get($small-tokens, 'container-height'),
    'small-container-shape': map.get($small-tokens, 'container-shape'),
    'small-container-width': map.get($small-tokens, 'container-width'),
    'small-icon-size': map.get($small-tokens, 'icon-size'),
    'tertiary-container-color': map.get($tertiary-tokens, 'container-color'),
    'tertiary-focus-icon-color': map.get($tertiary-tokens, 'focus-icon-color'),
    'tertiary-focus-label-text-color':
      map.get($extended-tertiary-tokens, 'focus-label-text-color'),
    'tertiary-hover-icon-color': map.get($tertiary-tokens, 'hover-icon-color'),
    'tertiary-hover-label-text-color':
      map.get($extended-tertiary-tokens, 'hover-label-text-color'),
    'tertiary-hover-state-layer-color':
      map.get($tertiary-tokens, 'hover-state-layer-color'),
    'tertiary-icon-color': map.get($tertiary-tokens, 'icon-color'),
    'tertiary-label-text-color':
      map.get($extended-tertiary-tokens, 'label-text-color'),
    'tertiary-pressed-icon-color':
      map.get($tertiary-tokens, 'pressed-icon-color'),
    'tertiary-pressed-label-text-color':
      map.get($extended-tertiary-tokens, 'pressed-label-text-color'),
    'tertiary-pressed-state-layer-color':
      map.get($tertiary-tokens, 'pressed-state-layer-color'),
    // go/keep-sorted end
  );
}

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $tokens: md-comp-fab-surface.values($deps, $exclude-hardcoded-values);
  $new-tokens: _get-new-tokens($deps, $exclude-hardcoded-values);
  $new-tokens: map.merge(
    $new-tokens,
    shape.get-new-logical-shape-tokens($tokens, 'container-shape')
  );

  $new-tokens: map.merge(
    $new-tokens,
    shape.get-new-logical-shape-tokens(
      $new-tokens,
      'large-container-shape',
      'small-container-shape'
    )
  );

  $tokens: validate.values(
    $tokens,
    $supported-tokens: $supported-tokens,
    $unsupported-tokens: $unsupported-tokens,
    $new-tokens: $new-tokens
  );

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      $shape-tokens: (
        'container-shape',
        'large-container-shape',
        'small-container-shape'
      );

      @each $shape-token in $shape-tokens {
        @if string.index($token, '#{$shape-token}-') == 1 {
          // Add fallback to shorthand for logical shape properties.
          $value: var(--md-fab-#{$shape-token}, #{$value});
        }
      }

      $tokens: map.set($tokens, $token, var(--md-fab-#{$token}, #{$value}));
    }
  }

  @return $tokens;
}
