{"version": 3, "file": "item.js", "sourceRoot": "", "sources": ["item.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,IAAI,EAAC,MAAM,oBAAoB,CAAC;AACxC,OAAO,EAAC,MAAM,EAAC,MAAM,2BAA2B,CAAC;AAQjD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0DG;AAEI,IAAM,MAAM,GAAZ,MAAM,MAAO,SAAQ,IAAI;;AACd,aAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAD5C,MAAM;IADlB,aAAa,CAAC,SAAS,CAAC;GACZ,MAAM,CAElB", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Item} from './internal/item.js';\nimport {styles} from './internal/item-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-item': MdItem;\n  }\n}\n\n/**\n * An item layout component that can be used inside list items to give them\n * their customizable structure.\n *\n * `<md-item>` does not have any functionality, which must be added by the\n * component using it.\n *\n * All text will wrap unless `white-space: nowrap` is set on the item or any of\n * its children.\n *\n * Slots available:\n * - `<default>`: The headline, or custom content.\n * - `headline`: The first line.\n * - `supporting-text`: Supporting text lines underneath the headline.\n * - `trailing-supporting-text`: A small text snippet at the end of the item.\n * - `start`: Any leading content, such as icons, avatars, or checkboxes.\n * - `end`: Any trailing content, such as icons and buttons.\n * - `container`: Background container content, intended for adding additional\n *     styles, such as ripples or focus rings.\n *\n * @example\n * ```html\n * <md-item>Single line</md-item>\n *\n * <md-item>\n *   <div class=\"custom-content\">...</div>\n * </md-item>\n *\n * <!-- Classic 1 to 3+ line list items -->\n * <md-item>\n *   <md-icon slot=\"start\">image</md-icon>\n *   <div slot=\"overline\">Overline</div>\n *   <div slot=\"headline\">Headline</div>\n *   <div=\"supporting-text\">Supporting text</div>\n *   <div=\"trailing-supporting-text\">Trailing</div>\n *   <md-icon slot=\"end\">image</md-icon>\n * </md-item>\n * ```\n *\n * When wrapping `<md-item>`, forward the available slots to use the same slot\n * structure for the wrapping component (this is what `<md-list-item>` does).\n *\n * @example\n * ```html\n * <md-item>\n *   <slot></slot>\n *   <slot name=\"overline\" slot=\"overline\"></slot>\n *   <slot name=\"headline\" slot=\"headline\"></slot>\n *   <slot name=\"supporting-text\" slot=\"supporting-text\"></slot>\n *   <slot name=\"trailing-supporting-text\"\n *       slot=\"trailing-supporting-text\"></slot>\n *   <slot name=\"start\" slot=\"start\"></slot>\n *   <slot name=\"end\" slot=\"end\"></slot>\n * </md-item>\n * ```\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-item')\nexport class MdItem extends Item {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}