{"version": 3, "file": "menu.js", "sourceRoot": "", "sources": ["menu.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,8BAA8B,CAAC;AACtC,OAAO,8BAA8B,CAAC;AAEtC,OAAO,EAAC,UAAU,EAAkB,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAC,MAAM,KAAK,CAAC;AACxE,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAE,qBAAqB,EAAE,KAAK,EAAC,MAAM,mBAAmB,CAAC;AAChF,OAAO,EAAY,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAChE,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAErD,OAAO,EAAC,MAAM,EAAE,qBAAqB,EAAC,MAAM,oCAAoC,CAAC;AACjF,OAAO,EACL,cAAc,EACd,aAAa,GACd,MAAM,wCAAwC,CAAC;AAChD,OAAO,EACL,aAAa,EACb,uBAAuB,EACvB,sBAAsB,GACvB,MAAM,gDAAgD,CAAC;AAGxD,OAAO,EAGL,UAAU,EACV,aAAa,EACb,kBAAkB,GACnB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EACL,MAAM,EACN,yBAAyB,GAE1B,MAAM,4CAA4C,CAAC;AACpD,OAAO,EAAC,mBAAmB,EAAC,MAAM,sCAAsC,CAAC;AAEzE,OAAO,EAAC,MAAM,EAAC,MAAM,4CAA4C,CAAC;AAElE;;GAEG;AACH,MAAM,CAAC,MAAM,6BAA6B,GAAG,GAAG,CAAC;AAEjD,MAAM,cAAc,GAAG,IAAI,GAAG,CAAS;IACrC,aAAa,CAAC,SAAS;IACvB,aAAa,CAAC,OAAO;IACrB,aAAa,CAAC,IAAI;IAClB,aAAa,CAAC,GAAG;CAClB,CAAC,CAAC;AAEH,MAAM,WAAW,GAAG,IAAI,GAAG,CAAS;IAClC,aAAa,CAAC,SAAS;IACvB,aAAa,CAAC,UAAU;IACxB,GAAG,cAAc;CAClB,CAAC,CAAC;AAEH;;;;;;GAMG;AACH,SAAS,iBAAiB,CACxB,YAAmC,QAAQ;IAE3C,IAAI,QAAQ,GAAG,SAAS,CAAC,aAAmC,CAAC;IAE7D,8EAA8E;IAC9E,wBAAwB;IACxB,OAAO,QAAQ,IAAI,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;QACvD,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,aAAmC,CAAC;IACrE,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;GAKG;AACH,MAAM,OAAgB,IAAK,SAAQ,UAAU;IA8N3C;;;OAGG;IACH,IAAY,aAAa;QACvB,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,OAAO,eAAe,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;IACrD,CAAC;IAoBD;;;;OAIG;IACH,IAAI,aAAa;QACf,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,OAAQ,IAAI,CAAC,WAAW,EAA4B,CAAC,aAAa,CAChE,IAAI,IAAI,CAAC,MAAM,EAAE,CAClB,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IAED,IAAI,aAAa,CACf,OAA8D;QAE9D,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QACpC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;IACtC,CAAC;IAMD;QACE,KAAK,EAAE,CAAC;QAhRV;;;;;;WAMG;QACS,WAAM,GAAG,EAAE,CAAC;QACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;WAqCG;QACS,gBAAW,GACrB,UAAU,CAAC;QACb;;WAEG;QACwB,UAAK,GAAG,KAAK,CAAC;QACzC;;;;;;;WAOG;QACmD,gBAAW,GAAG,KAAK,CAAC;QAC1E;;;WAGG;QACuC,SAAI,GAAG,KAAK,CAAC;QACvD;;;;;;;WAOG;QAC8C,YAAO,GAAG,CAAC,CAAC;QAC7D;;;;;WAKG;QAC8C,YAAO,GAAG,CAAC,CAAC;QAC7D;;;WAGG;QACyD,qBAAgB,GAC1E,KAAK,CAAC;QACR;;;WAGG;QACuD,mBAAc,GACtE,KAAK,CAAC;QACR;;;WAGG;QAEH,mBAAc,GAAG,6BAA6B,CAAC;QAC/C;;;;;;;WAOG;QAEH,iBAAY,GAAW,MAAM,CAAC,SAAS,CAAC;QACxC;;;;;;;WAOG;QACmC,eAAU,GAAW,MAAM,CAAC,WAAW,CAAC;QAC9E;;;;;WAKG;QAEH,2BAAsB,GAAG,KAAK,CAAC;QAC/B;;;;;WAKG;QAEH,uBAAkB,GAAG,KAAK,CAAC;QAC3B;;;WAGG;QAEH,qBAAgB,GAAG,KAAK,CAAC;QACzB;;;;;;WAMG;QAEH,iBAAY,GAAe,UAAU,CAAC,UAAU,CAAC;QAEjD;;;;WAIG;QAEH,qBAAgB,GAAG,KAAK,CAAC;QAGR,oBAAe,GAAG,IAAI,CAAC;QAExC;;;;;WAKG;QACH,cAAS,GAAG,KAAK,CAAC;QAElB;;WAEG;QACK,gBAAW,GAAkB,EAAE,CAAC;QAExC;;WAEG;QACK,oBAAe,GAAG,KAAK,CAAC;QACf,6BAAwB,GAAG,qBAAqB,EAAE,CAAC;QAEnD,mBAAc,GAAG,IAAI,cAAc,CAAW;YAC7D,MAAM,EAAE,CAAC,SAAsB,EAAyB,EAAE;gBACxD,OAAO,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YAChD,CAAC;YACD,gBAAgB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS;YACtC,KAAK,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,KAAK;YACvD,cAAc,EAAE,CAAC,IAAc,EAAE,EAAE;gBACjC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACtB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;YACrB,CAAC;YACD,YAAY,EAAE,CAAC,IAAc,EAAE,EAAE;gBAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;YACpB,CAAC;YACD,cAAc,EAAE,CAAC,GAAW,EAAE,EAAE;gBAC9B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;oBACpB,OAAO,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC9B,CAAC;gBAED,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC;gBACzD,mEAAmE;gBACnE,6DAA6D;gBAC7D,mEAAmE;gBACnE,MAAM;gBACN,MAAM,SAAS,GAAG,KAAK;oBACrB,CAAC,CAAC,aAAa,CAAC,SAAS;oBACzB,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC;gBAE7B,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;oBACtB,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,OAAO,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACjC,CAAC;YACD,cAAc,EAAE,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB;SAC7C,CAAC,CAAC;QAWH;;WAEG;QACK,uBAAkB,GAAuB,IAAI,CAAC;QAEtD;;WAEG;QACH,wBAAmB,GAAG,IAAI,mBAAmB,CAAC,GAAG,EAAE;YACjD,OAAO;gBACL,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK;gBAC1B,mBAAmB,EAAE,IAAI,CAAC,cAAc;gBACxC,MAAM,EAAE,IAAI,CAAC,eAAe;aAC7B,CAAC;QACJ,CAAC,CAAC,CAAC;QAEK,yBAAoB,GAAuB,IAAI,CAAC;QAwBvC,cAAS;QACxB,0BAA0B;QACzB,IAAoB,CAAC,eAAe,EAAE,CAAC;QAe1C;;;WAGG;QACc,2BAAsB,GAAG,IAAI,yBAAyB,CACrE,IAAI,EACJ,GAAG,EAAE;YACH,OAAO;gBACL,YAAY,EAAE,IAAI,CAAC,YAAY;gBAC/B,aAAa,EAAE,IAAI,CAAC,UAAU;gBAC9B,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,QAAQ,EAAE,IAAI,CAAC,aAAa;gBAC5B,WAAW,EACT,IAAI,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW;gBAChE,MAAM,EAAE,IAAI,CAAC,IAAI;gBACjB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,gBAAgB,EAAE,IAAI,CAAC,cAAc;gBACrC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB;gBACxC,MAAM,EAAE,IAAI,CAAC,QAAQ;gBACrB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,OAAO,EAAE,IAAI,CAAC,QAAQ;gBACtB,gEAAgE;gBAChE,iEAAiE;gBACjE,sEAAsE;gBACtE,sEAAsE;gBACtE,WAAW;gBACX,kBAAkB,EAChB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS;oBAChD,CAAC,CAAC,MAAM;oBACR,CAAC,CAAC,QAAQ;aACf,CAAC;QACJ,CAAC,CACF,CAAC;QA6Ce,mBAAc,GAAG,GAAG,EAAE;YACrC,IACE,IAAI,CAAC,eAAe;gBACpB,CAAC,IAAI,CAAC,WAAW,KAAK,UAAU;oBAC9B,IAAI,CAAC,WAAW,KAAK,OAAO;oBAC5B,IAAI,CAAC,WAAW,KAAK,SAAS,CAAC,EACjC,CAAC;gBACD,OAAO;YACT,CAAC;YACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC/B,CAAC,CAAC;QA+Ee,mBAAc,GAAG,KAAK,EAAE,KAAiB,EAAE,EAAE;YAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAc,CAAC;YACrC,uEAAuE;YACvE,4EAA4E;YAC5E,qBAAqB;YACrB,IACE,IAAI,CAAC,kBAAkB;gBACvB,CAAC,IAAI,CAAC,IAAI;gBACV,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,EACnC,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;gBACxB,+DAA+D;gBAC/D,0EAA0E;gBAC1E,0EAA0E;gBAC1E,4BAA4B;gBAC5B,IACE,kBAAkB,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC;oBAC7C,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;wBAC5B,kBAAkB,CAAC,KAAK,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,EACpD,CAAC;oBACD,OAAO;gBACT,CAAC;YACH,CAAC;iBAAM,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3C,0EAA0E;gBAC1E,8BAA8B;gBAC9B,OAAO;YACT,CAAC;YAED,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAC9C,2EAA2E;YAC3E,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,kBAAkB;YAClB,MAAM,IAAI,CAAC,cAAc,CAAC;YAC1B,8BAA8B;YAC9B,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QAC1C,CAAC,CAAC;QAeF;;;WAGG;QACc,aAAQ,GAAG,KAAK,IAAI,EAAE;YACrC,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,EAAE,CAAC;YAE9C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,MAAM,gBAAgB,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;YAE9C,IAAI,gBAAgB,IAAI,IAAI,CAAC,YAAY,KAAK,UAAU,CAAC,IAAI,EAAE,CAAC;gBAC9D,gBAAgB,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;YACtC,CAAC;YAED,IAAI,gBAAgB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;YAEnC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACN,gBAAgB,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YAClD,CAAC;YAED,yEAAyE;YACzE,wEAAwE;YACxE,4DAA4D;YAC5D,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC1B,KAAK,UAAU,CAAC,UAAU;oBACxB,MAAM,KAAK,GAAG,uBAAuB,CAAC,KAAK,CAAC,CAAC;oBAC7C,IAAI,KAAK,EAAE,CAAC;wBACV,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;wBACnB,KAAK,CAAC,KAAK,EAAE,CAAC;wBACd,MAAO,KAA+B,CAAC,cAAc,CAAC;oBACxD,CAAC;oBACD,MAAM;gBACR,KAAK,UAAU,CAAC,SAAS;oBACvB,MAAM,IAAI,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;oBAC3C,IAAI,IAAI,EAAE,CAAC;wBACT,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;wBAClB,IAAI,CAAC,KAAK,EAAE,CAAC;wBACb,MAAO,IAA8B,CAAC,cAAc,CAAC;oBACvD,CAAC;oBACD,MAAM;gBACR,KAAK,UAAU,CAAC,SAAS;oBACvB,IAAI,CAAC,KAAK,EAAE,CAAC;oBACb,MAAM;gBACR,QAAQ;gBACR,KAAK,UAAU,CAAC,IAAI;oBAClB,cAAc;oBACd,MAAM;YACV,CAAC;YAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACtB,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC,CAAC;QAEF;;WAEG;QACc,gBAAW,GAAG,KAAK,IAAI,EAAE;YACxC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;YAElB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC3B,IAAI,CAAC,kBAAkB,EAAE,KAAK,EAAE,EAAE,CAAC;YACrC,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAChB,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC,CAAC;QAEF;;WAEG;QACc,aAAQ,GAAG,GAAG,EAAE;YAC/B,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;gBACzC,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC,CAAC;QAyPe,wBAAmB,GAAG,CAAC,KAAmB,EAAE,EAAE;YAC7D,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;QAC1C,CAAC,CAAC;QAEF;;;;WAIG;QACc,oBAAe,GAAG,CAAC,KAAY,EAAE,EAAE;YAClD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACf,OAAO;YACT,CAAC;YAED,MAAM,IAAI,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;YAElC,IACE,CAAC,IAAI,CAAC,sBAAsB;gBAC5B,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACpB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAc,CAAC,EACnC,CAAC;gBACD,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;YACpB,CAAC;QACH,CAAC,CAAC;QAzkBA,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,CAAC;YAC7B,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACrD,wEAAwE;YACxE,wEAAwE;YACxE,2DAA2D;YAC3D,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;YACvE,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAqCD;;;OAGG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;IACnC,CAAC;IAEkB,UAAU,CAAC,OAA6B;QACzD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YACpC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAEQ,MAAM,CAAC,OAA6B;QAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACd,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACrC,CAAC;QACH,CAAC;QAED,8DAA8D;QAC9D,IACE,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;YAC1B,IAAI,CAAC,WAAW,KAAK,SAAS;YAC9B,0CAA0C;YAC1C,CAAE,IAA8C,CAAC,WAAW,EAC5D,CAAC;YACD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QAC7B,CAAC;QAED,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACxB,CAAC;IAgBQ,iBAAiB;QACxB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAEQ,oBAAoB;QAC3B,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACrC,CAAC;IAEQ,qBAAqB;QAC5B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC,qBAAqB,EAAE,CAAC;QACvC,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC;IAChD,CAAC;IAEQ,cAAc;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC,cAAc,EAAE,CAAC;QAChC,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;IACzC,CAAC;IAEkB,MAAM;QACvB,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,OAAO,IAAI,CAAA;;sBAEO,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACxC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;kBACjD,IAAI,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO;UAC3D,IAAI,CAAC,eAAe,EAAE;;uCAEO,IAAI,CAAC,eAAe,EAAE;;;KAGxD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,OAAO,IAAI,CAAA;oBACK,IAAI,CAAC,WAAW;0BACV,IAAI,CAAC,iBAAiB;4BACpB,IAAI,CAAC,mBAAmB;8BACtB,IAAI,CAAC,yBAAyB;4BAChC,IAAI,CAAC,uBAAuB;+BACzB,IAAI,CAAC,wBAAwB;2BACjC,IAAI,CAAC,qBAAqB;oBACjC,IAAI,CAAC,cAAc,CAAC,YAAY,UAAU,CAAC;IAC7D,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,OAAO,IAAI,CAAA,gDAAgD,CAAC;IAC9D,CAAC;IAEO,iBAAiB;QACvB,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,IAAI,CAAC,WAAW,KAAK,OAAO;YACnC,cAAc,EAAE,IAAI,CAAC,WAAW;SACjC,CAAC;IACJ,CAAC;IA2CO,cAAc,CAAC,KAAoB;QACzC,IACE,KAAK,CAAC,MAAM,KAAK,IAAI;YACrB,CAAC,KAAK,CAAC,gBAAgB;YACvB,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,EACzB,CAAC;YACD,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IAmFD;;;;;;;OAOG;IACK,KAAK,CAAC,WAAW;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAEvC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;QACzC,wEAAwE;QACxE,gBAAgB;QAChB,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAE9C,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACrD,MAAM,MAAM,GAAG,SAAS,CAAC,YAAY,CAAC;QACtC,MAAM,cAAc,GAAG,aAAa,KAAK,IAAI,CAAC;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,MAAM,aAAa,GAAG,GAAG,CAAC;QAC1B,MAAM,wBAAwB,GAAG,EAAE,CAAC;QACpC,MAAM,qBAAqB,GAAG,GAAG,CAAC;QAClC,2EAA2E;QAC3E,iBAAiB;QACjB,MAAM,mBAAmB,GACvB,CAAC,aAAa,GAAG,qBAAqB,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;QAE5D,MAAM,sBAAsB,GAAG,SAAS,CAAC,OAAO,CAC9C,CAAC,EAAC,MAAM,EAAE,KAAK,EAAC,EAAE,EAAC,MAAM,EAAE,GAAG,MAAM,IAAI,EAAC,CAAC,EAC1C;YACE,QAAQ,EAAE,aAAa;YACvB,MAAM,EAAE,MAAM,CAAC,UAAU;SAC1B,CACF,CAAC;QACF,4EAA4E;QAC5E,4EAA4E;QAC5E,mBAAmB;QACnB,MAAM,6BAA6B,GAAG,MAAM,CAAC,OAAO,CAClD;YACE,EAAC,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC,eAAe,MAAM,KAAK,CAAC,CAAC,CAAC,EAAE,EAAC;YAC7D,EAAC,SAAS,EAAE,EAAE,EAAC;SAChB,EACD,EAAC,QAAQ,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,CAAC,UAAU,EAAC,CACrD,CAAC;QAEF,MAAM,uBAAuB,GAAG,SAAS,CAAC,OAAO,CAC/C,CAAC,EAAC,OAAO,EAAE,CAAC,EAAC,EAAE,EAAC,OAAO,EAAE,CAAC,EAAC,CAAC,EAC5B,wBAAwB,CACzB,CAAC;QAEF,MAAM,kBAAkB,GAAoC,EAAE,CAAC;QAE/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,+DAA+D;YAC/D,MAAM,gBAAgB,GAAG,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtE,MAAM,KAAK,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YACzC,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,EAAC,OAAO,EAAE,CAAC,EAAC,EAAE,EAAC,OAAO,EAAE,CAAC,EAAC,CAAC,EAAE;gBAC5D,QAAQ,EAAE,qBAAqB;gBAC/B,KAAK,EAAE,mBAAmB,GAAG,CAAC;aAC/B,CAAC,CAAC;YAEH,sEAAsE;YACtE,aAAa;YACb,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;YAC/C,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACxC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YAEH,kBAAkB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,gBAAgB,GAAG,CAAC,KAAc,EAAE,EAAE,GAAE,CAAC,CAAC;QAC9C,MAAM,iBAAiB,GAAG,IAAI,OAAO,CAAU,CAAC,OAAO,EAAE,EAAE;YACzD,gBAAgB,GAAG,OAAO,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACpC,sBAAsB,CAAC,MAAM,EAAE,CAAC;YAChC,6BAA6B,CAAC,MAAM,EAAE,CAAC;YACvC,uBAAuB,CAAC,MAAM,EAAE,CAAC;YACjC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,EAAE;gBAChD,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;gBAChD,SAAS,CAAC,MAAM,EAAE,CAAC;YACrB,CAAC,CAAC,CAAC;YAEH,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,sBAAsB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,EAAE;YACrD,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAC/C,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,CAAC;YACvC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,iBAAiB,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACK,YAAY;QAClB,IAAI,OAAkC,CAAC;QAEvC,mEAAmE;QACnE,yEAAyE;QACzE,MAAM,cAAc,GAAG,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACzC,OAAO,GAAG,GAAG,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,KAAK,CAAC,CAAC;YACf,OAAO,cAAc,CAAC;QACxB,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,MAAM,gBAAgB,GAAG,aAAa,KAAK,IAAI,CAAC;QAChD,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;QACzC,wEAAwE;QACxE,gBAAgB;QAChB,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACrD,MAAM,MAAM,GAAG,SAAS,CAAC,YAAY,CAAC;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,MAAM,aAAa,GAAG,GAAG,CAAC;QAC1B,MAAM,wBAAwB,GAAG,EAAE,CAAC;QACpC,yCAAyC;QACzC,MAAM,qBAAqB,GAAG,aAAa,GAAG,wBAAwB,CAAC;QACvE,MAAM,qBAAqB,GAAG,EAAE,CAAC;QACjC,MAAM,0BAA0B,GAAG,EAAE,CAAC;QACtC,MAAM,qBAAqB,GAAG,IAAI,CAAC;QAEnC,4EAA4E;QAC5E,iBAAiB;QACjB,MAAM,mBAAmB,GACvB,CAAC,aAAa,GAAG,0BAA0B,GAAG,qBAAqB,CAAC;YACpE,QAAQ,CAAC,MAAM,CAAC;QAElB,2CAA2C;QAC3C,MAAM,sBAAsB,GAAG,SAAS,CAAC,OAAO,CAC9C;YACE,EAAC,MAAM,EAAE,GAAG,MAAM,IAAI,EAAC;YACvB,EAAC,MAAM,EAAE,GAAG,MAAM,GAAG,qBAAqB,IAAI,EAAC;SAChD,EACD;YACE,QAAQ,EAAE,aAAa;YACvB,MAAM,EAAE,MAAM,CAAC,qBAAqB;SACrC,CACF,CAAC;QAEF,uEAAuE;QACvE,4EAA4E;QAC5E,0BAA0B;QAC1B,MAAM,+BAA+B,GAAG,MAAM,CAAC,OAAO,CACpD;YACE,EAAC,SAAS,EAAE,EAAE,EAAC;YACf;gBACE,SAAS,EAAE,gBAAgB;oBACzB,CAAC,CAAC,eAAe,MAAM,GAAG,CAAC,CAAC,GAAG,qBAAqB,CAAC,KAAK;oBAC1D,CAAC,CAAC,EAAE;aACP;SACF,EACD,EAAC,QAAQ,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,CAAC,qBAAqB,EAAC,CAChE,CAAC;QAEF,MAAM,uBAAuB,GAAG,SAAS,CAAC,OAAO,CAC/C,CAAC,EAAC,OAAO,EAAE,CAAC,EAAC,EAAE,EAAC,OAAO,EAAE,CAAC,EAAC,CAAC,EAC5B,EAAC,QAAQ,EAAE,wBAAwB,EAAE,KAAK,EAAE,qBAAqB,EAAC,CACnE,CAAC;QAEF,MAAM,kBAAkB,GAAoC,EAAE,CAAC;QAE/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,gEAAgE;YAChE,yDAAyD;YACzD,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;YACxE,MAAM,KAAK,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YACzC,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,EAAC,OAAO,EAAE,CAAC,EAAC,EAAE,EAAC,OAAO,EAAE,CAAC,EAAC,CAAC,EAAE;gBAC5D,QAAQ,EAAE,qBAAqB;gBAC/B,KAAK,EAAE,0BAA0B,GAAG,mBAAmB,GAAG,CAAC;aAC5D,CAAC,CAAC;YAEH,sEAAsE;YACtE,wDAAwD;YACxD,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACxC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YACH,kBAAkB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;QAC9C,CAAC;QAED,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACpC,sBAAsB,CAAC,MAAM,EAAE,CAAC;YAChC,+BAA+B,CAAC,MAAM,EAAE,CAAC;YACzC,uBAAuB,CAAC,MAAM,EAAE,CAAC;YACjC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,EAAE;gBAChD,SAAS,CAAC,MAAM,EAAE,CAAC;gBACnB,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,sBAAsB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,EAAE;YACrD,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAC/C,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE;gBACrC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,CAAC;YACvC,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,OAAO,cAAc,CAAC;IACxB,CAAC;IAEO,aAAa,CAAC,KAAoB;QACxC,4EAA4E;QAC5E,yEAAyE;QACzE,kCAAkC;QAClC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAEO,yBAAyB;QAC/B,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;QAC1E,MAAM,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACjE,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;QAC1E,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;IAC1E,CAAC;IAEO,2BAA2B;QACjC,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;YAC1D,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;QACH,MAAM,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACpE,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5D,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IAC5D,CAAC;IA2BO,WAAW;QACjB,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEO,iBAAiB,CAAC,KAAY;QACpC,KAAK,CAAC,eAAe,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;IAC1C,CAAC;IAEO,mBAAmB,CAAC,KAAY;QACtC,KAAK,CAAC,eAAe,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IACjD,CAAC;IAEO,yBAAyB,CAAC,KAA+B;QAC/D,2EAA2E;QAC3E,mCAAmC;QACnC,KAAK,CAAC,eAAe,EAAE,CAAC;QACxB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAC/B,CAAC;IAEO,uBAAuB,CAAC,KAA6B;QAC3D,yEAAyE;QACzE,mCAAmC;QACnC,KAAK,CAAC,eAAe,EAAE,CAAC;QACxB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC9B,CAAC;IAEO,wBAAwB,CAAC,KAAY;QAC3C,KAAK,CAAC,eAAe,EAAE,CAAC;QACxB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACjC,CAAC;IAEO,qBAAqB,CAAC,KAAY;QACxC,KAAK,CAAC,eAAe,EAAE,CAAC;QACxB,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,KAAK;QACH,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,MAAM,YAAY,GAAG,IAAI,CAAC,SAEzB,CAAC;QACF,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC5B,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI;QACF,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC;IACxD,CAAC;IAED;;;;;OAKG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAAE,IAAI,IAAI,CAAC;IAC5D,CAAC;IAED;;;;;OAKG;IACH,UAAU;QACR,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,CAAC;QACzC,CAAC;IACH,CAAC;CACF;AAl7BkC;IAAhC,KAAK,CAAC,OAAO,CAAC;uCAAiD;AAChC;IAA/B,KAAK,CAAC,MAAM,CAAC;oCAAkD;AASpD;IAAX,QAAQ,EAAE;oCAAa;AAuCZ;IAAX,QAAQ,EAAE;yCACE;AAIc;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;mCAAe;AASa;IAArD,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,cAAc,EAAC,CAAC;yCAAqB;AAKhC;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;kCAAc;AASN;IAAhD,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAC,CAAC;qCAAa;AAOZ;IAAhD,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAC,CAAC;qCAAa;AAKD;IAA3D,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,oBAAoB,EAAC,CAAC;8CACnD;AAKkD;IAAzD,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAC,CAAC;4CACjD;AAMR;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAC,CAAC;4CACR;AAU/C;IADC,QAAQ,CAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAC;0CACC;AASF;IAArC,QAAQ,CAAC,EAAC,SAAS,EAAE,aAAa,EAAC,CAAC;wCAAyC;AAQ9E;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,4BAA4B,EAAC,CAAC;oDACpC;AAQ/B;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,uBAAuB,EAAC,CAAC;gDACnC;AAM3B;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,oBAAoB,EAAC,CAAC;8CAClC;AASzB;IADC,QAAQ,CAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAC;0CACU;AAQjD;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,oBAAoB,EAAC,CAAC;8CAClC;AAEyB;IAAjD,qBAAqB,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;uCAAqC;AAC3D;IAAhB,KAAK,EAAE;6CAAgC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../elevation/elevation.js';\nimport '../../focus/md-focus-ring.js';\n\nimport {LitElement, PropertyValues, html, isServer, nothing} from 'lit';\nimport {property, query, queryAssignedElements, state} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\nimport {styleMap} from 'lit/directives/style-map.js';\n\nimport {EASING, createAnimationSignal} from '../../internal/motion/animation.js';\nimport {\n  ListController,\n  NavigableKeys,\n} from '../../list/internal/list-controller.js';\nimport {\n  getActiveItem,\n  getFirstActivatableItem,\n  getLastActivatableItem,\n} from '../../list/internal/list-navigation-helpers.js';\n\nimport {MenuItem} from './controllers/menuItemController.js';\nimport {\n  ActivateTypeaheadEvent,\n  DeactivateTypeaheadEvent,\n  FocusState,\n  isClosableKey,\n  isElementInSubtree,\n} from './controllers/shared.js';\nimport {\n  Corner,\n  SurfacePositionController,\n  SurfacePositionTarget,\n} from './controllers/surfacePositionController.js';\nimport {TypeaheadController} from './controllers/typeaheadController.js';\n\nexport {Corner} from './controllers/surfacePositionController.js';\n\n/**\n * The default value for the typeahead buffer time in Milliseconds.\n */\nexport const DEFAULT_TYPEAHEAD_BUFFER_TIME = 200;\n\nconst submenuNavKeys = new Set<string>([\n  NavigableKeys.ArrowDown,\n  NavigableKeys.ArrowUp,\n  NavigableKeys.Home,\n  NavigableKeys.End,\n]);\n\nconst menuNavKeys = new Set<string>([\n  NavigableKeys.ArrowLeft,\n  NavigableKeys.ArrowRight,\n  ...submenuNavKeys,\n]);\n\n/**\n * Gets the currently focused element on the page.\n *\n * @param activeDoc The document or shadowroot from which to start the search.\n *    Defaults to `window.document`\n * @return Returns the currently deeply focused element or `null` if none.\n */\nfunction getFocusedElement(\n  activeDoc: Document | ShadowRoot = document,\n): HTMLElement | null {\n  let activeEl = activeDoc.activeElement as HTMLElement | null;\n\n  // Check for activeElement in the case that an element with a shadow root host\n  // is currently focused.\n  while (activeEl && activeEl?.shadowRoot?.activeElement) {\n    activeEl = activeEl.shadowRoot.activeElement as HTMLElement | null;\n  }\n\n  return activeEl;\n}\n\n/**\n * @fires opening {Event} Fired before the opening animation begins\n * @fires opened {Event} Fired once the menu is open, after any animations\n * @fires closing {Event} Fired before the closing animation begins\n * @fires closed {Event} Fired once the menu is closed, after any animations\n */\nexport abstract class Menu extends LitElement {\n  @query('.menu') private readonly surfaceEl!: HTMLElement | null;\n  @query('slot') private readonly slotEl!: HTMLSlotElement | null;\n\n  /**\n   * The ID of the element in the same root node in which the menu should align\n   * to. Overrides setting `anchorElement = elementReference`.\n   *\n   * __NOTE__: anchor or anchorElement must either be an HTMLElement or resolve\n   * to an HTMLElement in order for menu to open.\n   */\n  @property() anchor = '';\n  /**\n   * Whether the positioning algorithm should calculate relative to the parent\n   * of the anchor element (`absolute`), relative to the window (`fixed`), or\n   * relative to the document (`document`). `popover` will use the popover API\n   * to render the menu in the top-layer. If your browser does not support the\n   * popover API, it will fall back to `fixed`.\n   *\n   * __Examples for `position = 'fixed'`:__\n   *\n   * - If there is no `position:relative` in the given parent tree and the\n   *   surface is `position:absolute`\n   * - If the surface is `position:fixed`\n   * - If the surface is in the \"top layer\"\n   * - The anchor and the surface do not share a common `position:relative`\n   *   ancestor\n   *\n   * When using `positioning=fixed`, in most cases, the menu should position\n   * itself above most other `position:absolute` or `position:fixed` elements\n   * when placed inside of them. e.g. using a menu inside of an `md-dialog`.\n   *\n   * __NOTE__: Fixed menus will not scroll with the page and will be fixed to\n   * the window instead.\n   *\n   * __Examples for `position = 'document'`:__\n   *\n   * - There is no parent that creates a relative positioning context e.g.\n   *   `position: relative`, `position: absolute`, `transform: translate(x, y)`,\n   *   etc.\n   * - You put the effort into hoisting the menu to the top of the DOM like the\n   *   end of the `<body>` to render over everything or in a top-layer.\n   * - You are reusing a single `md-menu` element that dynamically renders\n   *   content.\n   *\n   * __Examples for `position = 'popover'`:__\n   *\n   * - Your browser supports `popover`.\n   * - Most cases. Once popover is in browsers, this will become the default.\n   */\n  @property() positioning: 'absolute' | 'fixed' | 'document' | 'popover' =\n    'absolute';\n  /**\n   * Skips the opening and closing animations.\n   */\n  @property({type: Boolean}) quick = false;\n  /**\n   * Displays overflow content like a submenu. Not required in most cases when\n   * using `positioning=\"popover\"`.\n   *\n   * __NOTE__: This may cause adverse effects if you set\n   * `md-menu {max-height:...}`\n   * and have items overflowing items in the \"y\" direction.\n   */\n  @property({type: Boolean, attribute: 'has-overflow'}) hasOverflow = false;\n  /**\n   * Opens the menu and makes it visible. Alternative to the `.show()` and\n   * `.close()` methods\n   */\n  @property({type: Boolean, reflect: true}) open = false;\n  /**\n   * Offsets the menu's inline alignment from the anchor by the given number in\n   * pixels. This value is direction aware and will follow the LTR / RTL\n   * direction.\n   *\n   * e.g. LTR: positive -> right, negative -> left\n   *      RTL: positive -> left, negative -> right\n   */\n  @property({type: Number, attribute: 'x-offset'}) xOffset = 0;\n  /**\n   * Offsets the menu's block alignment from the anchor by the given number in\n   * pixels.\n   *\n   * e.g. positive -> down, negative -> up\n   */\n  @property({type: Number, attribute: 'y-offset'}) yOffset = 0;\n  /**\n   * Disable the `flip` behavior that usually happens on the horizontal axis\n   * when the surface would render outside the viewport.\n   */\n  @property({type: Boolean, attribute: 'no-horizontal-flip'}) noHorizontalFlip =\n    false;\n  /**\n   * Disable the `flip` behavior that usually happens on the vertical axis when\n   * the surface would render outside the viewport.\n   */\n  @property({type: Boolean, attribute: 'no-vertical-flip'}) noVerticalFlip =\n    false;\n  /**\n   * The max time between the keystrokes of the typeahead menu behavior before\n   * it clears the typeahead buffer.\n   */\n  @property({type: Number, attribute: 'typeahead-delay'})\n  typeaheadDelay = DEFAULT_TYPEAHEAD_BUFFER_TIME;\n  /**\n   * The corner of the anchor which to align the menu in the standard logical\n   * property style of <block>-<inline> e.g. `'end-start'`.\n   *\n   * NOTE: This value may not be respected by the menu positioning algorithm\n   * if the menu would render outisde the viewport.\n   * Use `no-horizontal-flip` or `no-vertical-flip` to force the usage of the value\n   */\n  @property({attribute: 'anchor-corner'})\n  anchorCorner: Corner = Corner.END_START;\n  /**\n   * The corner of the menu which to align the anchor in the standard logical\n   * property style of <block>-<inline> e.g. `'start-start'`.\n   *\n   * NOTE: This value may not be respected by the menu positioning algorithm\n   * if the menu would render outisde the viewport.\n   * Use `no-horizontal-flip` or `no-vertical-flip` to force the usage of the value\n   */\n  @property({attribute: 'menu-corner'}) menuCorner: Corner = Corner.START_START;\n  /**\n   * Keeps the user clicks outside the menu.\n   *\n   * NOTE: clicking outside may still cause focusout to close the menu so see\n   * `stayOpenOnFocusout`.\n   */\n  @property({type: Boolean, attribute: 'stay-open-on-outside-click'})\n  stayOpenOnOutsideClick = false;\n  /**\n   * Keeps the menu open when focus leaves the menu's composed subtree.\n   *\n   * NOTE: Focusout behavior will stop propagation of the focusout event. Set\n   * this property to true to opt-out of menu's focusout handling altogether.\n   */\n  @property({type: Boolean, attribute: 'stay-open-on-focusout'})\n  stayOpenOnFocusout = false;\n  /**\n   * After closing, does not restore focus to the last focused element before\n   * the menu was opened.\n   */\n  @property({type: Boolean, attribute: 'skip-restore-focus'})\n  skipRestoreFocus = false;\n  /**\n   * The element that should be focused by default once opened.\n   *\n   * NOTE: When setting default focus to 'LIST_ROOT', remember to change\n   * `tabindex` to `0` and change md-menu's display to something other than\n   * `display: contents` when necessary.\n   */\n  @property({attribute: 'default-focus'})\n  defaultFocus: FocusState = FocusState.FIRST_ITEM;\n\n  /**\n   * Turns off navigation wrapping. By default, navigating past the end of the\n   * menu items will wrap focus back to the beginning and vice versa. Use this\n   * for ARIA patterns that do not wrap focus, like combobox.\n   */\n  @property({type: Boolean, attribute: 'no-navigation-wrap'})\n  noNavigationWrap = false;\n\n  @queryAssignedElements({flatten: true}) protected slotItems!: HTMLElement[];\n  @state() private typeaheadActive = true;\n\n  /**\n   * Whether or not the current menu is a submenu and should not handle specific\n   * navigation keys.\n   *\n   * @export\n   */\n  isSubmenu = false;\n\n  /**\n   * The event path of the last window pointerdown event.\n   */\n  private pointerPath: EventTarget[] = [];\n\n  /**\n   * Whether or not the menu is repositoining due to window / document resize\n   */\n  private isRepositioning = false;\n  private readonly openCloseAnimationSignal = createAnimationSignal();\n\n  private readonly listController = new ListController<MenuItem>({\n    isItem: (maybeItem: HTMLElement): maybeItem is MenuItem => {\n      return maybeItem.hasAttribute('md-menu-item');\n    },\n    getPossibleItems: () => this.slotItems,\n    isRtl: () => getComputedStyle(this).direction === 'rtl',\n    deactivateItem: (item: MenuItem) => {\n      item.selected = false;\n      item.tabIndex = -1;\n    },\n    activateItem: (item: MenuItem) => {\n      item.selected = true;\n      item.tabIndex = 0;\n    },\n    isNavigableKey: (key: string) => {\n      if (!this.isSubmenu) {\n        return menuNavKeys.has(key);\n      }\n\n      const isRtl = getComputedStyle(this).direction === 'rtl';\n      // we want md-submenu to handle the submenu's left/right arrow exit\n      // key so it can close the menu instead of navigate the list.\n      // Therefore we need to include all keys but left/right arrow close\n      // key\n      const arrowOpen = isRtl\n        ? NavigableKeys.ArrowLeft\n        : NavigableKeys.ArrowRight;\n\n      if (key === arrowOpen) {\n        return true;\n      }\n\n      return submenuNavKeys.has(key);\n    },\n    wrapNavigation: () => !this.noNavigationWrap,\n  });\n\n  /**\n   * Whether the menu is animating upwards or downwards when opening. This is\n   * helpful for calculating some animation calculations.\n   */\n  private get openDirection(): 'UP' | 'DOWN' {\n    const menuCornerBlock = this.menuCorner.split('-')[0];\n    return menuCornerBlock === 'start' ? 'DOWN' : 'UP';\n  }\n\n  /**\n   * The element that was focused before the menu opened.\n   */\n  private lastFocusedElement: HTMLElement | null = null;\n\n  /**\n   * Handles typeahead navigation through the menu.\n   */\n  typeaheadController = new TypeaheadController(() => {\n    return {\n      getItems: () => this.items,\n      typeaheadBufferTime: this.typeaheadDelay,\n      active: this.typeaheadActive,\n    };\n  });\n\n  private currentAnchorElement: HTMLElement | null = null;\n\n  /**\n   * The element which the menu should align to. If `anchor` is set to a\n   * non-empty idref string, then `anchorEl` will resolve to the element with\n   * the given id in the same root node. Otherwise, `null`.\n   */\n  get anchorElement(): (HTMLElement & Partial<SurfacePositionTarget>) | null {\n    if (this.anchor) {\n      return (this.getRootNode() as Document | ShadowRoot).querySelector(\n        `#${this.anchor}`,\n      );\n    }\n\n    return this.currentAnchorElement;\n  }\n\n  set anchorElement(\n    element: (HTMLElement & Partial<SurfacePositionTarget>) | null,\n  ) {\n    this.currentAnchorElement = element;\n    this.requestUpdate('anchorElement');\n  }\n\n  private readonly internals =\n    // Cast needed for closure\n    (this as HTMLElement).attachInternals();\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.internals.role = 'menu';\n      this.addEventListener('keydown', this.handleKeydown);\n      // Capture so that we can grab the event before it reaches the menu item\n      // istelf. Specifically useful for the case where typeahead encounters a\n      // space and we don't want the menu item to close the menu.\n      this.addEventListener('keydown', this.captureKeydown, {capture: true});\n      this.addEventListener('focusout', this.handleFocusout);\n    }\n  }\n\n  /**\n   * Handles positioning the surface and aligning it to the anchor as well as\n   * keeping it in the viewport.\n   */\n  private readonly menuPositionController = new SurfacePositionController(\n    this,\n    () => {\n      return {\n        anchorCorner: this.anchorCorner,\n        surfaceCorner: this.menuCorner,\n        surfaceEl: this.surfaceEl,\n        anchorEl: this.anchorElement,\n        positioning:\n          this.positioning === 'popover' ? 'document' : this.positioning,\n        isOpen: this.open,\n        xOffset: this.xOffset,\n        yOffset: this.yOffset,\n        disableBlockFlip: this.noVerticalFlip,\n        disableInlineFlip: this.noHorizontalFlip,\n        onOpen: this.onOpened,\n        beforeClose: this.beforeClose,\n        onClose: this.onClosed,\n        // We can't resize components that have overflow like menus with\n        // submenus because the overflow-y will show menu items / content\n        // outside the bounds of the menu. Popover API fixes this because each\n        // submenu is hoisted to the top-layer and are not considered overflow\n        // content.\n        repositionStrategy:\n          this.hasOverflow && this.positioning !== 'popover'\n            ? 'move'\n            : 'resize',\n      };\n    },\n  );\n\n  /**\n   * The menu items associated with this menu. The items must be `MenuItem`s and\n   * have both the `md-menu-item` and `md-list-item` attributes.\n   */\n  get items(): MenuItem[] {\n    return this.listController.items;\n  }\n\n  protected override willUpdate(changed: PropertyValues<Menu>) {\n    if (!changed.has('open')) {\n      return;\n    }\n\n    if (this.open) {\n      this.removeAttribute('aria-hidden');\n      return;\n    }\n\n    this.setAttribute('aria-hidden', 'true');\n  }\n\n  override update(changed: PropertyValues<Menu>) {\n    if (changed.has('open')) {\n      if (this.open) {\n        this.setUpGlobalEventListeners();\n      } else {\n        this.cleanUpGlobalEventListeners();\n      }\n    }\n\n    // Firefox does not support popover. Fall-back to using fixed.\n    if (\n      changed.has('positioning') &&\n      this.positioning === 'popover' &&\n      // type required for Google JS conformance\n      !(this as unknown as {showPopover?: () => void}).showPopover\n    ) {\n      this.positioning = 'fixed';\n    }\n\n    super.update(changed);\n  }\n\n  private readonly onWindowResize = () => {\n    if (\n      this.isRepositioning ||\n      (this.positioning !== 'document' &&\n        this.positioning !== 'fixed' &&\n        this.positioning !== 'popover')\n    ) {\n      return;\n    }\n    this.isRepositioning = true;\n    this.reposition();\n    this.isRepositioning = false;\n  };\n\n  override connectedCallback() {\n    super.connectedCallback();\n    if (this.open) {\n      this.setUpGlobalEventListeners();\n    }\n  }\n\n  override disconnectedCallback() {\n    super.disconnectedCallback();\n    this.cleanUpGlobalEventListeners();\n  }\n\n  override getBoundingClientRect() {\n    if (!this.surfaceEl) {\n      return super.getBoundingClientRect();\n    }\n    return this.surfaceEl.getBoundingClientRect();\n  }\n\n  override getClientRects() {\n    if (!this.surfaceEl) {\n      return super.getClientRects();\n    }\n    return this.surfaceEl.getClientRects();\n  }\n\n  protected override render() {\n    return this.renderSurface();\n  }\n\n  /**\n   * Renders the positionable surface element and its contents.\n   */\n  private renderSurface() {\n    return html`\n      <div\n        class=\"menu ${classMap(this.getSurfaceClasses())}\"\n        style=${styleMap(this.menuPositionController.surfaceStyles)}\n        popover=${this.positioning === 'popover' ? 'manual' : nothing}>\n        ${this.renderElevation()}\n        <div class=\"items\">\n          <div class=\"item-padding\"> ${this.renderMenuItems()} </div>\n        </div>\n      </div>\n    `;\n  }\n\n  /**\n   * Renders the menu items' slot\n   */\n  private renderMenuItems() {\n    return html`<slot\n      @close-menu=${this.onCloseMenu}\n      @deactivate-items=${this.onDeactivateItems}\n      @request-activation=${this.onRequestActivation}\n      @deactivate-typeahead=${this.handleDeactivateTypeahead}\n      @activate-typeahead=${this.handleActivateTypeahead}\n      @stay-open-on-focusout=${this.handleStayOpenOnFocusout}\n      @close-on-focusout=${this.handleCloseOnFocusout}\n      @slotchange=${this.listController.onSlotchange}></slot>`;\n  }\n\n  /**\n   * Renders the elevation component.\n   */\n  private renderElevation() {\n    return html`<md-elevation part=\"elevation\"></md-elevation>`;\n  }\n\n  private getSurfaceClasses(): ClassInfo {\n    return {\n      open: this.open,\n      fixed: this.positioning === 'fixed',\n      'has-overflow': this.hasOverflow,\n    };\n  }\n\n  private readonly handleFocusout = async (event: FocusEvent) => {\n    const anchorEl = this.anchorElement!;\n    // Do not close if we focused out by clicking on the anchor element. We\n    // can't assume anchor buttons can be the related target because of iOS does\n    // not focus buttons.\n    if (\n      this.stayOpenOnFocusout ||\n      !this.open ||\n      this.pointerPath.includes(anchorEl)\n    ) {\n      return;\n    }\n\n    if (event.relatedTarget) {\n      // Don't close the menu if we are switching focus between menu,\n      // md-menu-item, and md-list or if the anchor was click focused, but check\n      // if length of pointerPath is 0 because that means something was at least\n      // clicked (shift+tab case).\n      if (\n        isElementInSubtree(event.relatedTarget, this) ||\n        (this.pointerPath.length !== 0 &&\n          isElementInSubtree(event.relatedTarget, anchorEl))\n      ) {\n        return;\n      }\n    } else if (this.pointerPath.includes(this)) {\n      // If menu tabindex == -1 and the user clicks on the menu or a divider, we\n      // want to keep the menu open.\n      return;\n    }\n\n    const oldRestoreFocus = this.skipRestoreFocus;\n    // allow focus to continue to the next focused object rather than returning\n    this.skipRestoreFocus = true;\n    this.close();\n    // await for close\n    await this.updateComplete;\n    // return to previous behavior\n    this.skipRestoreFocus = oldRestoreFocus;\n  };\n\n  private captureKeydown(event: KeyboardEvent) {\n    if (\n      event.target === this &&\n      !event.defaultPrevented &&\n      isClosableKey(event.code)\n    ) {\n      event.preventDefault();\n      this.close();\n    }\n\n    this.typeaheadController.onKeydown(event);\n  }\n\n  /**\n   * Saves the last focused element focuses the new element based on\n   * `defaultFocus`, and animates open.\n   */\n  private readonly onOpened = async () => {\n    this.lastFocusedElement = getFocusedElement();\n\n    const items = this.items;\n    const activeItemRecord = getActiveItem(items);\n\n    if (activeItemRecord && this.defaultFocus !== FocusState.NONE) {\n      activeItemRecord.item.tabIndex = -1;\n    }\n\n    let animationAborted = !this.quick;\n\n    if (this.quick) {\n      this.dispatchEvent(new Event('opening'));\n    } else {\n      animationAborted = !!(await this.animateOpen());\n    }\n\n    // This must come after the opening animation or else it may focus one of\n    // the items before the animation has begun and causes the list to slide\n    // (block-padding-of-the-menu)px at the end of the animation\n    switch (this.defaultFocus) {\n      case FocusState.FIRST_ITEM:\n        const first = getFirstActivatableItem(items);\n        if (first) {\n          first.tabIndex = 0;\n          first.focus();\n          await (first as LitElement & MenuItem).updateComplete;\n        }\n        break;\n      case FocusState.LAST_ITEM:\n        const last = getLastActivatableItem(items);\n        if (last) {\n          last.tabIndex = 0;\n          last.focus();\n          await (last as LitElement & MenuItem).updateComplete;\n        }\n        break;\n      case FocusState.LIST_ROOT:\n        this.focus();\n        break;\n      default:\n      case FocusState.NONE:\n        // Do nothing.\n        break;\n    }\n\n    if (!animationAborted) {\n      this.dispatchEvent(new Event('opened'));\n    }\n  };\n\n  /**\n   * Animates closed.\n   */\n  private readonly beforeClose = async () => {\n    this.open = false;\n\n    if (!this.skipRestoreFocus) {\n      this.lastFocusedElement?.focus?.();\n    }\n\n    if (!this.quick) {\n      await this.animateClose();\n    }\n  };\n\n  /**\n   * Focuses the last focused element.\n   */\n  private readonly onClosed = () => {\n    if (this.quick) {\n      this.dispatchEvent(new Event('closing'));\n      this.dispatchEvent(new Event('closed'));\n    }\n  };\n\n  /**\n   * Performs the opening animation:\n   *\n   * https://direct.googleplex.com/#/spec/295000003+271060003\n   *\n   * @return A promise that resolve to `true` if the animation was aborted,\n   *     `false` if it was not aborted.\n   */\n  private async animateOpen() {\n    const surfaceEl = this.surfaceEl;\n    const slotEl = this.slotEl;\n\n    if (!surfaceEl || !slotEl) return true;\n\n    const openDirection = this.openDirection;\n    this.dispatchEvent(new Event('opening'));\n    // needs to be imperative because we don't want to mix animation and Lit\n    // render timing\n    surfaceEl.classList.toggle('animating', true);\n\n    const signal = this.openCloseAnimationSignal.start();\n    const height = surfaceEl.offsetHeight;\n    const openingUpwards = openDirection === 'UP';\n    const children = this.items;\n    const FULL_DURATION = 500;\n    const SURFACE_OPACITY_DURATION = 50;\n    const ITEM_OPACITY_DURATION = 250;\n    // We want to fit every child fade-in animation within the full duration of\n    // the animation.\n    const DELAY_BETWEEN_ITEMS =\n      (FULL_DURATION - ITEM_OPACITY_DURATION) / children.length;\n\n    const surfaceHeightAnimation = surfaceEl.animate(\n      [{height: '0px'}, {height: `${height}px`}],\n      {\n        duration: FULL_DURATION,\n        easing: EASING.EMPHASIZED,\n      },\n    );\n    // When we are opening upwards, we want to make sure the last item is always\n    // in view, so we need to translate it upwards the opposite direction of the\n    // height animation\n    const upPositionCorrectionAnimation = slotEl.animate(\n      [\n        {transform: openingUpwards ? `translateY(-${height}px)` : ''},\n        {transform: ''},\n      ],\n      {duration: FULL_DURATION, easing: EASING.EMPHASIZED},\n    );\n\n    const surfaceOpacityAnimation = surfaceEl.animate(\n      [{opacity: 0}, {opacity: 1}],\n      SURFACE_OPACITY_DURATION,\n    );\n\n    const childrenAnimations: Array<[HTMLElement, Animation]> = [];\n\n    for (let i = 0; i < children.length; i++) {\n      // If we are animating upwards, then reverse the children list.\n      const directionalIndex = openingUpwards ? children.length - 1 - i : i;\n      const child = children[directionalIndex];\n      const animation = child.animate([{opacity: 0}, {opacity: 1}], {\n        duration: ITEM_OPACITY_DURATION,\n        delay: DELAY_BETWEEN_ITEMS * i,\n      });\n\n      // Make them all initially hidden and then clean up at the end of each\n      // animation.\n      child.classList.toggle('md-menu-hidden', true);\n      animation.addEventListener('finish', () => {\n        child.classList.toggle('md-menu-hidden', false);\n      });\n\n      childrenAnimations.push([child, animation]);\n    }\n\n    let resolveAnimation = (value: boolean) => {};\n    const animationFinished = new Promise<boolean>((resolve) => {\n      resolveAnimation = resolve;\n    });\n\n    signal.addEventListener('abort', () => {\n      surfaceHeightAnimation.cancel();\n      upPositionCorrectionAnimation.cancel();\n      surfaceOpacityAnimation.cancel();\n      childrenAnimations.forEach(([child, animation]) => {\n        child.classList.toggle('md-menu-hidden', false);\n        animation.cancel();\n      });\n\n      resolveAnimation(true);\n    });\n\n    surfaceHeightAnimation.addEventListener('finish', () => {\n      surfaceEl.classList.toggle('animating', false);\n      this.openCloseAnimationSignal.finish();\n      resolveAnimation(false);\n    });\n\n    return await animationFinished;\n  }\n\n  /**\n   * Performs the closing animation:\n   *\n   * https://direct.googleplex.com/#/spec/295000003+271060003\n   */\n  private animateClose() {\n    let resolve!: (value: unknown) => void;\n\n    // This promise blocks the surface position controller from setting\n    // display: none on the surface which will interfere with this animation.\n    const animationEnded = new Promise((res) => {\n      resolve = res;\n    });\n\n    const surfaceEl = this.surfaceEl;\n    const slotEl = this.slotEl;\n\n    if (!surfaceEl || !slotEl) {\n      resolve(false);\n      return animationEnded;\n    }\n\n    const openDirection = this.openDirection;\n    const closingDownwards = openDirection === 'UP';\n    this.dispatchEvent(new Event('closing'));\n    // needs to be imperative because we don't want to mix animation and Lit\n    // render timing\n    surfaceEl.classList.toggle('animating', true);\n    const signal = this.openCloseAnimationSignal.start();\n    const height = surfaceEl.offsetHeight;\n    const children = this.items;\n    const FULL_DURATION = 150;\n    const SURFACE_OPACITY_DURATION = 50;\n    // The surface fades away at the very end\n    const SURFACE_OPACITY_DELAY = FULL_DURATION - SURFACE_OPACITY_DURATION;\n    const ITEM_OPACITY_DURATION = 50;\n    const ITEM_OPACITY_INITIAL_DELAY = 50;\n    const END_HEIGHT_PERCENTAGE = 0.35;\n\n    // We want to fit every child fade-out animation within the full duration of\n    // the animation.\n    const DELAY_BETWEEN_ITEMS =\n      (FULL_DURATION - ITEM_OPACITY_INITIAL_DELAY - ITEM_OPACITY_DURATION) /\n      children.length;\n\n    // The mock has the animation shrink to 35%\n    const surfaceHeightAnimation = surfaceEl.animate(\n      [\n        {height: `${height}px`},\n        {height: `${height * END_HEIGHT_PERCENTAGE}px`},\n      ],\n      {\n        duration: FULL_DURATION,\n        easing: EASING.EMPHASIZED_ACCELERATE,\n      },\n    );\n\n    // When we are closing downwards, we want to make sure the last item is\n    // always in view, so we need to translate it upwards the opposite direction\n    // of the height animation\n    const downPositionCorrectionAnimation = slotEl.animate(\n      [\n        {transform: ''},\n        {\n          transform: closingDownwards\n            ? `translateY(-${height * (1 - END_HEIGHT_PERCENTAGE)}px)`\n            : '',\n        },\n      ],\n      {duration: FULL_DURATION, easing: EASING.EMPHASIZED_ACCELERATE},\n    );\n\n    const surfaceOpacityAnimation = surfaceEl.animate(\n      [{opacity: 1}, {opacity: 0}],\n      {duration: SURFACE_OPACITY_DURATION, delay: SURFACE_OPACITY_DELAY},\n    );\n\n    const childrenAnimations: Array<[HTMLElement, Animation]> = [];\n\n    for (let i = 0; i < children.length; i++) {\n      // If the animation is closing upwards, then reverse the list of\n      // children so that we animate in the opposite direction.\n      const directionalIndex = closingDownwards ? i : children.length - 1 - i;\n      const child = children[directionalIndex];\n      const animation = child.animate([{opacity: 1}, {opacity: 0}], {\n        duration: ITEM_OPACITY_DURATION,\n        delay: ITEM_OPACITY_INITIAL_DELAY + DELAY_BETWEEN_ITEMS * i,\n      });\n\n      // Make sure the items stay hidden at the end of each child animation.\n      // We clean this up at the end of the overall animation.\n      animation.addEventListener('finish', () => {\n        child.classList.toggle('md-menu-hidden', true);\n      });\n      childrenAnimations.push([child, animation]);\n    }\n\n    signal.addEventListener('abort', () => {\n      surfaceHeightAnimation.cancel();\n      downPositionCorrectionAnimation.cancel();\n      surfaceOpacityAnimation.cancel();\n      childrenAnimations.forEach(([child, animation]) => {\n        animation.cancel();\n        child.classList.toggle('md-menu-hidden', false);\n      });\n      resolve(false);\n    });\n\n    surfaceHeightAnimation.addEventListener('finish', () => {\n      surfaceEl.classList.toggle('animating', false);\n      childrenAnimations.forEach(([child]) => {\n        child.classList.toggle('md-menu-hidden', false);\n      });\n      this.openCloseAnimationSignal.finish();\n      this.dispatchEvent(new Event('closed'));\n      resolve(true);\n    });\n\n    return animationEnded;\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    // At any key event, the pointer interaction is done so we need to clear our\n    // cached pointerpath. This handles the case where the user clicks on the\n    // anchor, and then hits shift+tab\n    this.pointerPath = [];\n    this.listController.handleKeydown(event);\n  }\n\n  private setUpGlobalEventListeners() {\n    document.addEventListener('click', this.onDocumentClick, {capture: true});\n    window.addEventListener('pointerdown', this.onWindowPointerdown);\n    document.addEventListener('resize', this.onWindowResize, {passive: true});\n    window.addEventListener('resize', this.onWindowResize, {passive: true});\n  }\n\n  private cleanUpGlobalEventListeners() {\n    document.removeEventListener('click', this.onDocumentClick, {\n      capture: true,\n    });\n    window.removeEventListener('pointerdown', this.onWindowPointerdown);\n    document.removeEventListener('resize', this.onWindowResize);\n    window.removeEventListener('resize', this.onWindowResize);\n  }\n\n  private readonly onWindowPointerdown = (event: PointerEvent) => {\n    this.pointerPath = event.composedPath();\n  };\n\n  /**\n   * We cannot listen to window click because Safari on iOS will not bubble a\n   * click event on window if the item clicked is not a \"clickable\" item such as\n   * <body>\n   */\n  private readonly onDocumentClick = (event: Event) => {\n    if (!this.open) {\n      return;\n    }\n\n    const path = event.composedPath();\n\n    if (\n      !this.stayOpenOnOutsideClick &&\n      !path.includes(this) &&\n      !path.includes(this.anchorElement!)\n    ) {\n      this.open = false;\n    }\n  };\n\n  private onCloseMenu() {\n    this.close();\n  }\n\n  private onDeactivateItems(event: Event) {\n    event.stopPropagation();\n    this.listController.onDeactivateItems();\n  }\n\n  private onRequestActivation(event: Event) {\n    event.stopPropagation();\n    this.listController.onRequestActivation(event);\n  }\n\n  private handleDeactivateTypeahead(event: DeactivateTypeaheadEvent) {\n    // stopPropagation so that this does not deactivate any typeaheads in menus\n    // nested above it e.g. md-sub-menu\n    event.stopPropagation();\n    this.typeaheadActive = false;\n  }\n\n  private handleActivateTypeahead(event: ActivateTypeaheadEvent) {\n    // stopPropagation so that this does not activate any typeaheads in menus\n    // nested above it e.g. md-sub-menu\n    event.stopPropagation();\n    this.typeaheadActive = true;\n  }\n\n  private handleStayOpenOnFocusout(event: Event) {\n    event.stopPropagation();\n    this.stayOpenOnFocusout = true;\n  }\n\n  private handleCloseOnFocusout(event: Event) {\n    event.stopPropagation();\n    this.stayOpenOnFocusout = false;\n  }\n\n  close() {\n    this.open = false;\n    const maybeSubmenu = this.slotItems as Array<\n      HTMLElement & {close?: () => void}\n    >;\n    maybeSubmenu.forEach((item) => {\n      item.close?.();\n    });\n  }\n\n  show() {\n    this.open = true;\n  }\n\n  /**\n   * Activates the next item in the menu. If at the end of the menu, the first\n   * item will be activated.\n   *\n   * @return The activated menu item or `null` if there are no items.\n   */\n  activateNextItem() {\n    return this.listController.activateNextItem() ?? null;\n  }\n\n  /**\n   * Activates the previous item in the menu. If at the start of the menu, the\n   * last item will be activated.\n   *\n   * @return The activated menu item or `null` if there are no items.\n   */\n  activatePreviousItem() {\n    return this.listController.activatePreviousItem() ?? null;\n  }\n\n  /**\n   * Repositions the menu if it is open.\n   *\n   * Useful for the case where document or window-positioned menus have their\n   * anchors moved while open.\n   */\n  reposition() {\n    if (this.open) {\n      this.menuPositionController.position();\n    }\n  }\n}\n"]}