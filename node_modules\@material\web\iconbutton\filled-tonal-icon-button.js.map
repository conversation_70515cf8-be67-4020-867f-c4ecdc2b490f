{"version": 3, "file": "filled-tonal-icon-button.js", "sourceRoot": "", "sources": ["filled-tonal-icon-button.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,MAAM,EAAC,MAAM,mCAAmC,CAAC;AACzD,OAAO,EAAC,UAAU,EAAC,MAAM,2BAA2B,CAAC;AACrD,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAQnE;;;;;;;;;;;;;;;;;GAiBG;AAEI,IAAM,uBAAuB,GAA7B,MAAM,uBAAwB,SAAQ,UAAU;IAGlC,gBAAgB;QACjC,OAAO;YACL,GAAG,KAAK,CAAC,gBAAgB,EAAE;YAC3B,cAAc,EAAE,IAAI;YACpB,qBAAqB,EAAE,IAAI,CAAC,MAAM;SACnC,CAAC;IACJ,CAAC;;AARe,8BAAM,GAAwB,CAAC,YAAY,EAAE,MAAM,CAAC,AAA9C,CAA+C;AAD1D,uBAAuB;IADnC,aAAa,CAAC,6BAA6B,CAAC;GAChC,uBAAuB,CAUnC", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {styles} from './internal/filled-tonal-styles.js';\nimport {IconButton} from './internal/icon-button.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-filled-tonal-icon-button': MdFilledTonalIconButton;\n  }\n}\n\n/**\n * @summary Icon buttons help people take supplementary actions with a single\n * tap.\n *\n * @description\n * __Emphasis:__ Low emphasis – For optional or supplementary actions with the\n * least amount of prominence.\n *\n * __Rationale:__ The most compact and unobtrusive type of button, icon buttons\n * are used for optional supplementary actions such as \"Bookmark\" or \"Star.\"\n *\n * __Example usages:__\n * - Add to Favorites\n * - Print\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-filled-tonal-icon-button')\nexport class MdFilledTonalIconButton extends IconButton {\n  static override styles: CSSResultOrNative[] = [sharedStyles, styles];\n\n  protected override getRenderClasses() {\n    return {\n      ...super.getRenderClasses(),\n      'filled-tonal': true,\n      'toggle-filled-tonal': this.toggle,\n    };\n  }\n}\n"]}