//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
@use 'sass:string';
// go/keep-sorted end
// go/keep-sorted start
@use './internal/shape';
@use './internal/validate';
@use './md-sys-color';
@use './md-sys-shape';
@use './md-sys-state';
@use './v0_192/md-comp-switch';
// go/keep-sorted end

$supported-tokens: (
  // go/keep-sorted start
  'disabled-handle-color',
  'disabled-handle-opacity',
  'disabled-icon-color',
  'disabled-icon-opacity',
  'disabled-selected-handle-color',
  'disabled-selected-handle-opacity',
  'disabled-selected-icon-color',
  'disabled-selected-icon-opacity',
  'disabled-selected-track-color',
  'disabled-track-color',
  'disabled-track-opacity',
  'disabled-track-outline-color',
  'focus-handle-color',
  'focus-icon-color',
  'focus-track-color',
  'focus-track-outline-color',
  'handle-color',
  'handle-height',
  'handle-shape',
  'handle-shape-end-end',
  'handle-shape-end-start',
  'handle-shape-start-end',
  'handle-shape-start-start',
  'handle-width',
  'hover-handle-color',
  'hover-icon-color',
  'hover-state-layer-color',
  'hover-state-layer-opacity',
  'hover-track-color',
  'hover-track-outline-color',
  'icon-color',
  'icon-size',
  'pressed-handle-color',
  'pressed-handle-height',
  'pressed-handle-width',
  'pressed-icon-color',
  'pressed-state-layer-color',
  'pressed-state-layer-opacity',
  'pressed-track-color',
  'pressed-track-outline-color',
  'selected-focus-handle-color',
  'selected-focus-icon-color',
  'selected-focus-track-color',
  'selected-handle-color',
  'selected-handle-height',
  'selected-handle-width',
  'selected-hover-handle-color',
  'selected-hover-icon-color',
  'selected-hover-state-layer-color',
  'selected-hover-state-layer-opacity',
  'selected-hover-track-color',
  'selected-icon-color',
  'selected-icon-size',
  'selected-pressed-handle-color',
  'selected-pressed-icon-color',
  'selected-pressed-state-layer-color',
  'selected-pressed-state-layer-opacity',
  'selected-pressed-track-color',
  'selected-track-color',
  'state-layer-shape',
  'state-layer-size',
  'touch-target-size',
  'track-color',
  'track-height',
  'track-outline-color',
  'track-outline-width',
  'track-shape',
  'track-shape-end-end',
  'track-shape-end-start',
  'track-shape-start-end',
  'track-shape-start-start',
  'track-width',
  'with-icon-handle-height',
  'with-icon-handle-width',
  // go/keep-sorted end
);

$unsupported-tokens: (
  // go/keep-sorted start
  'focus-state-layer-color',
  'focus-state-layer-opacity',
  'selected-focus-state-layer-color',
  'selected-focus-state-layer-opacity',
  // go/keep-sorted end
);

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
);

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $tokens: md-comp-switch.values($deps, $exclude-hardcoded-values);
  $new-tokens: shape.get-new-logical-shape-tokens(
    $tokens,
    'handle-shape',
    'track-shape'
  );
  $new-tokens: map.merge(
    $new-tokens,
    (
      'touch-target-size': if($exclude-hardcoded-values, null, 48px)
    )
  );

  $tokens: validate.values(
    $tokens,
    $supported-tokens: $supported-tokens,
    $unsupported-tokens: $unsupported-tokens,
    $new-tokens: $new-tokens,
    $renamed-tokens: (
      // Remove default "unselected" prefix (b/292244480)
      'disabled-unselected-handle-color': 'disabled-handle-color',
      'disabled-unselected-handle-opacity': 'disabled-handle-opacity',
      'disabled-unselected-icon-color': 'disabled-icon-color',
      'disabled-unselected-icon-opacity': 'disabled-icon-opacity',
      'disabled-unselected-track-color': 'disabled-track-color',
      'disabled-unselected-track-outline-color': 'disabled-track-outline-color',
      'unselected-focus-handle-color': 'focus-handle-color',
      'unselected-focus-icon-color': 'focus-icon-color',
      'unselected-focus-state-layer-color': 'focus-state-layer-color',
      'unselected-focus-state-layer-opacity': 'focus-state-layer-opacity',
      'unselected-focus-track-color': 'focus-track-color',
      'unselected-focus-track-outline-color': 'focus-track-outline-color',
      'unselected-handle-color': 'handle-color',
      'unselected-handle-height': 'handle-height',
      'unselected-handle-width': 'handle-width',
      'unselected-hover-handle-color': 'hover-handle-color',
      'unselected-hover-icon-color': 'hover-icon-color',
      'unselected-hover-state-layer-color': 'hover-state-layer-color',
      'unselected-hover-state-layer-opacity': 'hover-state-layer-opacity',
      'unselected-hover-track-color': 'hover-track-color',
      'unselected-hover-track-outline-color': 'hover-track-outline-color',
      'unselected-icon-color': 'icon-color',
      'unselected-icon-size': 'icon-size',
      'unselected-pressed-handle-color': 'pressed-handle-color',
      'unselected-pressed-icon-color': 'pressed-icon-color',
      'unselected-pressed-state-layer-color': 'pressed-state-layer-color',
      'unselected-pressed-state-layer-opacity': 'pressed-state-layer-opacity',
      'unselected-pressed-track-color': 'pressed-track-color',
      'unselected-pressed-track-outline-color': 'pressed-track-outline-color',
      'unselected-track-color': 'track-color',
      'unselected-track-outline-color': 'track-outline-color'
    )
  );

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      $shape-tokens: ('handle-shape', 'track-shape');
      @each $shape-token in $shape-tokens {
        @if string.index($token, '#{$shape-token}-') == 1 {
          // Add fallback to shorthand for logical shape properties.
          $value: var(--md-switch-#{$shape-token}, #{$value});
        }
      }

      $tokens: map.set($tokens, $token, var(--md-switch-#{$token}, #{$value}));
    }
  }

  @return $tokens;
}
