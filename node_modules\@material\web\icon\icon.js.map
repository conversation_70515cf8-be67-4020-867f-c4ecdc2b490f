{"version": 3, "file": "icon.js", "sourceRoot": "", "sources": ["icon.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,IAAI,EAAC,MAAM,oBAAoB,CAAC;AACxC,OAAO,EAAC,MAAM,EAAC,MAAM,2BAA2B,CAAC;AAQjD;;;GAGG;AAEI,IAAM,MAAM,GAAZ,MAAM,MAAO,SAAQ,IAAI;;AAC9B,kBAAkB;AACF,aAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAF5C,MAAM;IADlB,aAAa,CAAC,SAAS,CAAC;GACZ,MAAM,CAGlB", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Icon} from './internal/icon.js';\nimport {styles} from './internal/icon-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-icon': MdIcon;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-icon')\nexport class MdIcon extends Icon {\n  /** @nocollapse */\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}