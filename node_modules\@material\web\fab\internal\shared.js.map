{"version": 3, "file": "shared.js", "sourceRoot": "", "sources": ["shared.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,8BAA8B,CAAC;AACtC,OAAO,8BAA8B,CAAC;AACtC,OAAO,wBAAwB,CAAC;AAEhC,OAAO,EAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAC,MAAM,KAAK,CAAC;AAC9C,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAGrD,OAAO,EAAC,kBAAkB,EAAC,MAAM,iCAAiC,CAAC;AAOnE,wCAAwC;AACxC,MAAM,YAAY,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAEpD,gEAAgE;AAChE,MAAM,OAAgB,SAAU,SAAQ,YAAY;IAApD;;QAOE;;;;;WAKG;QACwB,SAAI,GAAY,QAAQ,CAAC;QAEpD;;WAEG;QACS,UAAK,GAAG,EAAE,CAAC;QAEvB;;WAEG;QACwB,YAAO,GAAG,KAAK,CAAC;IA+C7C,CAAC;IA7CoB,MAAM;QACvB,iCAAiC;QACjC,MAAM,EAAC,SAAS,EAAC,GAAG,IAAuB,CAAC;QAC5C,OAAO,IAAI,CAAA;;qBAEM,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;qBACjC,SAAS,IAAI,OAAO;;;;UAI/B,IAAI,CAAC,iBAAiB,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE;;KAExE,CAAC;IACJ,CAAC;IAES,gBAAgB;QACxB,MAAM,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;QAChC,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,OAAO;YACvB,OAAO,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,UAAU;YAC7C,OAAO,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,UAAU;YAC7C,UAAU,EAAE,UAAU;SACvB,CAAC;IACJ,CAAC;IAEO,iBAAiB;QACvB,OAAO,IAAI,CAAA,kCAAkC,CAAC;IAChD,CAAC;IAEO,WAAW;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA,uBAAuB,IAAI,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;IAC1E,CAAC;IAEO,UAAU;QAChB,MAAM,EAAC,SAAS,EAAC,GAAG,IAAuB,CAAC;QAC5C,OAAO,IAAI,CAAA;;;sBAGO,SAAS,IAAI,IAAI,CAAC,KAAK;YACnC,CAAC,CAAC,MAAM;YACR,CAAC,CAAE,OAA8B;;;YAG/B,CAAC;IACX,CAAC;;AApED,kBAAkB;AACF,2BAAiB,GAAmB;IAClD,IAAI,EAAE,MAAe;IACrB,cAAc,EAAE,IAAI;CACrB,AAHgC,CAG/B;AAQyB;IAA1B,QAAQ,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;uCAA0B;AAKxC;IAAX,QAAQ,EAAE;wCAAY;AAKI;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;0CAAiB", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../elevation/elevation.js';\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, LitElement, nothing} from 'lit';\nimport {property} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\n\n/**\n * Sizes variants available to non-extended FABs.\n */\nexport type FabSize = 'medium' | 'small' | 'large';\n\n// Separate variable needed for closure.\nconst fabBaseClass = mixinDelegatesAria(LitElement);\n\n// tslint:disable-next-line:enforce-comments-on-exported-symbols\nexport abstract class SharedFab extends fabBaseClass {\n  /** @nocollapse */\n  static override shadowRootOptions: ShadowRootInit = {\n    mode: 'open' as const,\n    delegatesFocus: true,\n  };\n\n  /**\n   * The size of the FAB.\n   *\n   * NOTE: Branded FABs cannot be sized to `small`, and Extended FABs do not\n   * have different sizes.\n   */\n  @property({reflect: true}) size: FabSize = 'medium';\n\n  /**\n   * The text to display on the FAB.\n   */\n  @property() label = '';\n\n  /**\n   * Lowers the FAB's elevation.\n   */\n  @property({type: Boolean}) lowered = false;\n\n  protected override render() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`\n      <button\n        class=\"fab ${classMap(this.getRenderClasses())}\"\n        aria-label=${ariaLabel || nothing}>\n        <md-elevation part=\"elevation\"></md-elevation>\n        <md-focus-ring part=\"focus-ring\"></md-focus-ring>\n        <md-ripple class=\"ripple\"></md-ripple>\n        ${this.renderTouchTarget()} ${this.renderIcon()} ${this.renderLabel()}\n      </button>\n    `;\n  }\n\n  protected getRenderClasses() {\n    const isExtended = !!this.label;\n    return {\n      'lowered': this.lowered,\n      'small': this.size === 'small' && !isExtended,\n      'large': this.size === 'large' && !isExtended,\n      'extended': isExtended,\n    };\n  }\n\n  private renderTouchTarget() {\n    return html`<div class=\"touch-target\"></div>`;\n  }\n\n  private renderLabel() {\n    return this.label ? html`<span class=\"label\">${this.label}</span>` : '';\n  }\n\n  private renderIcon() {\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`<span class=\"icon\">\n      <slot\n        name=\"icon\"\n        aria-hidden=${ariaLabel || this.label\n          ? 'true'\n          : (nothing as unknown as 'false')}>\n        <span></span>\n      </slot>\n    </span>`;\n  }\n}\n"]}