{"version": 3, "file": "focus-ring.js", "sourceRoot": "", "sources": ["focus-ring.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,EAAC,QAAQ,EAAE,UAAU,EAAiB,MAAM,KAAK,CAAC;AACzD,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAEL,oBAAoB,GACrB,MAAM,oDAAoD,CAAC;AAE5D;;GAEG;AACH,MAAM,MAAM,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;AAEtD;;;;GAIG;AACH,MAAM,OAAO,SAAU,SAAQ,UAAU;IAAzC;;QACE;;WAEG;QACuC,YAAO,GAAG,KAAK,CAAC;QAE1D;;WAEG;QACuC,WAAM,GAAG,KAAK,CAAC;QAiBxC,yBAAoB,GAAG,IAAI,oBAAoB,CAC9D,IAAI,EACJ,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC,CAAC;IAyDJ,CAAC;IA3EC,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;IAC3C,CAAC;IAED,IAAI,OAAO,CAAC,OAAsB;QAChC,IAAI,CAAC,oBAAoB,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9C,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;IAC3C,CAAC;IACD,IAAI,OAAO,CAAC,OAA2B;QACrC,IAAI,CAAC,oBAAoB,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9C,CAAC;IAOD,MAAM,CAAC,OAAoB;QACzB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC;IACrC,CAAC;IAEQ,iBAAiB;QACxB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,wEAAwE;QACxE,4BAA4B;QAC5B,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED,eAAe;IACf,WAAW,CAAC,KAAqB;QAC/B,IAAI,KAAK,CAAC,qBAAqB,CAAC,EAAE,CAAC;YACjC,0EAA0E;YAC1E,sCAAsC;YACtC,OAAO;QACT,CAAC;QAED,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB;gBACE,OAAO;YACT,KAAK,SAAS;gBACZ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,gBAAgB,CAAC,IAAI,KAAK,CAAC;gBAChE,MAAM;YACR,KAAK,UAAU,CAAC;YAChB,KAAK,aAAa;gBAChB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;gBACrB,MAAM;QACV,CAAC;QAED,KAAK,CAAC,qBAAqB,CAAC,GAAG,IAAI,CAAC;IACtC,CAAC;IAEO,eAAe,CAAC,IAAwB,EAAE,IAAwB;QACxE,IAAI,QAAQ;YAAE,OAAO;QAErB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,EAAE,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACvC,IAAI,EAAE,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAEQ,MAAM,CAAC,OAAkC;QAChD,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3B,yEAAyE;YACzE,iEAAiE;YACjE,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;QACtD,CAAC;QACD,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACxB,CAAC;CACF;AAlF2C;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;0CAAiB;AAKhB;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;yCAAgB;AA+E3D,MAAM,qBAAqB,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {isServer, LitElement, PropertyValues} from 'lit';\nimport {property} from 'lit/decorators.js';\n\nimport {\n  Attachable,\n  AttachableController,\n} from '../../internal/controller/attachable-controller.js';\n\n/**\n * Events that the focus ring listens to.\n */\nconst EVENTS = ['focusin', 'focusout', 'pointerdown'];\n\n/**\n * A focus ring component.\n *\n * @fires visibility-changed {Event} Fired whenever `visible` changes.\n */\nexport class FocusRing extends LitElement implements Attachable {\n  /**\n   * Makes the focus ring visible.\n   */\n  @property({type: Boolean, reflect: true}) visible = false;\n\n  /**\n   * Makes the focus ring animate inwards instead of outwards.\n   */\n  @property({type: Boolean, reflect: true}) inward = false;\n\n  get htmlFor() {\n    return this.attachableController.htmlFor;\n  }\n\n  set htmlFor(htmlFor: string | null) {\n    this.attachableController.htmlFor = htmlFor;\n  }\n\n  get control() {\n    return this.attachableController.control;\n  }\n  set control(control: HTMLElement | null) {\n    this.attachableController.control = control;\n  }\n\n  private readonly attachableController = new AttachableController(\n    this,\n    this.onControlChange.bind(this),\n  );\n\n  attach(control: HTMLElement) {\n    this.attachableController.attach(control);\n  }\n\n  detach() {\n    this.attachableController.detach();\n  }\n\n  override connectedCallback() {\n    super.connectedCallback();\n    // Needed for VoiceOver, which will create a \"group\" if the element is a\n    // sibling to other content.\n    this.setAttribute('aria-hidden', 'true');\n  }\n\n  /** @private */\n  handleEvent(event: FocusRingEvent) {\n    if (event[HANDLED_BY_FOCUS_RING]) {\n      // This ensures the focus ring does not activate when multiple focus rings\n      // are used within a single component.\n      return;\n    }\n\n    switch (event.type) {\n      default:\n        return;\n      case 'focusin':\n        this.visible = this.control?.matches(':focus-visible') ?? false;\n        break;\n      case 'focusout':\n      case 'pointerdown':\n        this.visible = false;\n        break;\n    }\n\n    event[HANDLED_BY_FOCUS_RING] = true;\n  }\n\n  private onControlChange(prev: HTMLElement | null, next: HTMLElement | null) {\n    if (isServer) return;\n\n    for (const event of EVENTS) {\n      prev?.removeEventListener(event, this);\n      next?.addEventListener(event, this);\n    }\n  }\n\n  override update(changed: PropertyValues<FocusRing>) {\n    if (changed.has('visible')) {\n      // This logic can be removed once the `:has` selector has been introduced\n      // to Firefox. This is necessary to allow correct submenu styles.\n      this.dispatchEvent(new Event('visibility-changed'));\n    }\n    super.update(changed);\n  }\n}\n\nconst HANDLED_BY_FOCUS_RING = Symbol('handledByFocusRing');\n\ninterface FocusRingEvent extends Event {\n  [HANDLED_BY_FOCUS_RING]: true;\n}\n"]}