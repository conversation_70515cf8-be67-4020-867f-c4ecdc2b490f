{"version": 3, "file": "outlined-segmented-button.js", "sourceRoot": "", "sources": ["outlined-segmented-button.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,uBAAuB,EAAC,MAAM,yCAAyC,CAAC;AAChF,OAAO,EAAC,MAAM,IAAI,cAAc,EAAC,MAAM,+BAA+B,CAAC;AACvE,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAQnE;;;;;GAKG;AAEI,IAAM,yBAAyB,GAA/B,MAAM,yBAA0B,SAAQ,uBAAuB;;AACpD,gCAAM,GAAwB,CAAC,YAAY,EAAE,cAAc,CAAC,AAAtD,CAAuD;AADlE,yBAAyB;IADrC,aAAa,CAAC,8BAA8B,CAAC;GACjC,yBAAyB,CAErC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {OutlinedSegmentedButton} from './internal/outlined-segmented-button.js';\nimport {styles as outlinedStyles} from './internal/outlined-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-outlined-segmented-button': MdOutlinedSegmentedButton;\n  }\n}\n\n/**\n * MdOutlinedSegmentedButton is the custom element for the Material\n * Design outlined segmented button component.\n * @final\n * @suppress {visibility}\n */\n@customElement('md-outlined-segmented-button')\nexport class MdOutlinedSegmentedButton extends OutlinedSegmentedButton {\n  static override styles: CSSResultOrNative[] = [sharedStyles, outlinedStyles];\n}\n"]}