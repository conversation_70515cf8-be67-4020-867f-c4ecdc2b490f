{"version": 3, "file": "select.js", "sourceRoot": "", "sources": ["select.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;AAEH,OAAO,oBAAoB,CAAC;AAE5B,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAiB,MAAM,KAAK,CAAC;AACxE,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAE,qBAAqB,EAAE,KAAK,EAAC,MAAM,mBAAmB,CAAC;AAChF,OAAO,EAAY,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAChE,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AACrD,OAAO,EAAC,IAAI,IAAI,UAAU,EAAc,MAAM,oBAAoB,CAAC;AAInE,OAAO,EAAC,kBAAkB,EAAC,MAAM,iCAAiC,CAAC;AACnE,OAAO,EAAC,eAAe,EAAC,MAAM,2CAA2C,CAAC;AAC1E,OAAO,EACL,eAAe,EACf,iBAAiB,EACjB,yBAAyB,GAC1B,MAAM,+CAA+C,CAAC;AACvD,OAAO,EAAC,qBAAqB,EAAC,MAAM,2CAA2C,CAAC;AAChF,OAAO,EACL,YAAY,EACZ,mBAAmB,GACpB,MAAM,yCAAyC,CAAC;AACjD,OAAO,EACL,qBAAqB,EACrB,gBAAgB,GACjB,MAAM,4CAA4C,CAAC;AACpD,OAAO,EAAC,eAAe,EAAC,MAAM,qDAAqD,CAAC;AACpF,OAAO,EAAC,aAAa,EAAC,MAAM,gDAAgD,CAAC;AAC7E,OAAO,EAEL,UAAU,EACV,kBAAkB,EAClB,eAAe,GAChB,MAAM,2CAA2C,CAAC;AACnD,OAAO,EAAC,gBAAgB,EAAC,MAAM,wDAAwD,CAAC;AACxF,OAAO,EAAC,6BAA6B,EAAO,MAAM,6BAA6B,CAAC;AAMhF,OAAO,EAAC,gBAAgB,EAAqB,MAAM,aAAa,CAAC;AAEjE,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AAE9B,wCAAwC;AACxC,MAAM,eAAe,GAAG,kBAAkB,CACxC,qBAAqB,CACnB,yBAAyB,CACvB,mBAAmB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,CACvD,CACF,CACF,CAAC;AAEF;;;;;;;;;;;;;GAaG;AACH,MAAM,OAAgB,MAAO,SAAQ,eAAe;IA4FlD;;;;;OAKG;IAEH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAED,IAAI,KAAK,CAAC,KAAa;QACrB,IAAI,QAAQ;YAAE,OAAO;QACrB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAID,IAAI,OAAO;QACT,+BAA+B;QAC/B,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE,CAAmB,CAAC;IACpD,CAAC;IAED;;;;;;OAMG;IAEH,IAAI,aAAa;QACf,+CAA+C;QAC/C,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACpE,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC;IACrB,CAAC;IAED,IAAI,aAAa,CAAC,KAAa;QAC7B,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;QACtC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACH,IAAI,eAAe;QACjB,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;IACrE,CAAC;IAiCD,IAAY,QAAQ;QAClB,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC;IACxC,CAAC;IAeD;QACE,KAAK,EAAE,CAAC;QA1LV;;WAEG;QACwB,UAAK,GAAG,KAAK,CAAC;QAEzC;;WAEG;QACwB,aAAQ,GAAG,KAAK,CAAC;QAE5C;;;;;;;WAOG;QACgD,cAAS,GAAG,EAAE,CAAC;QAElE;;WAEG;QACS,UAAK,GAAG,EAAE,CAAC;QAEvB;;;WAGG;QACkD,eAAU,GAAG,KAAK,CAAC;QAExE;;;WAGG;QACqD,mBAAc,GAAG,EAAE,CAAC;QAE5E;;;;;WAKG;QACuC,UAAK,GAAG,KAAK,CAAC;QAExD;;;;;;WAMG;QAEH,oBAAe,GAAqC,SAAS,CAAC;QAE9D;;WAEG;QAEH,mBAAc,GAAG,KAAK,CAAC;QAEvB;;;WAGG;QAEH,mBAAc,GAAG,6BAA6B,CAAC;QAE/C;;WAEG;QAEH,mBAAc,GAAG,KAAK,CAAC;QAEvB;;WAEG;QACoC,gBAAW,GAAG,EAAE,CAAC;QAExD;;;WAGG;QACkC,cAAS,GAAoB,OAAO,CAAC;QAmB1E,QAAO,GAAG,EAAE,CAAC;QAqCb;;WAEG;QACK,qBAAgB,GAAkB,IAAI,CAAC;QAE/C;;;WAGG;QACK,6BAAwB,GAAkB,IAAI,CAAC;QAEvD;;WAEG;QACK,uBAAkB,GAAwB,IAAI,CAAC;QAEvD,+CAA+C;QACvC,8BAAyB,GAAyB,EAAE,CAAC;QAE7D;;WAEG;QACc,gBAAW,GAAG,KAAK,CAAC;QAErC;;;WAGG;QACc,oBAAe,GAAG,EAAE,CAAC;QAKrB,YAAO,GAAG,KAAK,CAAC;QAChB,SAAI,GAAG,KAAK,CAAC;QACb,iBAAY,GAAe,UAAU,CAAC,IAAI,CAAC;QAM5D,8EAA8E;QAC9E,iDAAiD;QACzC,aAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QACrB,gBAAW,GAAG,CAAC,CAAC;QAItB,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO;QACT,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAa;QAClB,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CACtC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,KAAK,CACnC,CAAC;QACF,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,KAAa;QACvB,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC3C,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK;QACH,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClC,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;IAC5B,CAAC;IAEQ,OAlIR,KAAK,EAkII,gBAAgB,EAAC,CAAC,YAA0B;QACpD,mCAAmC;QACnC,YAAY,EAAE,cAAc,EAAE,CAAC;QAE/B,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACxC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,YAAY,CAAC;QAClC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAE9C,IAAI,WAAW,KAAK,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACxC,IAAI,CAAC,KAAK,EAAE,eAAe,EAAE,CAAC;QAChC,CAAC;IACH,CAAC;IAEkB,MAAM,CAAC,OAA+B;QACvD,uEAAuE;QACvE,wCAAwC;QACxC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC;QAED,gCAAgC;QAChC,2EAA2E;QAC3E,uEAAuE;QACvE,2EAA2E;QAC3E,oCAAoC;QACpC,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACxB,CAAC;IAEkB,MAAM;QACvB,OAAO,IAAI,CAAA;;wBAES,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACrC,IAAI,CAAC,cAAc;UAC7B,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;;KAE5C,CAAC;IACJ,CAAC;IAEkB,KAAK,CAAC,YAAY,CAAC,OAA+B;QACnE,MAAM,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC;QAChC,oEAAoE;QACpE,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,CAAC;YAC3C,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC;QAED,yEAAyE;QACzE,yEAAyE;QACzE,IACE,CAAC,IAAI,CAAC,yBAAyB,CAAC,MAAM;YACtC,CAAC,QAAQ;YACT,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EACpB,CAAC;YACD,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACnC,CAAC,CAAC,CAAC;QACL,CAAC;QAED,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAEO,gBAAgB;QACtB,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,QAAQ;YACzB,OAAO,EAAE,IAAI,CAAC,KAAK;YACnB,MAAM,EAAE,IAAI,CAAC,IAAI;SAClB,CAAC;IACJ,CAAC;IAEO,WAAW;QACjB,MAAM,SAAS,GAAI,IAAwB,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC;QACpE,OAAO,UAAU,CAAA;SACZ,IAAI,CAAC,QAAQ;;;;;qBAKD,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG;uBACxB,SAAS,IAAI,OAAO;;0BAEjB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;;;kBAGpC,IAAI,CAAC,KAAK;yBACH,IAAI,CAAC,UAAU;qBACnB,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI;uBACvB,CAAC,CAAC,IAAI,CAAC,WAAW;sBACnB,IAAI,CAAC,QAAQ;sBACb,IAAI,CAAC,QAAQ;mBAChB,IAAI,CAAC,QAAQ;uBACT,IAAI,CAAC,cAAc;;4BAEd,IAAI,CAAC,cAAc;uBACxB,IAAI,CAAC,YAAY,EAAE;qBACrB,IAAI,CAAC,aAAa;mBACpB,IAAI,CAAC,WAAW;WACxB,IAAI,CAAC,kBAAkB,EAAE;;UAE1B,IAAI,CAAC,QAAQ,GAAG,CAAC;IACzB,CAAC;IAEO,kBAAkB;QACxB,OAAO;YACL,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,kBAAkB,EAAE;SAC1B,CAAC;IACJ,CAAC;IAEO,iBAAiB;QACvB,OAAO,IAAI,CAAA;;gDAEiC,IAAI,CAAC,gBAAgB;;KAEhE,CAAC;IACJ,CAAC;IAEO,kBAAkB;QACxB,OAAO,IAAI,CAAA;;iDAEkC,IAAI,CAAC,gBAAgB;;;;;;;;;;;;;;;KAejE,CAAC;IACJ,CAAC;IAEO,WAAW;QACjB,oEAAoE;QACpE,kBAAkB;QAClB,OAAO,IAAI,CAAA,mBAAmB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAA,QAAQ,QAAQ,CAAC;IACzE,CAAC;IAEO,UAAU;QAChB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,IAAK,IAAwB,CAAC,SAAS,CAAC;QACpE,OAAO,IAAI,CAAA;;;wBAGS,IAAI,CAAC,YAAY;;;qBAGpB,SAAS,IAAI,OAAO;;;;;gBAKzB,QAAQ,CAAC;YACf,oBAAoB,EAAE,GAAG,IAAI,CAAC,WAAW,IAAI;YAC7C,oBAAoB,EAAE,IAAI,CAAC,cAAc;gBACvC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,IAAI;gBACzB,CAAC,CAAC,SAAS;SACd,CAAC;;gBAEM,IAAI,CAAC,IAAI;iBACR,IAAI,CAAC,KAAK;uBACJ,IAAI,CAAC,eAAe;0BACjB,IAAI,CAAC,cAAc;wBACrB,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;sBACtD,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW;mBAC3D,IAAI,CAAC,aAAa;kBACnB,IAAI,CAAC,eAAe;mBACnB,IAAI,CAAC,eAAe;kBACrB,IAAI,CAAC,YAAY;sBACb,IAAI,CAAC,eAAe;6BACb,IAAI,CAAC,sBAAsB;+BACzB,IAAI,CAAC,wBAAwB;UAClD,IAAI,CAAC,iBAAiB,EAAE;;WAEvB,CAAC;IACV,CAAC;IAEO,iBAAiB;QACvB,OAAO,IAAI,CAAA,eAAe,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACK,aAAa,CAAC,KAAoB;QACxC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAC7C,OAAO;QACT,CAAC;QAED,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC;QAC1D,MAAM,SAAS,GACb,KAAK,CAAC,IAAI,KAAK,OAAO;YACtB,KAAK,CAAC,IAAI,KAAK,WAAW;YAC1B,KAAK,CAAC,IAAI,KAAK,SAAS;YACxB,KAAK,CAAC,IAAI,KAAK,KAAK;YACpB,KAAK,CAAC,IAAI,KAAK,MAAM;YACrB,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC;QAEzB,2EAA2E;QAC3E,wCAAwC;QACxC,IAAI,CAAC,mBAAmB,CAAC,aAAa,IAAI,SAAS,EAAE,CAAC;YACpD,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YAEjB,6FAA6F;YAC7F,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,OAAO,CAAC;gBACb,KAAK,WAAW,CAAC;gBACjB,KAAK,OAAO;oBACV,qEAAqE;oBACrE,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC;oBACpC,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,SAAS,CAAC;oBACzC,MAAM;gBACR,KAAK,SAAS,CAAC;gBACf,KAAK,MAAM;oBACT,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC;oBAC1C,MAAM;gBACR;oBACE,MAAM;YACV,CAAC;YACD,OAAO;QACT,CAAC;QAED,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;QAE9C,0EAA0E;QAC1E,4CAA4C;QAC5C,IAAI,cAAc,EAAE,CAAC;YACnB,mBAAmB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACrC,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,MAAM,EAAC,gBAAgB,EAAC,GAAG,mBAAmB,CAAC;YAE/C,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACtB,OAAO;YACT,CAAC;YAED,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YACpD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAChC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAiB,CACxD,CAAC;YAEF,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACnC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IACzB,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IAEO,UAAU;QAChB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,KAAiB;QACtC,+DAA+D;QAC/D,2BAA2B;QAC3B,IAAI,KAAK,CAAC,aAAa,IAAI,kBAAkB,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC;YACzE,OAAO;QACT,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACK,kBAAkB;QACxB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,yBAAyB,GAAG,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAuB,CAAC;QAChD,IAAI,CAAC,yBAAyB,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,yBAAyB,CAAC;IACxC,CAAC;IAEQ,KAAK,CAAC,iBAAiB;QAC9B,MAAM,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC;QAChC,OAAO,KAAK,CAAC,iBAAiB,EAAE,CAAC;IACnC,CAAC;IAED;;;;;OAKG;IACK,yBAAyB;QAC/B,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,IAAI,EAAE,CAAC;QACxD,2EAA2E;QAC3E,0EAA0E;QAC1E,uBAAuB;QACvB,IAAI,wBAAwB,GAAG,KAAK,CAAC;QAErC,IAAI,eAAe,CAAC,MAAM,EAAE,CAAC;YAC3B,MAAM,CAAC,mBAAmB,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACjD,wBAAwB;gBACtB,IAAI,CAAC,kBAAkB,KAAK,mBAAmB,CAAC;YAClD,IAAI,CAAC,kBAAkB,GAAG,mBAAmB,CAAC;YAC9C,IAAI,CAAC,KAAK,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC;YACxC,IAAI,CAAC,WAAW,GAAG,mBAAmB,CAAC,WAAW,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,KAAK,IAAI,CAAC;YAC5D,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;YACjB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACxB,CAAC;QAED,OAAO,wBAAwB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,aAAa,CAAC,CAAQ;QAClC,IAAI,CAAC,OAAO,EAAE,eAAe,EAAE,CAAC,WAAW,CAAC,CAAC;QAC7C,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAExB,wEAAwE;QACxE,sBAAsB;QACtB,IAAI,IAAI,CAAC,YAAY,KAAK,UAAU,CAAC,IAAI,EAAE,CAAC;YAC1C,OAAO;QACT,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,IAAK,CAAC,KAAuB,CAAC;QACjD,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;QAC9C,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEjE,4EAA4E;QAC5E,wEAAwE;QACxE,mBAAmB;QACnB,IAAI,UAAU,IAAI,UAAU,KAAK,YAAY,EAAE,CAAC;YAC9C,UAAU,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QAC3B,CAAC;QAED,6DAA6D;QAC7D,YAAY,GAAG,YAAY,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;QAExC,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,CAAC,QAAQ,GAAG,CAAC,CAAC;YAC1B,YAAY,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,CAAQ;QAC9B,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAC3B,CAAC;IAEO,YAAY,CAAC,CAAQ;QAC3B,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,KAAqB;QAC3C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;QACnC,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAiB,CAAC;QACtD,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,IAAI,MAAM,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;YACtC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YACpE,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,uCAAuC;YACvC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;YACnB,IAAI,CAAC,IAAI,EAAE,CAAC;QACd,CAAC;QAED,yEAAyE;QACzE,YAAY;QACZ,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,UAAU,CAAC,IAAkB;QACnC,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,IAAI,EAAE,CAAC;QACxD,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE;YACnC,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;gBACpB,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,OAAO,IAAI,CAAC,yBAAyB,EAAE,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACK,sBAAsB,CAC5B,KAAqD;QAErD,MAAM,kBAAkB,GAAG,KAAK,CAAC,MAAoC,CAAC;QAEtE,0CAA0C;QAC1C,IACE,IAAI,CAAC,yBAAyB,CAAC,IAAI,CACjC,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,MAAM,KAAK,kBAAkB,CAC5C,EACD,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC;IACtC,CAAC;IAED;;;OAGG;IACK,wBAAwB,CAC9B,KAAuD;QAEvD,MAAM,kBAAkB,GAAG,KAAK,CAAC,MAAoC,CAAC;QAEtE,wEAAwE;QACxE,IACE,CAAC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAClC,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,MAAM,KAAK,kBAAkB,CAC5C,EACD,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAED;;;OAGG;IACK,iBAAiB;QACvB,wEAAwE;QACxE,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,CAAC;YACpE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAEnC,qEAAqE;YACrE,aAAa;QACf,CAAC;aAAM,IACL,IAAI,CAAC,wBAAwB,KAAK,IAAI;YACtC,CAAC,IAAI,CAAC,yBAAyB,CAAC,MAAM,EACtC,CAAC;YACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAEhD,mBAAmB;QACrB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACK,yBAAyB;QAC/B,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;QACxE,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,YAAY;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;IAC5D,CAAC;IAMQ,CAAC,YAAY,CAAC;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAEQ,iBAAiB;QACxB,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEQ,wBAAwB,CAAC,KAAa;QAC7C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAEQ,KAAK;QACZ,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;IACtB,CAAC;IAEQ,CAAC,eAAe,CAAC;QACxB,OAAO,IAAI,eAAe,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAEQ,CAAC,iBAAiB,CAAC;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;;AAhwBD,kBAAkB;AACF,wBAAiB,GAAG;IAClC,GAAG,UAAU,CAAC,iBAAiB;IAC/B,cAAc,EAAE,IAAI;CACrB,AAHgC,CAG/B;AAKyB;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;qCAAe;AAKd;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;wCAAkB;AAUO;IAAlD,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAC,CAAC;yCAAgB;AAKtD;IAAX,QAAQ,EAAE;qCAAY;AAM8B;IAApD,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,aAAa,EAAC,CAAC;0CAAoB;AAMhB;IAAvD,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAC,CAAC;8CAAqB;AAQlC;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;qCAAe;AAUxD;IADC,QAAQ,CAAC,EAAC,SAAS,EAAE,kBAAkB,EAAC,CAAC;+CACoB;AAM9D;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAC,CAAC;8CAClC;AAOvB;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAC,CAAC;8CACR;AAM/C;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAC,CAAC;8CAClC;AAKgB;IAAtC,QAAQ,CAAC,EAAC,SAAS,EAAE,cAAc,EAAC,CAAC;2CAAkB;AAMnB;IAApC,QAAQ,CAAC,EAAC,SAAS,EAAE,YAAY,EAAC,CAAC;yCAAsC;AAS1E;IADC,QAAQ,EAAE;mCAGV;AAuBD;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAC;2CAKrD;AAwCgB;IAAhB,KAAK,EAAE;2CAA6B;AAMpB;IAAhB,KAAK,EAAE;+CAA8B;AAKrB;IAAhB,KAAK,EAAE;uCAAyB;AAChB;IAAhB,KAAK,EAAE;oCAAsB;AACb;IAAhB,KAAK,EAAE;4CAAoD;AAC1B;IAAjC,KAAK,CAAC,QAAQ,CAAC;qCAAuC;AACpB;IAAlC,KAAK,CAAC,SAAS,CAAC;oCAAqC;AACpB;IAAjC,KAAK,CAAC,QAAQ,CAAC;uCAAwC;AAEvC;IADhB,qBAAqB,CAAC,EAAC,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;4CACnB", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../menu/menu.js';\n\nimport {html, isServer, LitElement, nothing, PropertyValues} from 'lit';\nimport {property, query, queryAssignedElements, state} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\nimport {styleMap} from 'lit/directives/style-map.js';\nimport {html as staticHtml, StaticValue} from 'lit/static-html.js';\n\nimport {Field} from '../../field/internal/field.js';\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\nimport {redispatchEvent} from '../../internal/events/redispatch-event.js';\nimport {\n  createValidator,\n  getValidityAnchor,\n  mixinConstraintValidation,\n} from '../../labs/behaviors/constraint-validation.js';\nimport {mixinElementInternals} from '../../labs/behaviors/element-internals.js';\nimport {\n  getFormValue,\n  mixinFormAssociated,\n} from '../../labs/behaviors/form-associated.js';\nimport {\n  mixinOnReportValidity,\n  onReportValidity,\n} from '../../labs/behaviors/on-report-validity.js';\nimport {SelectValidator} from '../../labs/behaviors/validators/select-validator.js';\nimport {getActiveItem} from '../../list/internal/list-navigation-helpers.js';\nimport {\n  CloseMenuEvent,\n  FocusState,\n  isElementInSubtree,\n  isSelectableKey,\n} from '../../menu/internal/controllers/shared.js';\nimport {TYPEAHEAD_RECORD} from '../../menu/internal/controllers/typeaheadController.js';\nimport {DEFAULT_TYPEAHEAD_BUFFER_TIME, Menu} from '../../menu/internal/menu.js';\nimport {SelectOption} from './selectoption/select-option.js';\nimport {\n  createRequestDeselectionEvent,\n  createRequestSelectionEvent,\n} from './selectoption/selectOptionController.js';\nimport {getSelectedItems, SelectOptionRecord} from './shared.js';\n\nconst VALUE = Symbol('value');\n\n// Separate variable needed for closure.\nconst selectBaseClass = mixinDelegatesAria(\n  mixinOnReportValidity(\n    mixinConstraintValidation(\n      mixinFormAssociated(mixinElementInternals(LitElement)),\n    ),\n  ),\n);\n\n/**\n * @fires change {Event} The native `change` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/change_event)\n * --bubbles\n * @fires input {InputEvent} The native `input` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/input_event)\n * --bubbles --composed\n * @fires opening {Event} Fired when the select's menu is about to open.\n * @fires opened {Event} Fired when the select's menu has finished animations\n * and opened.\n * @fires closing {Event} Fired when the select's menu is about to close.\n * @fires closed {Event} Fired when the select's menu has finished animations\n * and closed.\n */\nexport abstract class Select extends selectBaseClass {\n  /** @nocollapse */\n  static override shadowRootOptions = {\n    ...LitElement.shadowRootOptions,\n    delegatesFocus: true,\n  };\n\n  /**\n   * Opens the menu synchronously with no animation.\n   */\n  @property({type: Boolean}) quick = false;\n\n  /**\n   * Whether or not the select is required.\n   */\n  @property({type: Boolean}) required = false;\n\n  /**\n   * The error message that replaces supporting text when `error` is true. If\n   * `errorText` is an empty string, then the supporting text will continue to\n   * show.\n   *\n   * This error message overrides the error message displayed by\n   * `reportValidity()`.\n   */\n  @property({type: String, attribute: 'error-text'}) errorText = '';\n\n  /**\n   * The floating label for the field.\n   */\n  @property() label = '';\n\n  /**\n   * Disables the asterisk on the floating label, when the select is\n   * required.\n   */\n  @property({type: Boolean, attribute: 'no-asterisk'}) noAsterisk = false;\n\n  /**\n   * Conveys additional information below the select, such as how it should\n   * be used.\n   */\n  @property({type: String, attribute: 'supporting-text'}) supportingText = '';\n\n  /**\n   * Gets or sets whether or not the select is in a visually invalid state.\n   *\n   * This error state overrides the error state controlled by\n   * `reportValidity()`.\n   */\n  @property({type: Boolean, reflect: true}) error = false;\n\n  /**\n   * Whether or not the underlying md-menu should be position: fixed to display\n   * in a top-level manner, or position: absolute.\n   *\n   * position:fixed is useful for cases where select is inside of another\n   * element with stacking context and hidden overflows such as `md-dialog`.\n   */\n  @property({attribute: 'menu-positioning'})\n  menuPositioning: 'absolute' | 'fixed' | 'popover' = 'popover';\n\n  /**\n   * Clamps the menu-width to the width of the select.\n   */\n  @property({type: Boolean, attribute: 'clamp-menu-width'})\n  clampMenuWidth = false;\n\n  /**\n   * The max time between the keystrokes of the typeahead select / menu behavior\n   * before it clears the typeahead buffer.\n   */\n  @property({type: Number, attribute: 'typeahead-delay'})\n  typeaheadDelay = DEFAULT_TYPEAHEAD_BUFFER_TIME;\n\n  /**\n   * Whether or not the text field has a leading icon. Used for SSR.\n   */\n  @property({type: Boolean, attribute: 'has-leading-icon'})\n  hasLeadingIcon = false;\n\n  /**\n   * Text to display in the field. Only set for SSR.\n   */\n  @property({attribute: 'display-text'}) displayText = '';\n\n  /**\n   * Whether the menu should be aligned to the start or the end of the select's\n   * textbox.\n   */\n  @property({attribute: 'menu-align'}) menuAlign: 'start' | 'end' = 'start';\n\n  /**\n   * The value of the currently selected option.\n   *\n   * Note: For SSR, set `[selected]` on the requested option and `displayText`\n   * rather than setting `value` setting `value` will incur a DOM query.\n   */\n  @property()\n  get value(): string {\n    return this[VALUE];\n  }\n\n  set value(value: string) {\n    if (isServer) return;\n    this.lastUserSetValue = value;\n    this.select(value);\n  }\n\n  [VALUE] = '';\n\n  get options() {\n    // NOTE: this does a DOM query.\n    return (this.menu?.items ?? []) as SelectOption[];\n  }\n\n  /**\n   * The index of the currently selected option.\n   *\n   * Note: For SSR, set `[selected]` on the requested option and `displayText`\n   * rather than setting `selectedIndex` setting `selectedIndex` will incur a\n   * DOM query.\n   */\n  @property({type: Number, attribute: 'selected-index'})\n  get selectedIndex(): number {\n    // tslint:disable-next-line:enforce-name-casing\n    const [_option, index] = (this.getSelectedOptions() ?? [])[0] ?? [];\n    return index ?? -1;\n  }\n\n  set selectedIndex(index: number) {\n    this.lastUserSetSelectedIndex = index;\n    this.selectIndex(index);\n  }\n\n  /**\n   * Returns an array of selected options.\n   *\n   * NOTE: md-select only supports single selection.\n   */\n  get selectedOptions() {\n    return (this.getSelectedOptions() ?? []).map(([option]) => option);\n  }\n\n  protected abstract readonly fieldTag: StaticValue;\n\n  /**\n   * Used for initializing select when the user sets the `value` directly.\n   */\n  private lastUserSetValue: string | null = null;\n\n  /**\n   * Used for initializing select when the user sets the `selectedIndex`\n   * directly.\n   */\n  private lastUserSetSelectedIndex: number | null = null;\n\n  /**\n   * Used for `input` and `change` event change detection.\n   */\n  private lastSelectedOption: SelectOption | null = null;\n\n  // tslint:disable-next-line:enforce-name-casing\n  private lastSelectedOptionRecords: SelectOptionRecord[] = [];\n\n  /**\n   * Whether or not a native error has been reported via `reportValidity()`.\n   */\n  @state() private nativeError = false;\n\n  /**\n   * The validation message displayed from a native error via\n   * `reportValidity()`.\n   */\n  @state() private nativeErrorText = '';\n  private get hasError() {\n    return this.error || this.nativeError;\n  }\n\n  @state() private focused = false;\n  @state() private open = false;\n  @state() private defaultFocus: FocusState = FocusState.NONE;\n  @query('.field') private readonly field!: Field | null;\n  @query('md-menu') private readonly menu!: Menu | null;\n  @query('#label') private readonly labelEl!: HTMLElement;\n  @queryAssignedElements({slot: 'leading-icon', flatten: true})\n  private readonly leadingIcons!: Element[];\n  // Have to keep track of previous open because it's state and private and thus\n  // cannot be tracked in PropertyValues<this> map.\n  private prevOpen = this.open;\n  private selectWidth = 0;\n\n  constructor() {\n    super();\n    if (isServer) {\n      return;\n    }\n\n    this.addEventListener('focus', this.handleFocus.bind(this));\n    this.addEventListener('blur', this.handleBlur.bind(this));\n  }\n\n  /**\n   * Selects an option given the value of the option, and updates MdSelect's\n   * value.\n   */\n  select(value: string) {\n    const optionToSelect = this.options.find(\n      (option) => option.value === value,\n    );\n    if (optionToSelect) {\n      this.selectItem(optionToSelect);\n    }\n  }\n\n  /**\n   * Selects an option given the index of the option, and updates MdSelect's\n   * value.\n   */\n  selectIndex(index: number) {\n    const optionToSelect = this.options[index];\n    if (optionToSelect) {\n      this.selectItem(optionToSelect);\n    }\n  }\n\n  /**\n   * Reset the select to its default value.\n   */\n  reset() {\n    for (const option of this.options) {\n      option.selected = option.hasAttribute('selected');\n    }\n\n    this.updateValueAndDisplayText();\n    this.nativeError = false;\n    this.nativeErrorText = '';\n  }\n\n  override [onReportValidity](invalidEvent: Event | null) {\n    // Prevent default pop-up behavior.\n    invalidEvent?.preventDefault();\n\n    const prevMessage = this.getErrorText();\n    this.nativeError = !!invalidEvent;\n    this.nativeErrorText = this.validationMessage;\n\n    if (prevMessage === this.getErrorText()) {\n      this.field?.reannounceError();\n    }\n  }\n\n  protected override update(changed: PropertyValues<Select>) {\n    // In SSR the options will be ready to query, so try to figure out what\n    // the value and display text should be.\n    if (!this.hasUpdated) {\n      this.initUserSelection();\n    }\n\n    // We have just opened the menu.\n    // We are only able to check for the select's rect in `update()` instead of\n    // having to wait for `updated()` because the menu can never be open on\n    // first render since it is not settable and Lit SSR does not support click\n    // events which would open the menu.\n    if (this.prevOpen !== this.open && this.open) {\n      const selectRect = this.getBoundingClientRect();\n      this.selectWidth = selectRect.width;\n    }\n\n    this.prevOpen = this.open;\n    super.update(changed);\n  }\n\n  protected override render() {\n    return html`\n      <span\n        class=\"select ${classMap(this.getRenderClasses())}\"\n        @focusout=${this.handleFocusout}>\n        ${this.renderField()} ${this.renderMenu()}\n      </span>\n    `;\n  }\n\n  protected override async firstUpdated(changed: PropertyValues<Select>) {\n    await this.menu?.updateComplete;\n    // If this has been handled on update already due to SSR, try again.\n    if (!this.lastSelectedOptionRecords.length) {\n      this.initUserSelection();\n    }\n\n    // Case for when the DOM is streaming, there are no children, and a child\n    // has [selected] set on it, we need to wait for DOM to render something.\n    if (\n      !this.lastSelectedOptionRecords.length &&\n      !isServer &&\n      !this.options.length\n    ) {\n      setTimeout(() => {\n        this.updateValueAndDisplayText();\n      });\n    }\n\n    super.firstUpdated(changed);\n  }\n\n  private getRenderClasses(): ClassInfo {\n    return {\n      'disabled': this.disabled,\n      'error': this.error,\n      'open': this.open,\n    };\n  }\n\n  private renderField() {\n    const ariaLabel = (this as ARIAMixinStrict).ariaLabel || this.label;\n    return staticHtml`\n      <${this.fieldTag}\n          aria-haspopup=\"listbox\"\n          role=\"combobox\"\n          part=\"field\"\n          id=\"field\"\n          tabindex=${this.disabled ? '-1' : '0'}\n          aria-label=${ariaLabel || nothing}\n          aria-describedby=\"description\"\n          aria-expanded=${this.open ? 'true' : 'false'}\n          aria-controls=\"listbox\"\n          class=\"field\"\n          label=${this.label}\n          ?no-asterisk=${this.noAsterisk}\n          .focused=${this.focused || this.open}\n          .populated=${!!this.displayText}\n          .disabled=${this.disabled}\n          .required=${this.required}\n          .error=${this.hasError}\n          ?has-start=${this.hasLeadingIcon}\n          has-end\n          supporting-text=${this.supportingText}\n          error-text=${this.getErrorText()}\n          @keydown=${this.handleKeydown}\n          @click=${this.handleClick}>\n         ${this.renderFieldContent()}\n         <div id=\"description\" slot=\"aria-describedby\"></div>\n      </${this.fieldTag}>`;\n  }\n\n  private renderFieldContent() {\n    return [\n      this.renderLeadingIcon(),\n      this.renderLabel(),\n      this.renderTrailingIcon(),\n    ];\n  }\n\n  private renderLeadingIcon() {\n    return html`\n      <span class=\"icon leading\" slot=\"start\">\n        <slot name=\"leading-icon\" @slotchange=${this.handleIconChange}></slot>\n      </span>\n    `;\n  }\n\n  private renderTrailingIcon() {\n    return html`\n      <span class=\"icon trailing\" slot=\"end\">\n        <slot name=\"trailing-icon\" @slotchange=${this.handleIconChange}>\n          <svg height=\"5\" viewBox=\"7 10 10 5\" focusable=\"false\">\n            <polygon\n              class=\"down\"\n              stroke=\"none\"\n              fill-rule=\"evenodd\"\n              points=\"7 10 12 15 17 10\"></polygon>\n            <polygon\n              class=\"up\"\n              stroke=\"none\"\n              fill-rule=\"evenodd\"\n              points=\"7 15 12 10 17 15\"></polygon>\n          </svg>\n        </slot>\n      </span>\n    `;\n  }\n\n  private renderLabel() {\n    // need to render &nbsp; so that line-height can apply and give it a\n    // non-zero height\n    return html`<div id=\"label\">${this.displayText || html`&nbsp;`}</div>`;\n  }\n\n  private renderMenu() {\n    const ariaLabel = this.label || (this as ARIAMixinStrict).ariaLabel;\n    return html`<div class=\"menu-wrapper\">\n      <md-menu\n        id=\"listbox\"\n        .defaultFocus=${this.defaultFocus}\n        role=\"listbox\"\n        tabindex=\"-1\"\n        aria-label=${ariaLabel || nothing}\n        stay-open-on-focusout\n        part=\"menu\"\n        exportparts=\"focus-ring: menu-focus-ring\"\n        anchor=\"field\"\n        style=${styleMap({\n          '--__menu-min-width': `${this.selectWidth}px`,\n          '--__menu-max-width': this.clampMenuWidth\n            ? `${this.selectWidth}px`\n            : undefined,\n        })}\n        no-navigation-wrap\n        .open=${this.open}\n        .quick=${this.quick}\n        .positioning=${this.menuPositioning}\n        .typeaheadDelay=${this.typeaheadDelay}\n        .anchorCorner=${this.menuAlign === 'start' ? 'end-start' : 'end-end'}\n        .menuCorner=${this.menuAlign === 'start' ? 'start-start' : 'start-end'}\n        @opening=${this.handleOpening}\n        @opened=${this.redispatchEvent}\n        @closing=${this.redispatchEvent}\n        @closed=${this.handleClosed}\n        @close-menu=${this.handleCloseMenu}\n        @request-selection=${this.handleRequestSelection}\n        @request-deselection=${this.handleRequestDeselection}>\n        ${this.renderMenuContent()}\n      </md-menu>\n    </div>`;\n  }\n\n  private renderMenuContent() {\n    return html`<slot></slot>`;\n  }\n\n  /**\n   * Handles opening the select on keydown and typahead selection when the menu\n   * is closed.\n   */\n  private handleKeydown(event: KeyboardEvent) {\n    if (this.open || this.disabled || !this.menu) {\n      return;\n    }\n\n    const typeaheadController = this.menu.typeaheadController;\n    const isOpenKey =\n      event.code === 'Space' ||\n      event.code === 'ArrowDown' ||\n      event.code === 'ArrowUp' ||\n      event.code === 'End' ||\n      event.code === 'Home' ||\n      event.code === 'Enter';\n\n    // Do not open if currently typing ahead because the user may be typing the\n    // spacebar to match a word with a space\n    if (!typeaheadController.isTypingAhead && isOpenKey) {\n      event.preventDefault();\n      this.open = true;\n\n      // https://www.w3.org/WAI/ARIA/apg/patterns/combobox/examples/combobox-select-only/#kbd_label\n      switch (event.code) {\n        case 'Space':\n        case 'ArrowDown':\n        case 'Enter':\n          // We will handle focusing last selected item in this.handleOpening()\n          this.defaultFocus = FocusState.NONE;\n          break;\n        case 'End':\n          this.defaultFocus = FocusState.LAST_ITEM;\n          break;\n        case 'ArrowUp':\n        case 'Home':\n          this.defaultFocus = FocusState.FIRST_ITEM;\n          break;\n        default:\n          break;\n      }\n      return;\n    }\n\n    const isPrintableKey = event.key.length === 1;\n\n    // Handles typing ahead when the menu is closed by delegating the event to\n    // the underlying menu's typeaheadController\n    if (isPrintableKey) {\n      typeaheadController.onKeydown(event);\n      event.preventDefault();\n\n      const {lastActiveRecord} = typeaheadController;\n\n      if (!lastActiveRecord) {\n        return;\n      }\n\n      this.labelEl?.setAttribute?.('aria-live', 'polite');\n      const hasChanged = this.selectItem(\n        lastActiveRecord[TYPEAHEAD_RECORD.ITEM] as SelectOption,\n      );\n\n      if (hasChanged) {\n        this.dispatchInteractionEvents();\n      }\n    }\n  }\n\n  private handleClick() {\n    this.open = !this.open;\n  }\n\n  private handleFocus() {\n    this.focused = true;\n  }\n\n  private handleBlur() {\n    this.focused = false;\n  }\n\n  /**\n   * Handles closing the menu when the focus leaves the select's subtree.\n   */\n  private handleFocusout(event: FocusEvent) {\n    // Don't close the menu if we are switching focus between menu,\n    // select-option, and field\n    if (event.relatedTarget && isElementInSubtree(event.relatedTarget, this)) {\n      return;\n    }\n\n    this.open = false;\n  }\n\n  /**\n   * Gets a list of all selected select options as a list item record array.\n   *\n   * @return An array of selected list option records.\n   */\n  private getSelectedOptions() {\n    if (!this.menu) {\n      this.lastSelectedOptionRecords = [];\n      return null;\n    }\n\n    const items = this.menu.items as SelectOption[];\n    this.lastSelectedOptionRecords = getSelectedItems(items);\n    return this.lastSelectedOptionRecords;\n  }\n\n  override async getUpdateComplete() {\n    await this.menu?.updateComplete;\n    return super.getUpdateComplete();\n  }\n\n  /**\n   * Gets the selected options from the DOM, and updates the value and display\n   * text to the first selected option's value and headline respectively.\n   *\n   * @return Whether or not the selected option has changed since last update.\n   */\n  private updateValueAndDisplayText() {\n    const selectedOptions = this.getSelectedOptions() ?? [];\n    // Used to determine whether or not we need to fire an input / change event\n    // which fire whenever the option element changes (value or selectedIndex)\n    // on user interaction.\n    let hasSelectedOptionChanged = false;\n\n    if (selectedOptions.length) {\n      const [firstSelectedOption] = selectedOptions[0];\n      hasSelectedOptionChanged =\n        this.lastSelectedOption !== firstSelectedOption;\n      this.lastSelectedOption = firstSelectedOption;\n      this[VALUE] = firstSelectedOption.value;\n      this.displayText = firstSelectedOption.displayText;\n    } else {\n      hasSelectedOptionChanged = this.lastSelectedOption !== null;\n      this.lastSelectedOption = null;\n      this[VALUE] = '';\n      this.displayText = '';\n    }\n\n    return hasSelectedOptionChanged;\n  }\n\n  /**\n   * Focuses and activates the last selected item upon opening, and resets other\n   * active items.\n   */\n  private async handleOpening(e: Event) {\n    this.labelEl?.removeAttribute?.('aria-live');\n    this.redispatchEvent(e);\n\n    // FocusState.NONE means we want to handle focus ourselves and focus the\n    // last selected item.\n    if (this.defaultFocus !== FocusState.NONE) {\n      return;\n    }\n\n    const items = this.menu!.items as SelectOption[];\n    const activeItem = getActiveItem(items)?.item;\n    let [selectedItem] = this.lastSelectedOptionRecords[0] ?? [null];\n\n    // This is true if the user keys through the list but clicks out of the menu\n    // thus no close-menu event is fired by an item and we can't clean up in\n    // handleCloseMenu.\n    if (activeItem && activeItem !== selectedItem) {\n      activeItem.tabIndex = -1;\n    }\n\n    // in the case that nothing is selected, focus the first item\n    selectedItem = selectedItem ?? items[0];\n\n    if (selectedItem) {\n      selectedItem.tabIndex = 0;\n      selectedItem.focus();\n    }\n  }\n\n  private redispatchEvent(e: Event) {\n    redispatchEvent(this, e);\n  }\n\n  private handleClosed(e: Event) {\n    this.open = false;\n    this.redispatchEvent(e);\n  }\n\n  /**\n   * Determines the reason for closing, and updates the UI accordingly.\n   */\n  private handleCloseMenu(event: CloseMenuEvent) {\n    const reason = event.detail.reason;\n    const item = event.detail.itemPath[0] as SelectOption;\n    this.open = false;\n    let hasChanged = false;\n\n    if (reason.kind === 'click-selection') {\n      hasChanged = this.selectItem(item);\n    } else if (reason.kind === 'keydown' && isSelectableKey(reason.key)) {\n      hasChanged = this.selectItem(item);\n    } else {\n      // This can happen on ESC being pressed\n      item.tabIndex = -1;\n      item.blur();\n    }\n\n    // Dispatch interaction events since selection has been made via keyboard\n    // or mouse.\n    if (hasChanged) {\n      this.dispatchInteractionEvents();\n    }\n  }\n\n  /**\n   * Selects a given option, deselects other options, and updates the UI.\n   *\n   * @return Whether the last selected option has changed.\n   */\n  private selectItem(item: SelectOption) {\n    const selectedOptions = this.getSelectedOptions() ?? [];\n    selectedOptions.forEach(([option]) => {\n      if (item !== option) {\n        option.selected = false;\n      }\n    });\n    item.selected = true;\n\n    return this.updateValueAndDisplayText();\n  }\n\n  /**\n   * Handles updating selection when an option element requests selection via\n   * property / attribute change.\n   */\n  private handleRequestSelection(\n    event: ReturnType<typeof createRequestSelectionEvent>,\n  ) {\n    const requestingOptionEl = event.target as SelectOption & HTMLElement;\n\n    // No-op if this item is already selected.\n    if (\n      this.lastSelectedOptionRecords.some(\n        ([option]) => option === requestingOptionEl,\n      )\n    ) {\n      return;\n    }\n\n    this.selectItem(requestingOptionEl);\n  }\n\n  /**\n   * Handles updating selection when an option element requests deselection via\n   * property / attribute change.\n   */\n  private handleRequestDeselection(\n    event: ReturnType<typeof createRequestDeselectionEvent>,\n  ) {\n    const requestingOptionEl = event.target as SelectOption & HTMLElement;\n\n    // No-op if this item is not even in the list of tracked selected items.\n    if (\n      !this.lastSelectedOptionRecords.some(\n        ([option]) => option === requestingOptionEl,\n      )\n    ) {\n      return;\n    }\n\n    this.updateValueAndDisplayText();\n  }\n\n  /**\n   * Attempts to initialize the selected option from user-settable values like\n   * SSR, setting `value`, or `selectedIndex` at startup.\n   */\n  private initUserSelection() {\n    // User has set `.value` directly, but internals have not yet booted up.\n    if (this.lastUserSetValue && !this.lastSelectedOptionRecords.length) {\n      this.select(this.lastUserSetValue);\n\n      // User has set `.selectedIndex` directly, but internals have not yet\n      // booted up.\n    } else if (\n      this.lastUserSetSelectedIndex !== null &&\n      !this.lastSelectedOptionRecords.length\n    ) {\n      this.selectIndex(this.lastUserSetSelectedIndex);\n\n      // Regular boot up!\n    } else {\n      this.updateValueAndDisplayText();\n    }\n  }\n\n  private handleIconChange() {\n    this.hasLeadingIcon = this.leadingIcons.length > 0;\n  }\n\n  /**\n   * Dispatches the `input` and `change` events.\n   */\n  private dispatchInteractionEvents() {\n    this.dispatchEvent(new Event('input', {bubbles: true, composed: true}));\n    this.dispatchEvent(new Event('change', {bubbles: true}));\n  }\n\n  private getErrorText() {\n    return this.error ? this.errorText : this.nativeErrorText;\n  }\n\n  // Writable mixin properties for lit-html binding, needed for lit-analyzer\n  declare disabled: boolean;\n  declare name: string;\n\n  override [getFormValue]() {\n    return this.value;\n  }\n\n  override formResetCallback() {\n    this.reset();\n  }\n\n  override formStateRestoreCallback(state: string) {\n    this.value = state;\n  }\n\n  override click() {\n    this.field?.click();\n  }\n\n  override [createValidator]() {\n    return new SelectValidator(() => this);\n  }\n\n  override [getValidityAnchor]() {\n    return this.field;\n  }\n}\n"]}