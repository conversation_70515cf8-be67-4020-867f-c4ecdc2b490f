//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use './internal/validate';
@use './md-sys-color';
@use './v0_192/md-comp-divider';
// go/keep-sorted end

$supported-tokens: (
  // go/keep-sorted start
  'color',
  'thickness',
  // go/keep-sorted end
);

$_default: (
  'md-sys-color': md-sys-color.values-light(),
);

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $tokens: validate.values(
    md-comp-divider.values($deps, $exclude-hardcoded-values),
    $supported-tokens: $supported-tokens
  );

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      $tokens: map.set($tokens, $token, var(--md-divider-#{$token}, #{$value}));
    }
  }

  @return $tokens;
}
