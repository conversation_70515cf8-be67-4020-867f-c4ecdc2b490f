//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use './internal/validate';
@use './md-sys-color';
@use './md-sys-shape';
@use './v0_192/md-comp-linear-progress-indicator';
// go/keep-sorted end

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-shape': md-sys-shape.values(),
);

$supported-tokens: (
  // go/keep-sorted start
  'active-indicator-color',
  'active-indicator-height',
  'four-color-active-indicator-four-color',
  'four-color-active-indicator-one-color',
  'four-color-active-indicator-three-color',
  'four-color-active-indicator-two-color',
  'track-color',
  'track-height',
  'track-shape',
  // go/keep-sorted end
);

$unsupported-tokens: (
  // go/keep-sorted start
  // can only control track since scaling is used on buffer/progress
  'active-indicator-shape',
  // go/keep-sorted end
);

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $tokens: validate.values(
    md-comp-linear-progress-indicator.values($deps, $exclude-hardcoded-values),
    $supported-tokens: $supported-tokens,
    $unsupported-tokens: $unsupported-tokens
  );

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      $tokens: map.set(
        $tokens,
        $token,
        var(--md-linear-progress-#{$token}, #{$value})
      );
    }
  }

  @return $tokens;
}
