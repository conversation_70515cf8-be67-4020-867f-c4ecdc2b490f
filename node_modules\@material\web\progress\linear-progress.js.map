{"version": 3, "file": "linear-progress.js", "sourceRoot": "", "sources": ["linear-progress.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,cAAc,EAAC,MAAM,+BAA+B,CAAC;AAC7D,OAAO,EAAC,MAAM,EAAC,MAAM,sCAAsC,CAAC;AAQ5D;;;;;;;;;;;GAWG;AAEI,IAAM,gBAAgB,GAAtB,MAAM,gBAAiB,SAAQ,cAAc;;AAClC,uBAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAD5C,gBAAgB;IAD5B,aAAa,CAAC,oBAAoB,CAAC;GACvB,gBAAgB,CAE5B", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {LinearProgress} from './internal/linear-progress.js';\nimport {styles} from './internal/linear-progress-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-linear-progress': MdLinearProgress;\n  }\n}\n\n/**\n * @summary Linear progress indicators display progress by animating along the\n * length of a fixed, visible track.\n *\n * @description\n * Progress indicators inform users about the status of ongoing processes.\n * - Determinate indicators display how long a process will take.\n * - Indeterminate indicators express an unspecified amount of wait time.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-linear-progress')\nexport class MdLinearProgress extends LinearProgress {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}