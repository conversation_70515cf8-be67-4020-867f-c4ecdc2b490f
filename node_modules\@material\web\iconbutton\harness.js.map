{"version": 3, "file": "harness.js", "sourceRoot": "", "sources": ["harness.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,OAAO,EAAC,MAAM,uBAAuB,CAAC;AAI9C;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,OAAmB;IACrC,KAAK,CAAC,qBAAqB;QAC5C,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;QAClC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,GAAG,CAAE,CAAC;QACrD,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAE,CAAC;IAC1D,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {Harness} from '../testing/harness.js';\n\nimport {IconButton} from './internal/icon-button.js';\n\n/**\n * Test harness for icon buttons.\n */\nexport class IconButtonHarness extends Harness<IconButton> {\n  protected override async getInteractiveElement() {\n    await this.element.updateComplete;\n    if (this.element.href) {\n      return this.element.renderRoot.querySelector('a')!;\n    }\n\n    return this.element.renderRoot.querySelector('button')!;\n  }\n}\n"]}