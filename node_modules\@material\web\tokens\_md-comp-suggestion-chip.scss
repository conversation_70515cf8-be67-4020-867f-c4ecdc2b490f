//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
@use 'sass:string';
// go/keep-sorted end
// go/keep-sorted start
@use './internal/shape';
@use './internal/validate';
@use './md-sys-color';
@use './md-sys-elevation';
@use './md-sys-shape';
@use './md-sys-state';
@use './md-sys-typescale';
@use './v0_192/md-comp-suggestion-chip';
// go/keep-sorted end

$supported-tokens: (
  // go/keep-sorted start
  'container-height',
  'container-shape',
  'container-shape-end-end',
  'container-shape-end-start',
  'container-shape-start-end',
  'container-shape-start-start',
  'disabled-label-text-color',
  'disabled-label-text-opacity',
  'disabled-leading-icon-color',
  'disabled-leading-icon-opacity',
  'disabled-outline-color',
  'disabled-outline-opacity',
  'elevated-container-color',
  'elevated-container-elevation',
  'elevated-container-shadow-color',
  'elevated-disabled-container-color',
  'elevated-disabled-container-elevation',
  'elevated-disabled-container-opacity',
  'elevated-focus-container-elevation',
  'elevated-hover-container-elevation',
  'elevated-pressed-container-elevation',
  'focus-label-text-color',
  'focus-leading-icon-color',
  'focus-outline-color',
  'hover-label-text-color',
  'hover-leading-icon-color',
  'hover-state-layer-color',
  'hover-state-layer-opacity',
  'icon-label-space',
  'icon-size',
  'label-text-color',
  'label-text-font',
  'label-text-line-height',
  'label-text-size',
  'label-text-weight',
  'leading-icon-color',
  'leading-space',
  'outline-color',
  'outline-width',
  'pressed-label-text-color',
  'pressed-leading-icon-color',
  'pressed-state-layer-color',
  'pressed-state-layer-opacity',
  'trailing-space',
  'with-leading-icon-leading-space',
  // go/keep-sorted end
);

$unsupported-tokens: (
  // go/keep-sorted start
  'container-elevation',
  'dragged-container-elevation',
  'dragged-label-text-color',
  'dragged-leading-icon-color',
  'dragged-state-layer-color',
  'dragged-state-layer-opacity',
  'focus-state-layer-color',
  'focus-state-layer-opacity',
  'label-text-tracking',
  'label-text-type',
  // go/keep-sorted end
);

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $tokens: md-comp-suggestion-chip.values($deps, $exclude-hardcoded-values);
  $new-tokens: map.merge(
    shape.get-new-logical-shape-tokens($tokens, 'container-shape'),
    (
      'leading-space': if($exclude-hardcoded-values, null, 16px),
      'trailing-space': if($exclude-hardcoded-values, null, 16px),
      'icon-label-space': if($exclude-hardcoded-values, null, 8px),
      'with-leading-icon-leading-space':
        if($exclude-hardcoded-values, null, 8px),
    )
  );

  $tokens: validate.values(
    $tokens,
    $supported-tokens: $supported-tokens,
    $unsupported-tokens: $unsupported-tokens,
    $new-tokens: $new-tokens,
    $renamed-tokens: (
      // Remove "flat-*" prefix (b/275577569)
      'flat-container-elevation': 'container-elevation',
      'flat-disabled-outline-color': 'disabled-outline-color',
      'flat-disabled-outline-opacity': 'disabled-outline-opacity',
      'flat-focus-outline-color': 'focus-outline-color',
      'flat-outline-color': 'outline-color',
      'flat-outline-width': 'outline-width',
      // Remove "with-*" prefixes (b/273534858)
      'with-leading-icon-disabled-leading-icon-color':
        'disabled-leading-icon-color',
      'with-leading-icon-disabled-leading-icon-opacity':
        'disabled-leading-icon-opacity',
      'with-leading-icon-dragged-leading-icon-color':
        'dragged-leading-icon-color',
      'with-leading-icon-focus-leading-icon-color': 'focus-leading-icon-color',
      'with-leading-icon-hover-leading-icon-color': 'hover-leading-icon-color',
      'with-leading-icon-leading-icon-color': 'leading-icon-color',
      'with-leading-icon-pressed-leading-icon-color':
        'pressed-leading-icon-color',
      // Rename "leading-icon-size" to "icon-size" (b/275577569)
      'with-leading-icon-leading-icon-size': 'icon-size'
    )
  );

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      @if string.index($token, 'container-shape-') == 1 {
        // Add fallback to shorthand for logical shape properties.
        $value: var(--md-suggestion-chip-container-shape, #{$value});
      }

      $tokens: map.set(
        $tokens,
        $token,
        var(--md-suggestion-chip-#{$token}, #{$value})
      );
    }
  }

  @return $tokens;
}
