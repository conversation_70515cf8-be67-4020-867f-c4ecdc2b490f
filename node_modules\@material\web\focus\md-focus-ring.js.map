{"version": 3, "file": "md-focus-ring.js", "sourceRoot": "", "sources": ["md-focus-ring.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,SAAS,EAAC,MAAM,0BAA0B,CAAC;AACnD,OAAO,EAAC,MAAM,EAAC,MAAM,iCAAiC,CAAC;AAQvD;;;;;GAKG;AAEI,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,SAAS;;AACxB,kBAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAD5C,WAAW;IADvB,aAAa,CAAC,eAAe,CAAC;GAClB,WAAW,CAEvB", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {FocusRing} from './internal/focus-ring.js';\nimport {styles} from './internal/focus-ring-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-focus-ring': MdFocusRing;\n  }\n}\n\n/**\n * TODO(b/*********): add docs\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-focus-ring')\nexport class MdFocusRing extends FocusRing {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}