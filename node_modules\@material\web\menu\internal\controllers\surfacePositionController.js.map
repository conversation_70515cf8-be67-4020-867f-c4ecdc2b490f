{"version": 3, "file": "surfacePositionController.js", "sourceRoot": "", "sources": ["surfacePositionController.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAoBH;;GAEG;AACH,2EAA2E;AAC3E,MAAM,CAAC,MAAM,MAAM,GAAG;IACpB,SAAS,EAAE,WAAW;IACtB,OAAO,EAAE,SAAS;IAClB,WAAW,EAAE,aAAa;IAC1B,SAAS,EAAE,WAAW;CACd,CAAC;AAkGX;;;;;GAKG;AACH,MAAM,OAAO,yBAAyB;IAWpC;;;;OAIG;IACH,YACmB,IAA4B,EAC5B,aAAwD;QADxD,SAAI,GAAJ,IAAI,CAAwB;QAC5B,kBAAa,GAAb,aAAa,CAA2C;QAjB3E,8CAA8C;QACtC,0BAAqB,GAAc;YACzC,SAAS,EAAE,MAAM;SAClB,CAAC;QACF,wEAAwE;QACxE,+CAA+C;QACvC,eAAU,GAAwC;YACxD,MAAM,EAAE,KAAK;SACyB,CAAC;QAWvC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,QAAQ;QACZ,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,YAAY,EAAE,eAAe,EAC7B,aAAa,EAAE,gBAAgB,EAC/B,WAAW,EACX,OAAO,EACP,OAAO,EACP,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,GACnB,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACzB,MAAM,YAAY,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAC1D,MAAM,aAAa,GAAG,gBAAgB,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAE5D,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,OAAO;QACT,CAAC;QAED,2EAA2E;QAC3E,QAAQ;QACR,MAAM,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC;QAC3C,MAAM,iBAAiB,GAAG,MAAM,CAAC,WAAW,CAAC;QAE7C,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC1C,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;QACxB,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC7B,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QAC5B,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC;QACtB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAC/B,MAAM,iBAAiB,GAAG,GAAG,CAAC,qBAAqB,EAAE,CAAC;QACtD,GAAG,CAAC,MAAM,EAAE,CAAC;QAEb,4EAA4E;QAC5E,+CAA+C;QAC/C,MAAM,oBAAoB,GAAG,MAAM,CAAC,WAAW,GAAG,iBAAiB,CAAC,MAAM,CAAC;QAC3E,MAAM,oBAAoB,GAAG,MAAM,CAAC,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC;QAEzE,0EAA0E;QAC1E,4BAA4B;QAC5B,IAAI,CAAC,qBAAqB,GAAG;YAC3B,SAAS,EAAE,OAAO;YAClB,SAAS,EAAE,GAAG;SACf,CAAC;QAEF,6BAA6B;QAC7B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAC1B,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;QAE/B,yEAAyE;QACzE,+DAA+D;QAC/D,iDAAiD;QACjD,yEAAyE;QACzE,IACG,SAA0C,CAAC,OAAO;YACnD,SAAS,CAAC,WAAW,EACrB,CAAC;YACA,SAAkD,CAAC,WAAW,EAAE,CAAC;QACpE,CAAC;QAED,MAAM,WAAW,GAAG,SAAS,CAAC,4BAA4B;YACxD,CAAC,CAAC,SAAS,CAAC,4BAA4B,EAAE;YAC1C,CAAC,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC;QACtC,MAAM,UAAU,GAAG,QAAQ,CAAC,4BAA4B;YACtD,CAAC,CAAC,QAAQ,CAAC,4BAA4B,EAAE;YACzC,CAAC,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;QACrC,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAE5D,CAAC;QACF,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAEzD,CAAC;QAEF,8DAA8D;QAC9D,MAAM,KAAK,GACT,gBAAgB,CAAC,SAAwB,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC;QAEjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;WAqCG;QAEH,6CAA6C;QAC7C,IAAI,EAAC,UAAU,EAAE,0BAA0B,EAAE,oBAAoB,EAAC,GAChE,IAAI,CAAC,cAAc,CAAC;YAClB,WAAW;YACX,UAAU;YACV,WAAW;YACX,YAAY;YACZ,OAAO;YACP,WAAW;YACX,iBAAiB;YACjB,oBAAoB;SACrB,CAAC,CAAC;QAEL,0EAA0E;QAC1E,yDAAyD;QACzD,IAAI,0BAA0B,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpD,MAAM,mBAAmB,GAAG,YAAY,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;YACvE,MAAM,kBAAkB,GAAG,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;YAErE,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC;gBACvC,WAAW;gBACX,UAAU;gBACV,WAAW,EAAE,kBAAkB;gBAC/B,YAAY,EAAE,mBAAmB;gBACjC,OAAO;gBACP,WAAW;gBACX,iBAAiB;gBACjB,oBAAoB;aACrB,CAAC,CAAC;YAEH,uEAAuE;YACvE,kDAAkD;YAClD,IACE,0BAA0B,GAAG,YAAY,CAAC,0BAA0B,EACpE,CAAC;gBACD,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;gBACrC,0BAA0B,GAAG,YAAY,CAAC,0BAA0B,CAAC;gBACrE,oBAAoB,GAAG,YAAY,CAAC,oBAAoB,CAAC;YAC3D,CAAC;QACH,CAAC;QAED,8CAA8C;QAC9C,IAAI,EAAC,WAAW,EAAE,2BAA2B,EAAE,qBAAqB,EAAC,GACnE,IAAI,CAAC,eAAe,CAAC;YACnB,WAAW;YACX,UAAU;YACV,YAAY;YACZ,aAAa;YACb,OAAO;YACP,WAAW;YACX,KAAK;YACL,gBAAgB;YAChB,oBAAoB;SACrB,CAAC,CAAC;QAEL,2EAA2E;QAC3E,0DAA0D;QAC1D,IAAI,2BAA2B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACtD,MAAM,oBAAoB,GAAG,aAAa,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;YACzE,MAAM,mBAAmB,GAAG,YAAY,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;YAEvE,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC;gBACzC,WAAW;gBACX,UAAU;gBACV,YAAY,EAAE,mBAAmB;gBACjC,aAAa,EAAE,oBAAoB;gBACnC,OAAO;gBACP,WAAW;gBACX,KAAK;gBACL,gBAAgB;gBAChB,oBAAoB;aACrB,CAAC,CAAC;YAEH,uEAAuE;YACvE,mDAAmD;YACnD,IACE,IAAI,CAAC,GAAG,CAAC,2BAA2B,CAAC;gBACrC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,2BAA2B,CAAC,EACnD,CAAC;gBACD,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;gBACxC,2BAA2B,GAAG,aAAa,CAAC,2BAA2B,CAAC;gBACxE,qBAAqB,GAAG,aAAa,CAAC,qBAAqB,CAAC;YAC9D,CAAC;QACH,CAAC;QAED,uEAAuE;QACvE,qEAAqE;QACrE,IAAI,kBAAkB,KAAK,MAAM,EAAE,CAAC;YAClC,UAAU,GAAG,UAAU,GAAG,0BAA0B,CAAC;YACrD,WAAW,GAAG,WAAW,GAAG,2BAA2B,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,qBAAqB,GAAG;YAC3B,SAAS,EAAE,OAAO;YAClB,SAAS,EAAE,GAAG;YACd,CAAC,oBAAoB,CAAC,EAAE,GAAG,UAAU,IAAI;YACzC,CAAC,qBAAqB,CAAC,EAAE,GAAG,WAAW,IAAI;SAC5C,CAAC;QAEF,2EAA2E;QAC3E,kDAAkD;QAClD,IAAI,kBAAkB,KAAK,QAAQ,EAAE,CAAC;YACpC,0EAA0E;YAC1E,IAAI,0BAA0B,EAAE,CAAC;gBAC/B,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,GAAG,GACrC,WAAW,CAAC,MAAM,GAAG,0BACvB,IAAI,CAAC;YACP,CAAC;YAED,yEAAyE;YACzE,IAAI,2BAA2B,EAAE,CAAC;gBAChC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,GAAG,GACpC,WAAW,CAAC,KAAK,GAAG,2BACtB,IAAI,CAAC;YACP,CAAC;QACH,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACK,cAAc,CAAC,MAStB;QACC,MAAM,EACJ,WAAW,EACX,UAAU,EACV,WAAW,EACX,YAAY,EACZ,OAAO,EACP,WAAW,EACX,iBAAiB,EACjB,oBAAoB,GACrB,GAAG,MAAM,CAAC;QACX,uEAAuE;QACvE,uEAAuE;QACvE,MAAM,gBAAgB,GACpB,WAAW,KAAK,OAAO,IAAI,WAAW,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,MAAM,kBAAkB,GAAG,WAAW,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,MAAM,mBAAmB,GAAG,YAAY,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,MAAM,iBAAiB,GAAG,YAAY,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,MAAM,aAAa,GAAG,WAAW,KAAK,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3D,mDAAmD;QACnD,MAAM,iBAAiB,GAAG,aAAa,GAAG,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC;QACtE,+DAA+D;QAC/D,MAAM,mBAAmB,GACvB,mBAAmB,GAAG,UAAU,CAAC,GAAG;YACpC,iBAAiB;gBACf,CAAC,iBAAiB,GAAG,UAAU,CAAC,MAAM,GAAG,oBAAoB,CAAC,CAAC;QACnE,MAAM,mBAAmB,GACvB,mBAAmB,GAAG,MAAM,CAAC,OAAO,GAAG,iBAAiB,GAAG,MAAM,CAAC,OAAO,CAAC;QAE5E,4EAA4E;QAC5E,KAAK;QACL,MAAM,0BAA0B,GAAG,IAAI,CAAC,GAAG,CACzC,IAAI,CAAC,GAAG,CACN,CAAC,EACD,iBAAiB;YACf,mBAAmB;YACnB,iBAAiB;YACjB,WAAW,CAAC,MAAM,CACrB,CACF,CAAC;QAEF,yCAAyC;QACzC,MAAM,UAAU,GACd,gBAAgB,GAAG,mBAAmB;YACtC,kBAAkB,GAAG,mBAAmB;YACxC,iBAAiB,CAAC;QAEpB,MAAM,oBAAoB,GACxB,YAAY,KAAK,OAAO,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,iBAAiB,CAAC;QAErE,OAAO,EAAC,UAAU,EAAE,0BAA0B,EAAE,oBAAoB,EAAC,CAAC;IACxE,CAAC;IAED;;;OAGG;IACK,eAAe,CAAC,MAUvB;QACC,MAAM,EACJ,KAAK,EAAE,SAAS,EAChB,aAAa,EACb,YAAY,EACZ,UAAU,EACV,WAAW,EACX,OAAO,EACP,WAAW,EACX,gBAAgB,EAChB,oBAAoB,GACrB,GAAG,MAAM,CAAC;QACX,uEAAuE;QACvE,uEAAuE;QACvE,MAAM,gBAAgB,GACpB,WAAW,KAAK,OAAO,IAAI,WAAW,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,MAAM,kBAAkB,GAAG,WAAW,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,oBAAoB,GAAG,aAAa,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,MAAM,kBAAkB,GAAG,aAAa,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,MAAM,cAAc,GAAG,YAAY,KAAK,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9D,kDAAkD;QAClD,MAAM,kBAAkB,GAAG,cAAc,GAAG,UAAU,CAAC,KAAK,GAAG,OAAO,CAAC;QACvE,8DAA8D;QAC9D,MAAM,uBAAuB,GAC3B,oBAAoB,GAAG,UAAU,CAAC,IAAI;YACtC,kBAAkB;gBAChB,CAAC,gBAAgB,GAAG,UAAU,CAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC;QACjE,8DAA8D;QAC9D,MAAM,uBAAuB,GAC3B,oBAAoB;YAClB,CAAC,gBAAgB,GAAG,UAAU,CAAC,KAAK,GAAG,oBAAoB,CAAC;YAC9D,kBAAkB,GAAG,UAAU,CAAC,IAAI,CAAC;QACvC,uDAAuD;QACvD,MAAM,oBAAoB,GACxB,KAAK,GAAG,uBAAuB,GAAG,KAAK,GAAG,uBAAuB,CAAC;QAEpE,8DAA8D;QAC9D,MAAM,uBAAuB,GAC3B,oBAAoB,GAAG,MAAM,CAAC,OAAO;YACrC,kBAAkB,GAAG,MAAM,CAAC,OAAO,CAAC;QACtC,8DAA8D;QAC9D,MAAM,uBAAuB,GAC3B,kBAAkB,GAAG,MAAM,CAAC,OAAO;YACnC,oBAAoB,GAAG,MAAM,CAAC,OAAO,CAAC;QACxC,uDAAuD;QACvD,MAAM,oBAAoB,GACxB,KAAK,GAAG,uBAAuB,GAAG,KAAK,GAAG,uBAAuB,CAAC;QAEpE,wEAAwE;QACxE,UAAU;QACV,MAAM,2BAA2B,GAAG,IAAI,CAAC,GAAG,CAC1C,IAAI,CAAC,GAAG,CACN,CAAC,EACD,gBAAgB;YACd,oBAAoB;YACpB,kBAAkB;YAClB,WAAW,CAAC,KAAK,CACpB,CACF,CAAC;QAEF,0CAA0C;QAC1C,MAAM,WAAW,GACf,gBAAgB,GAAG,oBAAoB;YACvC,kBAAkB;YAClB,kBAAkB,GAAG,oBAAoB,CAAC;QAE5C,IAAI,qBAAqB,GACvB,aAAa,KAAK,OAAO,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,kBAAkB,CAAC;QAExE,4EAA4E;QAC5E,wDAAwD;QACxD,IAAI,WAAW,KAAK,UAAU,IAAI,WAAW,KAAK,OAAO,EAAE,CAAC;YAC1D,IACE,CAAC,aAAa,KAAK,OAAO,IAAI,SAAS,CAAC;gBACxC,CAAC,aAAa,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,EACvC,CAAC;gBACD,qBAAqB,GAAG,MAAM,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACN,qBAAqB,GAAG,OAAO,CAAC;YAClC,CAAC;QACH,CAAC;QAED,OAAO;YACL,WAAW;YACX,2BAA2B;YAC3B,qBAAqB;SACtB,CAAC;IACJ,CAAC;IAED,UAAU;QACR,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAED,WAAW;QACT,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,QAAQ;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACnC,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACjD,2BAA2B;YAC3B,UAAU,GAAG,UAAU,IAAI,KAAK,KAAM,IAAI,CAAC,UAAkB,CAAC,GAAG,CAAC,CAAC;YACnE,IAAI,UAAU;gBAAE,MAAM;QACxB,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC;QAC5D,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;QACnC,MAAM,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC;QAErC,IAAI,UAAU,IAAI,SAAS,IAAI,UAAU,EAAE,CAAC;YAC1C,sEAAsE;YACtE,sEAAsE;YACtE,mCAAmC;YACnC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAEtC,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACjB,sEAAsE;gBACtE,kBAAkB;gBAClB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;gBAExB,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACtB,KAAK,CAAC,MAAM,EAAE,CAAC;YACjB,CAAC;iBAAM,IAAI,WAAW,EAAE,CAAC;gBACvB,MAAM,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC1B,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK;QACX,IAAI,CAAC,qBAAqB,GAAG;YAC3B,SAAS,EAAE,MAAM;SAClB,CAAC;QACF,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC;QAEjD,4EAA4E;QAC5E,mBAAmB;QACnB,IACG,SAA2C,EAAE,OAAO;YACrD,SAAS,EAAE,WAAW,EACtB,CAAC;YACA,SAAkD,CAAC,WAAW,EAAE,CAAC;QACpE,CAAC;IACH,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {ReactiveController, ReactiveControllerHost} from 'lit';\nimport {StyleInfo} from 'lit/directives/style-map.js';\n\n/**\n * Declare popoverAPI functions and properties. See\n * https://developer.mozilla.org/en-US/docs/Web/API/Popover_API\n * Without this, closure will rename these functions. Can remove once these\n * functions make it into the typescript lib.\n */\ndeclare global {\n  interface HTMLElement {\n    showPopover(): void;\n    hidePopover(): void;\n    togglePopover(force: boolean): void;\n    popover: string | null;\n  }\n}\n\n/**\n * An enum of supported Menu corners\n */\n// tslint:disable-next-line:enforce-name-casing We are mimicking enum style\nexport const Corner = {\n  END_START: 'end-start',\n  END_END: 'end-end',\n  START_START: 'start-start',\n  START_END: 'start-end',\n} as const;\n\n/**\n * A corner of a box in the standard logical property style of <block>_<inline>\n */\nexport type Corner = (typeof Corner)[keyof typeof Corner];\n\n/**\n * An interface that provides a method to customize the rect from which to\n * calculate the anchor positioning. Useful for when you want a surface to\n * anchor to an element in your shadow DOM rather than the host element.\n */\nexport interface SurfacePositionTarget extends HTMLElement {\n  getSurfacePositionClientRect?: () => DOMRect;\n}\n\n/**\n * The configurable options for the surface position controller.\n */\nexport interface SurfacePositionControllerProperties {\n  /**\n   * Disable the `flip` behavior on the block axis of the surface's corner\n   */\n  disableBlockFlip: boolean;\n  /**\n   * Disable the `flip` behavior on the inline axis of the surface's corner\n   */\n  disableInlineFlip: boolean;\n  /**\n   * The corner of the anchor to align the surface's position.\n   */\n  anchorCorner: Corner;\n  /**\n   * The corner of the surface to align to the given anchor corner.\n   */\n  surfaceCorner: Corner;\n  /**\n   * The HTMLElement reference of the surface to be positioned.\n   */\n  surfaceEl: SurfacePositionTarget | null;\n  /**\n   * The HTMLElement reference of the anchor to align to.\n   */\n  anchorEl: SurfacePositionTarget | null;\n  /**\n   * Whether the positioning algorithim should calculate relative to the parent\n   * of the anchor element (absolute) or relative to the window (fixed).\n   *\n   * Examples for `position = 'fixed'`:\n   *\n   * - If there is no `position:relative` in the given parent tree and the\n   *   surface is `position:absolute`\n   * - If the surface is `position:fixed`\n   * - If the surface is in the \"top layer\"\n   * - The anchor and the surface do not share a common `position:relative`\n   *   ancestor\n   */\n  positioning: 'absolute' | 'fixed' | 'document';\n  /**\n   * Whether or not the surface should be \"open\" and visible\n   */\n  isOpen: boolean;\n  /**\n   * The number of pixels in which to offset from the inline axis relative to\n   * logical property.\n   *\n   * Positive is right in LTR and left in RTL.\n   */\n  xOffset: number;\n  /**\n   * The number of pixes in which to offset the block axis.\n   *\n   * Positive is down and negative is up.\n   */\n  yOffset: number;\n  /**\n   * The strategy to follow when repositioning the menu to stay inside the\n   * viewport. \"move\" will simply move the surface to stay in the viewport.\n   * \"resize\" will attempt to resize the surface.\n   *\n   * Both strategies will still attempt to flip the anchor and surface corners.\n   */\n  repositionStrategy: 'move' | 'resize';\n  /**\n   * A function to call after the surface has been positioned.\n   */\n  onOpen: () => void;\n  /**\n   * A function to call before the surface should be closed. (A good time to\n   * perform animations while the surface is still visible)\n   */\n  beforeClose: () => Promise<void>;\n  /**\n   * A function to call after the surface has been closed.\n   */\n  onClose: () => void;\n}\n\n/**\n * Given a surface, an anchor, corners, and some options, this surface will\n * calculate the position of a surface to align the two given corners and keep\n * the surface inside the window viewport. It also provides a StyleInfo map that\n * can be applied to the surface to handle visiblility and position.\n */\nexport class SurfacePositionController implements ReactiveController {\n  // The current styles to apply to the surface.\n  private surfaceStylesInternal: StyleInfo = {\n    'display': 'none',\n  };\n  // Previous values stored for change detection. Open change detection is\n  // calculated separately so initialize it here.\n  private lastValues: SurfacePositionControllerProperties = {\n    isOpen: false,\n  } as SurfacePositionControllerProperties;\n\n  /**\n   * @param host The host to connect the controller to.\n   * @param getProperties A function that returns the properties for the\n   * controller.\n   */\n  constructor(\n    private readonly host: ReactiveControllerHost,\n    private readonly getProperties: () => SurfacePositionControllerProperties,\n  ) {\n    this.host.addController(this);\n  }\n\n  /**\n   * The StyleInfo map to apply to the surface via Lit's stylemap\n   */\n  get surfaceStyles() {\n    return this.surfaceStylesInternal;\n  }\n\n  /**\n   * Calculates the surface's new position required so that the surface's\n   * `surfaceCorner` aligns to the anchor's `anchorCorner` while keeping the\n   * surface inside the window viewport. This positioning also respects RTL by\n   * checking `getComputedStyle()` on the surface element.\n   */\n  async position() {\n    const {\n      surfaceEl,\n      anchorEl,\n      anchorCorner: anchorCornerRaw,\n      surfaceCorner: surfaceCornerRaw,\n      positioning,\n      xOffset,\n      yOffset,\n      disableBlockFlip,\n      disableInlineFlip,\n      repositionStrategy,\n    } = this.getProperties();\n    const anchorCorner = anchorCornerRaw.toLowerCase().trim();\n    const surfaceCorner = surfaceCornerRaw.toLowerCase().trim();\n\n    if (!surfaceEl || !anchorEl) {\n      return;\n    }\n\n    // Store these before we potentially resize the window with the next set of\n    // lines\n    const windowInnerWidth = window.innerWidth;\n    const windowInnerHeight = window.innerHeight;\n\n    const div = document.createElement('div');\n    div.style.opacity = '0';\n    div.style.position = 'fixed';\n    div.style.display = 'block';\n    div.style.inset = '0';\n    document.body.appendChild(div);\n    const scrollbarTestRect = div.getBoundingClientRect();\n    div.remove();\n\n    // Calculate the widths of the scrollbars in the inline and block directions\n    // to account for window-relative calculations.\n    const blockScrollbarHeight = window.innerHeight - scrollbarTestRect.bottom;\n    const inlineScrollbarWidth = window.innerWidth - scrollbarTestRect.right;\n\n    // Paint the surface transparently so that we can get the position and the\n    // rect info of the surface.\n    this.surfaceStylesInternal = {\n      'display': 'block',\n      'opacity': '0',\n    };\n\n    // Wait for it to be visible.\n    this.host.requestUpdate();\n    await this.host.updateComplete;\n\n    // Safari has a bug that makes popovers render incorrectly if the node is\n    // made visible + Animation Frame before calling showPopover().\n    // https://bugs.webkit.org/show_bug.cgi?id=264069\n    // also the cast is required due to differing TS types in Google and OSS.\n    if (\n      (surfaceEl as unknown as {popover: string}).popover &&\n      surfaceEl.isConnected\n    ) {\n      (surfaceEl as unknown as {showPopover: () => void}).showPopover();\n    }\n\n    const surfaceRect = surfaceEl.getSurfacePositionClientRect\n      ? surfaceEl.getSurfacePositionClientRect()\n      : surfaceEl.getBoundingClientRect();\n    const anchorRect = anchorEl.getSurfacePositionClientRect\n      ? anchorEl.getSurfacePositionClientRect()\n      : anchorEl.getBoundingClientRect();\n    const [surfaceBlock, surfaceInline] = surfaceCorner.split('-') as Array<\n      'start' | 'end'\n    >;\n    const [anchorBlock, anchorInline] = anchorCorner.split('-') as Array<\n      'start' | 'end'\n    >;\n\n    // LTR depends on the direction of the SURFACE not the anchor.\n    const isLTR =\n      getComputedStyle(surfaceEl as HTMLElement).direction === 'ltr';\n\n    /*\n     * For more on inline and block dimensions, see MDN article:\n     * https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_logical_properties_and_values\n     *\n     * ┌───── inline/blockDocumentOffset  inlineScrollbarWidth\n     * │       │                                    │\n     * │     ┌─▼─────┐                              │Document\n     * │    ┌┼───────┴──────────────────────────────┼────────┐\n     * │    ││                                      │        │\n     * └──► ││ ┌───── inline/blockWindowOffset      │        │\n     *      ││ │       │                            ▼        │\n     *      ││ │     ┌─▼───┐                 Window┌┐        │\n     *      └┤ │    ┌┼─────┴───────────────────────┼│        │\n     *       │ │    ││                             ││        │\n     *       │ └──► ││  ┌──inline/blockAnchorOffset││        │\n     *       │      ││  │     │                    ││        │\n     *       │      └┤  │  ┌──▼───┐                ││        │\n     *       │       │  │ ┌┼──────┤                ││        │\n     *       │       │  └─►│Anchor│                ││        │\n     *       │       │    └┴──────┘                ││        │\n     *       │       │                             ││        │\n     *       │       │     ┌───────────────────────┼┼────┐   │\n     *       │       │     │ Surface               ││    │   │\n     *       │       │     │                       ││    │   │\n     *       │       │     │                       ││    │   │\n     *       │       │     │                       ││    │   │\n     *       │       │     │                       ││    │   │\n     *       │      ┌┼─────┼───────────────────────┼│    │   │\n     *       │   ┌─►┴──────┼────────────────────────┘    ├┐  │\n     *       │   │         │ inline/blockOOBCorrection   ││  │\n     *       │   │         │                         │   ││  │\n     *       │   │         │                         ├──►├│  │\n     *       │   │         │                         │   ││  │\n     *       │   │         └────────────────────────┐▼───┼┘  │\n     *       │  blockScrollbarHeight                └────┘   │\n     *       │                                               │\n     *       └───────────────────────────────────────────────┘\n     */\n\n    // Calculate the block positioning properties\n    let {blockInset, blockOutOfBoundsCorrection, surfaceBlockProperty} =\n      this.calculateBlock({\n        surfaceRect,\n        anchorRect,\n        anchorBlock,\n        surfaceBlock,\n        yOffset,\n        positioning,\n        windowInnerHeight,\n        blockScrollbarHeight,\n      });\n\n    // If the surface should be out of bounds in the block direction, flip the\n    // surface and anchor corner block values and recalculate\n    if (blockOutOfBoundsCorrection && !disableBlockFlip) {\n      const flippedSurfaceBlock = surfaceBlock === 'start' ? 'end' : 'start';\n      const flippedAnchorBlock = anchorBlock === 'start' ? 'end' : 'start';\n\n      const flippedBlock = this.calculateBlock({\n        surfaceRect,\n        anchorRect,\n        anchorBlock: flippedAnchorBlock,\n        surfaceBlock: flippedSurfaceBlock,\n        yOffset,\n        positioning,\n        windowInnerHeight,\n        blockScrollbarHeight,\n      });\n\n      // In the case that the flipped verion would require less out of bounds\n      // correcting, use the flipped corner block values\n      if (\n        blockOutOfBoundsCorrection > flippedBlock.blockOutOfBoundsCorrection\n      ) {\n        blockInset = flippedBlock.blockInset;\n        blockOutOfBoundsCorrection = flippedBlock.blockOutOfBoundsCorrection;\n        surfaceBlockProperty = flippedBlock.surfaceBlockProperty;\n      }\n    }\n\n    // Calculate the inline positioning properties\n    let {inlineInset, inlineOutOfBoundsCorrection, surfaceInlineProperty} =\n      this.calculateInline({\n        surfaceRect,\n        anchorRect,\n        anchorInline,\n        surfaceInline,\n        xOffset,\n        positioning,\n        isLTR,\n        windowInnerWidth,\n        inlineScrollbarWidth,\n      });\n\n    // If the surface should be out of bounds in the inline direction, flip the\n    // surface and anchor corner inline values and recalculate\n    if (inlineOutOfBoundsCorrection && !disableInlineFlip) {\n      const flippedSurfaceInline = surfaceInline === 'start' ? 'end' : 'start';\n      const flippedAnchorInline = anchorInline === 'start' ? 'end' : 'start';\n\n      const flippedInline = this.calculateInline({\n        surfaceRect,\n        anchorRect,\n        anchorInline: flippedAnchorInline,\n        surfaceInline: flippedSurfaceInline,\n        xOffset,\n        positioning,\n        isLTR,\n        windowInnerWidth,\n        inlineScrollbarWidth,\n      });\n\n      // In the case that the flipped verion would require less out of bounds\n      // correcting, use the flipped corner inline values\n      if (\n        Math.abs(inlineOutOfBoundsCorrection) >\n        Math.abs(flippedInline.inlineOutOfBoundsCorrection)\n      ) {\n        inlineInset = flippedInline.inlineInset;\n        inlineOutOfBoundsCorrection = flippedInline.inlineOutOfBoundsCorrection;\n        surfaceInlineProperty = flippedInline.surfaceInlineProperty;\n      }\n    }\n\n    // If we are simply repositioning the surface back inside the viewport,\n    // subtract the out of bounds correction values from the positioning.\n    if (repositionStrategy === 'move') {\n      blockInset = blockInset - blockOutOfBoundsCorrection;\n      inlineInset = inlineInset - inlineOutOfBoundsCorrection;\n    }\n\n    this.surfaceStylesInternal = {\n      'display': 'block',\n      'opacity': '1',\n      [surfaceBlockProperty]: `${blockInset}px`,\n      [surfaceInlineProperty]: `${inlineInset}px`,\n    };\n\n    // In the case that we are resizing the surface to stay inside the viewport\n    // we need to set height and width on the surface.\n    if (repositionStrategy === 'resize') {\n      // Add a height property to the styles if there is block height correction\n      if (blockOutOfBoundsCorrection) {\n        this.surfaceStylesInternal['height'] = `${\n          surfaceRect.height - blockOutOfBoundsCorrection\n        }px`;\n      }\n\n      // Add a width property to the styles if there is block height correction\n      if (inlineOutOfBoundsCorrection) {\n        this.surfaceStylesInternal['width'] = `${\n          surfaceRect.width - inlineOutOfBoundsCorrection\n        }px`;\n      }\n    }\n\n    this.host.requestUpdate();\n  }\n\n  /**\n   * Calculates the css property, the inset, and the out of bounds correction\n   * for the surface in the block direction.\n   */\n  private calculateBlock(config: {\n    surfaceRect: DOMRect;\n    anchorRect: DOMRect;\n    anchorBlock: 'start' | 'end';\n    surfaceBlock: 'start' | 'end';\n    yOffset: number;\n    positioning: 'absolute' | 'fixed' | 'document';\n    windowInnerHeight: number;\n    blockScrollbarHeight: number;\n  }) {\n    const {\n      surfaceRect,\n      anchorRect,\n      anchorBlock,\n      surfaceBlock,\n      yOffset,\n      positioning,\n      windowInnerHeight,\n      blockScrollbarHeight,\n    } = config;\n    // We use number booleans to multiply values rather than `if` / ternary\n    // statements because it _heavily_ cuts down on nesting and readability\n    const relativeToWindow =\n      positioning === 'fixed' || positioning === 'document' ? 1 : 0;\n    const relativeToDocument = positioning === 'document' ? 1 : 0;\n    const isSurfaceBlockStart = surfaceBlock === 'start' ? 1 : 0;\n    const isSurfaceBlockEnd = surfaceBlock === 'end' ? 1 : 0;\n    const isOneBlockEnd = anchorBlock !== surfaceBlock ? 1 : 0;\n\n    // Whether or not to apply the height of the anchor\n    const blockAnchorOffset = isOneBlockEnd * anchorRect.height + yOffset;\n    // The absolute block position of the anchor relative to window\n    const blockTopLayerOffset =\n      isSurfaceBlockStart * anchorRect.top +\n      isSurfaceBlockEnd *\n        (windowInnerHeight - anchorRect.bottom - blockScrollbarHeight);\n    const blockDocumentOffset =\n      isSurfaceBlockStart * window.scrollY - isSurfaceBlockEnd * window.scrollY;\n\n    // If the surface's block would be out of bounds of the window, move it back\n    // in\n    const blockOutOfBoundsCorrection = Math.abs(\n      Math.min(\n        0,\n        windowInnerHeight -\n          blockTopLayerOffset -\n          blockAnchorOffset -\n          surfaceRect.height,\n      ),\n    );\n\n    // The block logical value of the surface\n    const blockInset =\n      relativeToWindow * blockTopLayerOffset +\n      relativeToDocument * blockDocumentOffset +\n      blockAnchorOffset;\n\n    const surfaceBlockProperty =\n      surfaceBlock === 'start' ? 'inset-block-start' : 'inset-block-end';\n\n    return {blockInset, blockOutOfBoundsCorrection, surfaceBlockProperty};\n  }\n\n  /**\n   * Calculates the css property, the inset, and the out of bounds correction\n   * for the surface in the inline direction.\n   */\n  private calculateInline(config: {\n    isLTR: boolean;\n    surfaceInline: 'start' | 'end';\n    anchorInline: 'start' | 'end';\n    anchorRect: DOMRect;\n    surfaceRect: DOMRect;\n    xOffset: number;\n    positioning: 'absolute' | 'fixed' | 'document';\n    windowInnerWidth: number;\n    inlineScrollbarWidth: number;\n  }) {\n    const {\n      isLTR: isLTRBool,\n      surfaceInline,\n      anchorInline,\n      anchorRect,\n      surfaceRect,\n      xOffset,\n      positioning,\n      windowInnerWidth,\n      inlineScrollbarWidth,\n    } = config;\n    // We use number booleans to multiply values rather than `if` / ternary\n    // statements because it _heavily_ cuts down on nesting and readability\n    const relativeToWindow =\n      positioning === 'fixed' || positioning === 'document' ? 1 : 0;\n    const relativeToDocument = positioning === 'document' ? 1 : 0;\n    const isLTR = isLTRBool ? 1 : 0;\n    const isRTL = isLTRBool ? 0 : 1;\n    const isSurfaceInlineStart = surfaceInline === 'start' ? 1 : 0;\n    const isSurfaceInlineEnd = surfaceInline === 'end' ? 1 : 0;\n    const isOneInlineEnd = anchorInline !== surfaceInline ? 1 : 0;\n\n    // Whether or not to apply the width of the anchor\n    const inlineAnchorOffset = isOneInlineEnd * anchorRect.width + xOffset;\n    // The inline position of the anchor relative to window in LTR\n    const inlineTopLayerOffsetLTR =\n      isSurfaceInlineStart * anchorRect.left +\n      isSurfaceInlineEnd *\n        (windowInnerWidth - anchorRect.right - inlineScrollbarWidth);\n    // The inline position of the anchor relative to window in RTL\n    const inlineTopLayerOffsetRTL =\n      isSurfaceInlineStart *\n        (windowInnerWidth - anchorRect.right - inlineScrollbarWidth) +\n      isSurfaceInlineEnd * anchorRect.left;\n    // The inline position of the anchor relative to window\n    const inlineTopLayerOffset =\n      isLTR * inlineTopLayerOffsetLTR + isRTL * inlineTopLayerOffsetRTL;\n\n    // The inline position of the anchor relative to window in LTR\n    const inlineDocumentOffsetLTR =\n      isSurfaceInlineStart * window.scrollX -\n      isSurfaceInlineEnd * window.scrollX;\n    // The inline position of the anchor relative to window in RTL\n    const inlineDocumentOffsetRTL =\n      isSurfaceInlineEnd * window.scrollX -\n      isSurfaceInlineStart * window.scrollX;\n    // The inline position of the anchor relative to window\n    const inlineDocumentOffset =\n      isLTR * inlineDocumentOffsetLTR + isRTL * inlineDocumentOffsetRTL;\n\n    // If the surface's inline would be out of bounds of the window, move it\n    // back in\n    const inlineOutOfBoundsCorrection = Math.abs(\n      Math.min(\n        0,\n        windowInnerWidth -\n          inlineTopLayerOffset -\n          inlineAnchorOffset -\n          surfaceRect.width,\n      ),\n    );\n\n    // The inline logical value of the surface\n    const inlineInset =\n      relativeToWindow * inlineTopLayerOffset +\n      inlineAnchorOffset +\n      relativeToDocument * inlineDocumentOffset;\n\n    let surfaceInlineProperty =\n      surfaceInline === 'start' ? 'inset-inline-start' : 'inset-inline-end';\n\n    // There are cases where the element is RTL but the root of the page is not.\n    // In these cases we want to not use logical properties.\n    if (positioning === 'document' || positioning === 'fixed') {\n      if (\n        (surfaceInline === 'start' && isLTRBool) ||\n        (surfaceInline === 'end' && !isLTRBool)\n      ) {\n        surfaceInlineProperty = 'left';\n      } else {\n        surfaceInlineProperty = 'right';\n      }\n    }\n\n    return {\n      inlineInset,\n      inlineOutOfBoundsCorrection,\n      surfaceInlineProperty,\n    };\n  }\n\n  hostUpdate() {\n    this.onUpdate();\n  }\n\n  hostUpdated() {\n    this.onUpdate();\n  }\n\n  /**\n   * Checks whether the properties passed into the controller have changed since\n   * the last positioning. If so, it will reposition if the surface is open or\n   * close it if the surface should close.\n   */\n  private async onUpdate() {\n    const props = this.getProperties();\n    let hasChanged = false;\n    for (const [key, value] of Object.entries(props)) {\n      // tslint:disable-next-line\n      hasChanged = hasChanged || value !== (this.lastValues as any)[key];\n      if (hasChanged) break;\n    }\n\n    const openChanged = this.lastValues.isOpen !== props.isOpen;\n    const hasAnchor = !!props.anchorEl;\n    const hasSurface = !!props.surfaceEl;\n\n    if (hasChanged && hasAnchor && hasSurface) {\n      // Only update isOpen, because if it's closed, we do not want to waste\n      // time on a useless reposition calculation. So save the other \"dirty\"\n      // values until next time it opens.\n      this.lastValues.isOpen = props.isOpen;\n\n      if (props.isOpen) {\n        // We are going to do a reposition, so save the prop values for future\n        // dirty checking.\n        this.lastValues = props;\n\n        await this.position();\n        props.onOpen();\n      } else if (openChanged) {\n        await props.beforeClose();\n        this.close();\n        props.onClose();\n      }\n    }\n  }\n\n  /**\n   * Hides the surface.\n   */\n  private close() {\n    this.surfaceStylesInternal = {\n      'display': 'none',\n    };\n    this.host.requestUpdate();\n    const surfaceEl = this.getProperties().surfaceEl;\n\n    // The following type casts are required due to differing TS types in Google\n    // and open source.\n    if (\n      (surfaceEl as unknown as {popover?: string})?.popover &&\n      surfaceEl?.isConnected\n    ) {\n      (surfaceEl as unknown as {hidePopover: () => void}).hidePopover();\n    }\n  }\n}\n"]}