{"version": 3, "file": "list.js", "sourceRoot": "", "sources": ["list.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,IAAI,EAAC,MAAM,oBAAoB,CAAC;AACxC,OAAO,EAAC,MAAM,EAAC,MAAM,2BAA2B,CAAC;AAQjD;;;;;;;;;;;;;;;;;;GAkBG;AAEI,IAAM,MAAM,GAAZ,MAAM,MAAO,SAAQ,IAAI;;AACd,aAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAD5C,MAAM;IADlB,aAAa,CAAC,SAAS,CAAC;GACZ,MAAM,CAElB", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {List} from './internal/list.js';\nimport {styles} from './internal/list-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-list': MdList;\n  }\n}\n\n/**\n * @summary Lists are continuous, vertical indexes of text or images.\n *\n * @description\n * Lists consist of one or more list items, and can contain actions represented\n * by icons and text. List items come in three sizes: one-line, two-line, and\n * three-line.\n *\n * __Takeaways:__\n *\n * - Lists should be sorted in logical ways that make content easy to scan, such\n *   as alphabetical, numerical, chronological, or by user preference.\n * - Lists present content in a way that makes it easy to identify a specific\n *   item in a collection and act on it.\n * - Lists should present icons, text, and actions in a consistent format.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-list')\nexport class MdList extends List {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}