{"version": 3, "file": "navigation-bar.js", "sourceRoot": "", "sources": ["navigation-bar.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,aAAa,EAAC,MAAM,8BAA8B,CAAC;AAC3D,OAAO,EAAC,MAAM,EAAC,MAAM,qCAAqC,CAAC;AAQ3D;;;GAGG;AAEI,IAAM,eAAe,GAArB,MAAM,eAAgB,SAAQ,aAAa;;AAChC,sBAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAD5C,eAAe;IAD3B,aAAa,CAAC,mBAAmB,CAAC;GACtB,eAAe,CAE3B", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {NavigationBar} from './internal/navigation-bar.js';\nimport {styles} from './internal/navigation-bar-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-navigation-bar': MdNavigationBar;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-navigation-bar')\nexport class MdNavigationBar extends NavigationBar {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}