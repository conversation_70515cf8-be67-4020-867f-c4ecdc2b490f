{"version": 3, "file": "outlined-button.js", "sourceRoot": "", "sources": ["outlined-button.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,cAAc,EAAC,MAAM,+BAA+B,CAAC;AAC7D,OAAO,EAAC,MAAM,IAAI,cAAc,EAAC,MAAM,+BAA+B,CAAC;AACvE,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAQnE;;;;;;;;;;;;;;;;;;;;;GAqBG;AAEI,IAAM,gBAAgB,GAAtB,MAAM,gBAAiB,SAAQ,cAAc;;AAClC,uBAAM,GAAwB,CAAC,YAAY,EAAE,cAAc,CAAC,AAAtD,CAAuD;AADlE,gBAAgB;IAD5B,aAAa,CAAC,oBAAoB,CAAC;GACvB,gBAAgB,CAE5B", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {OutlinedButton} from './internal/outlined-button.js';\nimport {styles as outlinedStyles} from './internal/outlined-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-outlined-button': MdOutlinedButton;\n  }\n}\n\n/**\n * @summary Buttons help people take action, such as sending an email, sharing a\n * document, or liking a comment.\n *\n * @description\n * __Emphasis:__ Medium emphasis – For important actions that don’t distract\n * from other onscreen elements.\n *\n * __Rationale:__ Use an outlined button for actions that need attention but\n * aren’t the primary action, such as “See all” or “Add to cart.” This is also\n * the button to use for giving someone the opportunity to change their mind or\n * escape a flow.\n *\n * __Example usages:__\n * - Reply\n * - View all\n * - Add to cart\n * - Take out of trash\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-outlined-button')\nexport class MdOutlinedButton extends OutlinedButton {\n  static override styles: CSSResultOrNative[] = [sharedStyles, outlinedStyles];\n}\n"]}