{"version": 3, "file": "icon.js", "sourceRoot": "", "sources": ["icon.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,IAAI,EAAE,UAAU,EAAC,MAAM,KAAK,CAAC;AAErC;;GAEG;AACH,MAAM,OAAO,IAAK,SAAQ,UAAU;IACf,MAAM;QACvB,OAAO,IAAI,CAAA,eAAe,CAAC;IAC7B,CAAC;IAEQ,iBAAiB;QACxB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QACpD,IAAI,UAAU,KAAK,OAAO,EAAE,CAAC;YAC3B,wEAAwE;YACxE,8BAA8B;YAC9B,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YACpC,OAAO;QACT,CAAC;QAED,wEAAwE;QACxE,4BAA4B;QAC5B,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, LitElement} from 'lit';\n\n/**\n * TODO(b/265336902): add docs\n */\nexport class Icon extends LitElement {\n  protected override render() {\n    return html`<slot></slot>`;\n  }\n\n  override connectedCallback() {\n    super.connectedCallback();\n    const ariaHidden = this.getAttribute('aria-hidden');\n    if (ariaHidden === 'false') {\n      // Allow the user to set `aria-hidden=\"false\"` to create an icon that is\n      // announced by screenreaders.\n      this.removeAttribute('aria-hidden');\n      return;\n    }\n\n    // Needed for VoiceOver, which will create a \"group\" if the element is a\n    // sibling to other content.\n    this.setAttribute('aria-hidden', 'true');\n  }\n}\n"]}