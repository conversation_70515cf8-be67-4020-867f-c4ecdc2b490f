{"version": 3, "file": "form-label-activation.js", "sourceRoot": "", "sources": ["form-label-activation.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,uBAAuB,CAAC,OAAoB;IAC1D,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,OAAO,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;IACvD,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC7B,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,MAAM,UAAU,iBAAiB,CAAC,KAAY;IAC5C,wCAAwC;IACxC,IAAI,KAAK,CAAC,aAAa,KAAK,KAAK,CAAC,MAAM,EAAE,CAAC;QACzC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,gDAAgD;IAChD,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,MAAM,EAAE,CAAC;QAC7C,OAAO,KAAK,CAAC;IACf,CAAC;IACD,0EAA0E;IAC1E,oBAAoB;IACpB,IAAK,KAAK,CAAC,MAA4C,CAAC,QAAQ,EAAE,CAAC;QACjE,OAAO,KAAK,CAAC;IACf,CAAC;IACD,8DAA8D;IAC9D,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC;AAED,6DAA6D;AAC7D,yCAAyC;AACzC,SAAS,YAAY,CAAC,KAAY;IAChC,MAAM,SAAS,GAAG,kBAAkB,CAAC;IACrC,IAAI,SAAS,EAAE,CAAC;QACd,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,KAAK,CAAC,wBAAwB,EAAE,CAAC;IACnC,CAAC;IACD,yBAAyB,EAAE,CAAC;IAC5B,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,wCAAwC;AACxC,IAAI,kBAAkB,GAAG,KAAK,CAAC;AAC/B,KAAK,UAAU,yBAAyB;IACtC,kBAAkB,GAAG,IAAI,CAAC;IAC1B,wCAAwC;IACxC,2BAA2B;IAC3B,MAAM,IAAI,CAAC;IACX,kBAAkB,GAAG,KAAK,CAAC;AAC7B,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * Dispatches a click event to the given element that triggers a native action,\n * but is not composed and therefore is not seen outside the element.\n *\n * This is useful for responding to an external click event on the host element\n * that should trigger an internal action like a button click.\n *\n * Note, a helper is provided because setting this up correctly is a bit tricky.\n * In particular, calling `click` on an element creates a composed event, which\n * is not desirable, and a manually dispatched event must specifically be a\n * `MouseEvent` to trigger a native action.\n *\n * @example\n * hostClickListener = (event: MouseEvent) {\n *   if (isActivationClick(event)) {\n *     this.dispatchActivationClick(this.buttonElement);\n *   }\n * }\n *\n */\nexport function dispatchActivationClick(element: HTMLElement) {\n  const event = new MouseEvent('click', {bubbles: true});\n  element.dispatchEvent(event);\n  return event;\n}\n\n/**\n * Returns true if the click event should trigger an activation behavior. The\n * behavior is defined by the element and is whatever it should do when\n * clicked.\n *\n * Typically when an element needs to handle a click, the click is generated\n * from within the element and an event listener within the element implements\n * the needed behavior; however, it's possible to fire a click directly\n * at the element that the element should handle. This method helps\n * distinguish these \"external\" clicks.\n *\n * An \"external\" click can be triggered in a number of ways: via a click\n * on an associated label for a form  associated element, calling\n * `element.click()`, or calling\n * `element.dispatchEvent(new MouseEvent('click', ...))`.\n *\n * Also works around Firefox issue\n * https://bugzilla.mozilla.org/show_bug.cgi?id=1804576 by squelching\n * events for a microtask after called.\n *\n * @example\n * hostClickListener = (event: MouseEvent) {\n *   if (isActivationClick(event)) {\n *     this.dispatchActivationClick(this.buttonElement);\n *   }\n * }\n *\n */\nexport function isActivationClick(event: Event) {\n  // Event must start at the event target.\n  if (event.currentTarget !== event.target) {\n    return false;\n  }\n  // Event must not be retargeted from shadowRoot.\n  if (event.composedPath()[0] !== event.target) {\n    return false;\n  }\n  // Target must not be disabled; this should only occur for a synthetically\n  // dispatched click.\n  if ((event.target as EventTarget & {disabled: boolean}).disabled) {\n    return false;\n  }\n  // This is an activation if the event should not be squelched.\n  return !squelchEvent(event);\n}\n\n// TODO(https://bugzilla.mozilla.org/show_bug.cgi?id=1804576)\n//  Remove when Firefox bug is addressed.\nfunction squelchEvent(event: Event) {\n  const squelched = isSquelchingEvents;\n  if (squelched) {\n    event.preventDefault();\n    event.stopImmediatePropagation();\n  }\n  squelchEventsForMicrotask();\n  return squelched;\n}\n\n// Ignore events for one microtask only.\nlet isSquelchingEvents = false;\nasync function squelchEventsForMicrotask() {\n  isSquelchingEvents = true;\n  // Need to pause for just one microtask.\n  // tslint:disable-next-line\n  await null;\n  isSquelchingEvents = false;\n}\n"]}