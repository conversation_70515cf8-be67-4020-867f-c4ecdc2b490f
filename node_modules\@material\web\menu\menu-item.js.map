{"version": 3, "file": "menu-item.js", "sourceRoot": "", "sources": ["menu-item.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,UAAU,EAAC,MAAM,kCAAkC,CAAC;AAC5D,OAAO,EAAC,MAAM,EAAC,MAAM,yCAAyC,CAAC;AAW/D;;;;;;;;;;;;;;GAcG;AAEI,IAAM,UAAU,GAAhB,MAAM,UAAW,SAAQ,UAAU;;AACxB,iBAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAD5C,UAAU;IADtB,aAAa,CAAC,cAAc,CAAC;GACjB,UAAU,CAEtB", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {MenuItemEl} from './internal/menuitem/menu-item.js';\nimport {styles} from './internal/menuitem/menu-item-styles.js';\n\nexport {type MenuItem} from './internal/controllers/menuItemController.js';\nexport {type CloseMenuEvent} from './internal/controllers/shared.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-menu-item': MdMenuItem;\n  }\n}\n\n/**\n * @summary Menus display a list of choices on a temporary surface.\n *\n * @description\n * Menu items are the selectable choices within the menu. Menu items must\n * implement the `MenuItem` interface and also have the `md-menu-item`\n * attribute. Additionally menu items are list items so they must also have the\n * `md-list-item` attribute.\n *\n * Menu items can control a menu by selectively firing the `close-menu` and\n * `deselect-items` events.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-menu-item')\nexport class MdMenuItem extends MenuItemEl {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}