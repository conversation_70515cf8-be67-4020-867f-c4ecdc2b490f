//
// Copyright 2022 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// stylelint-disable selector-class-pattern --
// Selector '.md3-*' should only be used in this project.

// go/keep-sorted start
@use '../../../tokens';
// go/keep-sorted end

@mixin theme($tokens) {
  // $supported-tokens: tokens.$md-comp-badge-supported-tokens;

  @each $token, $value in $tokens {
    // @if list.index($supported-tokens, $token) == null {
    //   @error 'Token `#{$token}` is not a supported token.';
    // }

    @if $value {
      --md-badge-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $tokens: tokens.md-comp-badge-values();

  :host {
    @each $token, $value in $tokens {
      --_#{$token}: #{$value};
    }
  }

  .md3-badge {
    inset-inline-start: 50%;
    margin-inline-start: 6px;
    margin-block-start: 4px;
    position: absolute;
    inset-block-start: 0px;
    background-color: var(--_color);
    border-radius: var(--_shape);
    height: var(--_size);

    &:not(.md3-badge--large) {
      // Separate selector since large uses min-width
      width: var(--_size);
    }

    &.md3-badge--large {
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-inline-start: 2px;
      margin-block-start: 1px;
      background-color: var(--_large-color);
      border-radius: var(--_large-shape);
      height: var(--_large-size);
      min-width: var(--_large-size);
      color: var(--_large-label-text-color);

      .md3-badge__value {
        padding: 0px 4px;
        text-align: center;
      }
    }
  }

  .md3-badge__value {
    font-family: var(--_large-label-text-font);
    font-size: var(--_large-label-text-size);
    line-height: var(--_large-label-text-line-height);
    font-weight: var(--_large-label-text-weight);
  }
}
