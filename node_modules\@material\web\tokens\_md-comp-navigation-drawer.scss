//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use './md-sys-color';
@use './md-sys-elevation';
@use './md-sys-shape';
@use './md-sys-state';
@use './md-sys-typescale';
@use './v0_192/md-comp-navigation-drawer';
// go/keep-sorted end

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $tokens: md-comp-navigation-drawer.values($deps, $exclude-hardcoded-values);

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      $tokens: map.set(
        $tokens,
        $token,
        var(--md-navigation-drawer-#{$token}, #{$value})
      );
    }
  }

  @return $tokens;
}
