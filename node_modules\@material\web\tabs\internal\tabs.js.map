{"version": 3, "file": "tabs.js", "sourceRoot": "", "sources": ["tabs.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,0BAA0B,CAAC;AAElC,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAC,MAAM,KAAK,CAAC;AAC/C,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAE,qBAAqB,EAAC,MAAM,mBAAmB,CAAC;AAEzE,OAAO,EAAC,iBAAiB,EAAM,MAAM,UAAU,CAAC;AAEhD;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,MAAM,OAAO,IAAK,SAAQ,UAAU;IAOlC;;;;OAIG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IACrD,CAAC;IACD,IAAI,SAAS,CAAC,GAAe;QAC3B,2EAA2E;QAC3E,oBAAoB;QACpB,IAAI,GAAG,EAAE,CAAC;YACR,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;IAED;;;;OAIG;IAEH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAClD,CAAC;IACD,IAAI,cAAc,CAAC,KAAa;QAC9B,MAAM,kBAAkB,GAAG,GAAG,EAAE;YAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7B,+BAA+B;YAC/B,IAAI,GAAG,EAAE,CAAC;gBACR,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YACxB,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,0EAA0E;YAC1E,WAAW;YACX,EAAE;YACF,QAAQ;YACR,QAAQ;YACR,mCAAmC;YACnC,6BAA6B;YAC7B,8BAA8B;YAC9B,eAAe;YACf,KAAK;YACL,MAAM;YACN,EAAE;YACF,uEAAuE;YACvE,wDAAwD;YACxD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC7C,OAAO;QACT,CAAC;QAED,kBAAkB,EAAE,CAAC;IACvB,CAAC;IAUD,IAAY,UAAU;QACpB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;IAC/D,CAAC;IAMD;QACE,KAAK,EAAE,CAAC;QAjBV;;WAEG;QACoD,iBAAY,GAAG,KAAK,CAAC;QAS3D,cAAS;QACxB,0BAA0B;QACzB,IAAoB,CAAC,eAAe,EAAE,CAAC;QAIxC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC;YAChC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAChE,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,WAAW,CAAC,aAA0B;QAC1C,MAAM,IAAI,CAAC,cAAc,CAAC;QAC1B,MAAM,EAAC,IAAI,EAAC,GAAG,IAAI,CAAC;QACpB,aAAa,KAAK,IAAI,CAAC,SAAS,CAAC;QACjC,IACE,CAAC,aAAa;YACd,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;YAC7B,CAAC,IAAI,CAAC,mBAAmB,EACzB,CAAC;YACD,OAAO;QACT,CAAC;QAED,2BAA2B;QAC3B,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAC5B,MAAM,GAAG,CAAC,cAAc,CAAC;QAC3B,CAAC;QAED,MAAM,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC;QACxC,MAAM,MAAM,GAAG,aAAa,CAAC,WAAW,CAAC;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QACpC,MAAM,YAAY,GAAG,EAAE,CAAC;QACxB,MAAM,GAAG,GAAG,MAAM,GAAG,YAAY,CAAC;QAClC,MAAM,GAAG,GAAG,MAAM,GAAG,MAAM,GAAG,UAAU,GAAG,YAAY,CAAC;QACxD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;QAChD,0EAA0E;QAC1E,yEAAyE;QACzE,4EAA4E;QAC5E,iBAAiB;QACjB,MAAM,QAAQ,GAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;QACvE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAC,CAAC,CAAC;IAClE,CAAC;IAEkB,MAAM;QACvB,OAAO,IAAI,CAAA;;;wBAGS,IAAI,CAAC,gBAAgB;mBAC1B,IAAI,CAAC,cAAc;;;KAGjC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,KAAY;QACvC,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QACzB,wBAAwB;QACxB,MAAM,CAAC,CAAC;QACR,IAAI,KAAK,CAAC,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACxD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IACxB,CAAC;IAEO,WAAW,CAAC,SAAc;QAChC,MAAM,EAAC,IAAI,EAAC,GAAG,IAAI,CAAC;QACpB,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YAC3D,iEAAiE;YACjE,OAAO;QACT,CAAC;QAED,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,GAAG,CAAC,MAAM,GAAG,GAAG,KAAK,SAAS,CAAC;QACjC,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YAChB,0EAA0E;YAC1E,kEAAkE;YAClE,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,aAAa,CAC1C,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAC,CAAC,CACvD,CAAC;YACF,IAAI,gBAAgB,EAAE,CAAC;gBACrB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;oBACvB,GAAG,CAAC,MAAM,GAAG,GAAG,KAAK,WAAW,CAAC;gBACnC,CAAC;gBACD,OAAO;YACT,CAAC;YAED,SAAS,CAAC,iBAAiB,CAAC,CAAC,WAAW,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IAC9B,CAAC;IAEO,kBAAkB,CAAC,YAAiB;QAC1C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAC5B,GAAG,CAAC,QAAQ,GAAG,GAAG,KAAK,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,iDAAiD;IACzC,KAAK,CAAC,aAAa,CAAC,KAAoB;QAC9C,yBAAyB;QACzB,MAAM,CAAC,CAAC;QACR,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,KAAK,WAAW,CAAC;QACzC,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,KAAK,YAAY,CAAC;QAC3C,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,KAAK,MAAM,CAAC;QACpC,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC;QAClC,6BAA6B;QAC7B,IAAI,KAAK,CAAC,gBAAgB,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACzE,OAAO;QACT,CAAC;QAED,MAAM,EAAC,IAAI,EAAC,GAAG,IAAI,CAAC;QACpB,uDAAuD;QACvD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,mDAAmD;QACnD,KAAK,CAAC,cAAc,EAAE,CAAC;QAEvB,IAAI,YAAoB,CAAC;QACzB,IAAI,MAAM,IAAI,KAAK,EAAE,CAAC;YACpB,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,wCAAwC;YACxC,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC;YACzD,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;YAC1C,MAAM,EAAC,UAAU,EAAC,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,sEAAsE;gBACtE,0CAA0C;gBAC1C,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;YAChD,CAAC;iBAAM,CAAC;gBACN,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACnD,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC;gBAC9D,IAAI,YAAY,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;oBAChC,gDAAgD;oBAChD,YAAY,GAAG,CAAC,CAAC;gBACnB,CAAC;qBAAM,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;oBAC5B,6CAA6C;oBAC7C,YAAY,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;QACtC,UAAU,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,2BAA2B;IACnB,WAAW;QACjB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;IACtD,CAAC;IAEO,cAAc;QACpB,4DAA4D;QAC5D,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;QAED,MAAM,EAAC,SAAS,EAAC,GAAG,IAAI,CAAC;QACzB,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAEO,gBAAgB;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,QAAQ,EAAE,CAAC;YAChC,yEAAyE;YACzE,uDAAuD;YACvD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC;QAED,yEAAyE;QACzE,4EAA4E;QAC5E,iCAAiC;QACjC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;CACF;AA/QU;IADR,qBAAqB,CAAC,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAC,CAAC;kCACvC;AAwBtB;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,kBAAkB,EAAC,CAAC;0CAGvD;AAmCsD;IAAtD,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,eAAe,EAAC,CAAC;0CAAsB;AAE3C;IAAhC,KAAK,CAAC,OAAO,CAAC;iDAA2D;AAC1C;IAA/B,KAAK,CAAC,MAAM,CAAC;yCAAuD;AAiNvE,SAAS,KAAK,CAAC,OAAgB;IAC7B,OAAO,OAAO,YAAY,WAAW,IAAI,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC1E,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../divider/divider.js';\n\nimport {html, isServer, LitElement} from 'lit';\nimport {property, query, queryAssignedElements} from 'lit/decorators.js';\n\nimport {ANIMATE_INDICATOR, Tab} from './tab.js';\n\n/**\n * @fires change {Event} Fired when the selected tab changes. The target's\n * `activeTabIndex` or `activeTab` provide information about the selection\n * change. The change event is fired when a user interaction like a space/enter\n * key or click cause a selection change. The tab selection based on these\n * actions can be cancelled by calling preventDefault on the triggering\n * `keydown` or `click` event. --bubbles\n *\n * @example\n * // perform an action if a tab is clicked\n * tabs.addEventListener('change', (event: Event) => {\n *   if (event.target.activeTabIndex === 2)\n *     takeAction();\n *   }\n * });\n *\n * // prevent a click from triggering tab selection under some condition\n * tabs.addEventListener('click', (event: Event) => {\n *   if (notReady)\n *     event.preventDefault();\n *   }\n * });\n *\n */\nexport class Tabs extends LitElement {\n  /**\n   * The tabs of this tab bar.\n   */\n  @queryAssignedElements({flatten: true, selector: '[md-tab]'})\n  readonly tabs!: Tab[];\n\n  /**\n   * The currently selected tab, `null` only when there are no tab children.\n   *\n   * @export\n   */\n  get activeTab() {\n    return this.tabs.find((tab) => tab.active) ?? null;\n  }\n  set activeTab(tab: Tab | null) {\n    // Ignore setting activeTab to null. As long as there are children, one tab\n    // must be selected.\n    if (tab) {\n      this.activateTab(tab);\n    }\n  }\n\n  /**\n   * The index of the currently selected tab.\n   *\n   * @export\n   */\n  @property({type: Number, attribute: 'active-tab-index'})\n  get activeTabIndex() {\n    return this.tabs.findIndex((tab) => tab.active);\n  }\n  set activeTabIndex(index: number) {\n    const activateTabAtIndex = () => {\n      const tab = this.tabs[index];\n      // Ignore out-of-bound indices.\n      if (tab) {\n        this.activateTab(tab);\n      }\n    };\n\n    if (!this.slotElement) {\n      // This is needed to support setting the activeTabIndex via a lit property\n      // binding.\n      //\n      // ```ts\n      // html`\n      //   <md-tabs .activeTabIndex=${1}>\n      //     <md-tab>First</md-tab>\n      //     <md-tab>Second</md-tab>\n      //   </md-tabs>\n      // `;\n      // ```\n      //\n      // It's needed since lit's rendering lifecycle is asynchronous, and the\n      // `<slot>` element hasn't rendered, so `tabs` is empty.\n      this.updateComplete.then(activateTabAtIndex);\n      return;\n    }\n\n    activateTabAtIndex();\n  }\n\n  /**\n   * Whether or not to automatically select a tab when it is focused.\n   */\n  @property({type: Boolean, attribute: 'auto-activate'}) autoActivate = false;\n\n  @query('.tabs') private readonly tabsScrollerElement!: HTMLElement | null;\n  @query('slot') private readonly slotElement!: HTMLSlotElement | null;\n\n  private get focusedTab() {\n    return this.tabs.find((tab) => tab.matches(':focus-within'));\n  }\n\n  private readonly internals =\n    // Cast needed for closure\n    (this as HTMLElement).attachInternals();\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.internals.role = 'tablist';\n      this.addEventListener('keydown', this.handleKeydown.bind(this));\n      this.addEventListener('keyup', this.handleKeyup.bind(this));\n      this.addEventListener('focusout', this.handleFocusout.bind(this));\n    }\n  }\n\n  /**\n   * Scrolls the toolbar, if overflowing, to the active tab, or the provided\n   * tab.\n   *\n   * @param tabToScrollTo The tab that should be scrolled to. Defaults to the\n   *     active tab.\n   * @return A Promise that resolves after the tab has been scrolled to.\n   */\n  async scrollToTab(tabToScrollTo?: Tab | null) {\n    await this.updateComplete;\n    const {tabs} = this;\n    tabToScrollTo ??= this.activeTab;\n    if (\n      !tabToScrollTo ||\n      !tabs.includes(tabToScrollTo) ||\n      !this.tabsScrollerElement\n    ) {\n      return;\n    }\n\n    // wait for tabs to render.\n    for (const tab of this.tabs) {\n      await tab.updateComplete;\n    }\n\n    const offset = tabToScrollTo.offsetLeft;\n    const extent = tabToScrollTo.offsetWidth;\n    const scroll = this.scrollLeft;\n    const hostExtent = this.offsetWidth;\n    const scrollMargin = 48;\n    const min = offset - scrollMargin;\n    const max = offset + extent - hostExtent + scrollMargin;\n    const to = Math.min(min, Math.max(max, scroll));\n    // When a tab is focused, use 'auto' to use the CSS `scroll-behavior`. The\n    // default behavior is smooth scrolling. However, when there is not a tab\n    // focused on initialization, use 'instant' to immediately bring the focused\n    // tab into view.\n    const behavior: ScrollBehavior = !this.focusedTab ? 'instant' : 'auto';\n    this.tabsScrollerElement.scrollTo({behavior, top: 0, left: to});\n  }\n\n  protected override render() {\n    return html`\n      <div class=\"tabs\">\n        <slot\n          @slotchange=${this.handleSlotChange}\n          @click=${this.handleTabClick}></slot>\n      </div>\n      <md-divider part=\"divider\"></md-divider>\n    `;\n  }\n\n  private async handleTabClick(event: Event) {\n    const tab = event.target;\n    // Allow event to bubble\n    await 0;\n    if (event.defaultPrevented || !isTab(tab) || tab.active) {\n      return;\n    }\n\n    this.activateTab(tab);\n  }\n\n  private activateTab(activeTab: Tab) {\n    const {tabs} = this;\n    const previousTab = this.activeTab;\n    if (!tabs.includes(activeTab) || previousTab === activeTab) {\n      // Ignore setting activeTab to a tab element that is not a child.\n      return;\n    }\n\n    for (const tab of tabs) {\n      tab.active = tab === activeTab;\n    }\n\n    if (previousTab) {\n      // Don't dispatch a change event if activating a tab when no previous tabs\n      // were selected, such as when md-tabs auto-selects the first tab.\n      const defaultPrevented = !this.dispatchEvent(\n        new Event('change', {bubbles: true, cancelable: true}),\n      );\n      if (defaultPrevented) {\n        for (const tab of tabs) {\n          tab.active = tab === previousTab;\n        }\n        return;\n      }\n\n      activeTab[ANIMATE_INDICATOR](previousTab);\n    }\n\n    this.updateFocusableTab(activeTab);\n    this.scrollToTab(activeTab);\n  }\n\n  private updateFocusableTab(focusableTab: Tab) {\n    for (const tab of this.tabs) {\n      tab.tabIndex = tab === focusableTab ? 0 : -1;\n    }\n  }\n\n  // focus item on keydown and optionally select it\n  private async handleKeydown(event: KeyboardEvent) {\n    // Allow event to bubble.\n    await 0;\n    const isLeft = event.key === 'ArrowLeft';\n    const isRight = event.key === 'ArrowRight';\n    const isHome = event.key === 'Home';\n    const isEnd = event.key === 'End';\n    // Ignore non-navigation keys\n    if (event.defaultPrevented || (!isLeft && !isRight && !isHome && !isEnd)) {\n      return;\n    }\n\n    const {tabs} = this;\n    // Don't try to select another tab if there aren't any.\n    if (tabs.length < 2) {\n      return;\n    }\n\n    // Prevent default interactions, such as scrolling.\n    event.preventDefault();\n\n    let indexToFocus: number;\n    if (isHome || isEnd) {\n      indexToFocus = isHome ? 0 : tabs.length - 1;\n    } else {\n      // Check if moving forwards or backwards\n      const isRtl = getComputedStyle(this).direction === 'rtl';\n      const forwards = isRtl ? isLeft : isRight;\n      const {focusedTab} = this;\n      if (!focusedTab) {\n        // If there is not already a tab focused, select the first or last tab\n        // based on the direction we're traveling.\n        indexToFocus = forwards ? 0 : tabs.length - 1;\n      } else {\n        const focusedIndex = this.tabs.indexOf(focusedTab);\n        indexToFocus = forwards ? focusedIndex + 1 : focusedIndex - 1;\n        if (indexToFocus >= tabs.length) {\n          // Return to start if moving past the last item.\n          indexToFocus = 0;\n        } else if (indexToFocus < 0) {\n          // Go to end if moving before the first item.\n          indexToFocus = tabs.length - 1;\n        }\n      }\n    }\n\n    const tabToFocus = tabs[indexToFocus];\n    tabToFocus.focus();\n    if (this.autoActivate) {\n      this.activateTab(tabToFocus);\n    } else {\n      this.updateFocusableTab(tabToFocus);\n    }\n  }\n\n  // scroll to item on keyup.\n  private handleKeyup() {\n    this.scrollToTab(this.focusedTab ?? this.activeTab);\n  }\n\n  private handleFocusout() {\n    // restore focus to selected item when blurring the tab bar.\n    if (this.matches(':focus-within')) {\n      return;\n    }\n\n    const {activeTab} = this;\n    if (activeTab) {\n      this.updateFocusableTab(activeTab);\n    }\n  }\n\n  private handleSlotChange() {\n    const firstTab = this.tabs[0];\n    if (!this.activeTab && firstTab) {\n      // If the active tab was removed, auto-select the first one. There should\n      // always be a selected tab while the bar has children.\n      this.activateTab(firstTab);\n    }\n\n    // When children shift, ensure the active tab is visible. For example, if\n    // many children are added before the active tab, it'd be pushed off screen.\n    // This ensures it stays visible.\n    this.scrollToTab(this.activeTab);\n  }\n}\n\nfunction isTab(element: unknown): element is Tab {\n  return element instanceof HTMLElement && element.hasAttribute('md-tab');\n}\n"]}