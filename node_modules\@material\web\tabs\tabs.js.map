{"version": 3, "file": "tabs.js", "sourceRoot": "", "sources": ["tabs.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,IAAI,EAAC,MAAM,oBAAoB,CAAC;AACxC,OAAO,EAAC,MAAM,EAAC,MAAM,2BAA2B,CAAC;AAQjD,8BAA8B;AAC9B;;;;;GAKG;AAEI,IAAM,MAAM,GAAZ,MAAM,MAAO,SAAQ,IAAI;;AACd,aAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAD5C,MAAM;IADlB,aAAa,CAAC,SAAS,CAAC;GACZ,MAAM,CAElB", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Tabs} from './internal/tabs.js';\nimport {styles} from './internal/tabs-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-tabs': MdTabs;\n  }\n}\n\n// TODO(b/267336507): add docs\n/**\n * @summary Tabs displays a list of selectable tabs.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-tabs')\nexport class MdTabs extends Tabs {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}