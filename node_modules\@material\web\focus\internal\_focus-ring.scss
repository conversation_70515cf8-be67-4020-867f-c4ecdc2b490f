//
// Copyright 2021 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:list';
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use '../../tokens';
// go/keep-sorted end

$_md-sys-motion: tokens.md-sys-motion-values();

@mixin theme($tokens) {
  $supported-tokens: tokens.$md-comp-focus-ring-supported-tokens;
  @each $token, $value in $tokens {
    @if list.index($supported-tokens, $token) == null {
      @error 'Token `#{$token}` is not a supported token.';
    }

    @if $value {
      --md-focus-ring-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $tokens: tokens.md-comp-focus-ring-values();

  $active-width: map.get($tokens, 'active-width');
  $color: map.get($tokens, 'color');
  $duration: map.get($tokens, 'duration');
  $width: map.get($tokens, 'width');
  $inward-offset: map.get($tokens, 'inward-offset');
  $outward-offset: map.get($tokens, 'outward-offset');
  $start-start: map.get($tokens, 'shape-start-start');
  $start-end: map.get($tokens, 'shape-start-end');
  $end-end: map.get($tokens, 'shape-end-end');
  $end-start: map.get($tokens, 'shape-end-start');

  :host {
    animation-delay: 0s, calc($duration * 0.25);
    animation-duration: calc($duration * 0.25), calc($duration * 0.75);
    animation-timing-function: map.get($_md-sys-motion, 'easing-emphasized');
    box-sizing: border-box;
    color: $color;
    display: none;
    pointer-events: none;
    position: absolute;
  }

  :host([visible]) {
    display: flex;
  }

  :host(:not([inward])) {
    animation-name: outward-grow, outward-shrink;
    border-end-end-radius: calc($end-end + $outward-offset);
    border-end-start-radius: calc($end-start + $outward-offset);
    border-start-end-radius: calc($start-end + $outward-offset);
    border-start-start-radius: calc($start-start + $outward-offset);
    inset: calc(-1 * $outward-offset);
    outline: $width solid currentColor;
  }

  :host([inward]) {
    animation-name: inward-grow, inward-shrink;
    border-end-end-radius: calc($end-end - $inward-offset);
    border-end-start-radius: calc($end-start - $inward-offset);
    border-start-end-radius: calc($start-end - $inward-offset);
    border-start-start-radius: calc($start-start - $inward-offset);
    border: $width solid currentColor;
    inset: $inward-offset;
  }

  @keyframes outward-grow {
    from {
      outline-width: 0;
    }
    to {
      outline-width: $active-width;
    }
  }

  @keyframes outward-shrink {
    from {
      outline-width: $active-width;
    }
  }

  @keyframes inward-grow {
    from {
      border-width: 0;
    }
    to {
      border-width: $active-width;
    }
  }

  @keyframes inward-shrink {
    from {
      border-width: $active-width;
    }
  }

  @media (prefers-reduced-motion) {
    :host {
      animation: none;
    }
  }
}
