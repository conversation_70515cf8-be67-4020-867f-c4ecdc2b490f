//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use './md-sys-color';
@use './md-sys-shape';
@use './md-sys-typescale';
@use './v0_192/md-comp-badge';
// go/keep-sorted end

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

$_unsupported-tokens: ('large-label-text-tracking', 'large-label-text-type');

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $tokens: md-comp-badge.values($deps, $exclude-hardcoded-values);
  $tokens: map.remove($tokens, $_unsupported-tokens...);

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      $tokens: map.set($tokens, $token, var(--md-badge-#{$token}, #{$value}));
    }
  }

  @return $tokens;
}
