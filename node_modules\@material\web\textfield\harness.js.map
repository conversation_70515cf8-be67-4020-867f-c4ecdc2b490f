{"version": 3, "file": "harness.js", "sourceRoot": "", "sources": ["harness.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,OAAO,EAAC,MAAM,uBAAuB,CAAC;AAI9C;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,OAAkB;IAAxD;;QACE,wEAAwE;QAChE,sBAAiB,GAAG,EAAE,CAAC;IA8HjC,CAAC;IA5HC;;;;;;;;;;;;OAYG;IACH,KAAK,CAAC,UAAU,CAAC,KAAa;QAC5B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,gBAAgB,CAAC,MAAM,IAAI,CAAC,qBAAqB,EAAE,EAAE,IAAI,CAAC,CAAC;YAChE,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,qBAAqB,EAAE,EAAE,IAAI,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,KAAK,CAAC,WAAW,CAAC,UAAmB,EAAE,QAAiB;QACtD,IAAI,CAAC,gBAAgB,CAAC,MAAM,IAAI,CAAC,qBAAqB,EAAE,EAAE,WAAW,CAAC,CAAC;QACvE,IAAI,CAAC,gBAAgB,CACnB,MAAM,IAAI,CAAC,qBAAqB,EAAE,EAClC,UAAU,EACV,QAAQ,CACT,CAAC;IACJ,CAAC;IAEQ,KAAK,CAAC,KAAK;QAClB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QAC5C,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;IAEQ,KAAK,CAAC,IAAI;QACjB,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,IAAI,CAAC,sBAAsB,CAAC,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;IAClE,CAAC;IAEkB,oBAAoB,CAAC,KAAkB;QACxD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;QAC/B,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC,KAAK,CAAC;QACzC,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAES,aAAa,CACrB,OAA+C,EAC/C,kBAA0B,EAC1B,IAAqB;QAErB,OAAO,CAAC,KAAK,IAAI,kBAAkB,CAAC;QACpC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAG;gBACL,SAAS,EAAE,YAAY;gBACvB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,IAAI;gBACb,WAAW,EAAE,KAAK;gBAClB,IAAI,EAAE,kBAAkB;aACzB,CAAC;QACJ,CAAC;QAED,OAAO,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;IACvD,CAAC;IAES,gBAAgB,CACxB,OAA+C,EAC/C,UAAmB,EACnB,QAAiB,EACjB,IAAqB;QAErB,MAAM,iBAAiB,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QACpE,OAAO,CAAC,KAAK;YACX,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,IAAI,CAAC,CAAC;gBAC3C,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC5D,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAG;gBACL,SAAS,EAAE,uBAAuB;gBAClC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,IAAI;gBACb,WAAW,EAAE,KAAK;gBAClB,IAAI,EAAE,iBAAiB;aACxB,CAAC;QACJ,CAAC;QAED,OAAO,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;IACvD,CAAC;IAES,sBAAsB,CAC9B,OAA+C;QAE/C,IAAI,IAAI,CAAC,iBAAiB,KAAK,OAAO,CAAC,KAAK,EAAE,CAAC;YAC7C,OAAO;QACT,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,KAAK,CAAC;QACvC,OAAO,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC7C,CAAC;IAEkB,KAAK,CAAC,qBAAqB;QAC5C,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAE9B,CAAC;IAC1B,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {<PERSON>rne<PERSON>} from '../testing/harness.js';\n\nimport {TextField} from './internal/text-field.js';\n\n/**\n * Test harness for text field elements.\n */\nexport class TextFieldHarness extends Harness<TextField> {\n  /** Used to track whether or not a change event should be dispatched. */\n  private valueBeforeChange = '';\n\n  /**\n   * Simulates a user typing a value one character at a time. This will fire\n   * multiple input events.\n   *\n   * Use focus/blur to ensure change events are fired.\n   *\n   * @example\n   * await harness.focusWithKeyboard();\n   * await harness.inputValue('value'); // input events\n   * await harness.blur(); // change event\n   *\n   * @param value The value to simulating typing.\n   */\n  async inputValue(value: string) {\n    for (const char of value) {\n      this.simulateKeypress(await this.getInteractiveElement(), char);\n      this.simulateInput(await this.getInteractiveElement(), char);\n    }\n  }\n\n  /**\n   * Simulates a user deleting part of a value with the backspace key.\n   * By default, the entire value is deleted. This will fire a single input\n   * event.\n   *\n   * Use focus/blur to ensure change events are fired.\n   *\n   * @example\n   * await harness.focusWithKeyboard();\n   * await harness.deleteValue(); // input event\n   * await harness.blur(); // change event\n   *\n   * @param beginIndex The starting index of the value to delete.\n   * @param endIndex The ending index of the value to delete.\n   */\n  async deleteValue(beginIndex?: number, endIndex?: number) {\n    this.simulateKeypress(await this.getInteractiveElement(), 'Backspace');\n    this.simulateDeletion(\n      await this.getInteractiveElement(),\n      beginIndex,\n      endIndex,\n    );\n  }\n\n  override async reset() {\n    this.element.reset();\n    this.valueBeforeChange = this.element.value;\n    await super.reset();\n  }\n\n  override async blur() {\n    await super.blur();\n    this.simulateChangeIfNeeded(await this.getInteractiveElement());\n  }\n\n  protected override simulatePointerFocus(input: HTMLElement) {\n    const textField = this.element;\n    if (textField.disabled) {\n      return;\n    }\n\n    this.valueBeforeChange = textField.value;\n    super.simulatePointerFocus(input);\n  }\n\n  protected simulateInput(\n    element: HTMLInputElement | HTMLTextAreaElement,\n    charactersToAppend: string,\n    init?: InputEventInit,\n  ) {\n    element.value += charactersToAppend;\n    if (!init) {\n      init = {\n        inputType: 'insertText',\n        composed: true,\n        bubbles: true,\n        isComposing: false,\n        data: charactersToAppend,\n      };\n    }\n\n    element.dispatchEvent(new InputEvent('input', init));\n  }\n\n  protected simulateDeletion(\n    element: HTMLInputElement | HTMLTextAreaElement,\n    beginIndex?: number,\n    endIndex?: number,\n    init?: InputEventInit,\n  ) {\n    const deletedCharacters = element.value.slice(beginIndex, endIndex);\n    element.value =\n      element.value.substring(0, beginIndex ?? 0) +\n      element.value.substring(endIndex ?? element.value.length);\n    if (!init) {\n      init = {\n        inputType: 'deleteContentBackward',\n        composed: true,\n        bubbles: true,\n        isComposing: false,\n        data: deletedCharacters,\n      };\n    }\n\n    element.dispatchEvent(new InputEvent('input', init));\n  }\n\n  protected simulateChangeIfNeeded(\n    element: HTMLInputElement | HTMLTextAreaElement,\n  ) {\n    if (this.valueBeforeChange === element.value) {\n      return;\n    }\n\n    this.valueBeforeChange = element.value;\n    element.dispatchEvent(new Event('change'));\n  }\n\n  protected override async getInteractiveElement() {\n    await this.element.updateComplete;\n    return this.element.renderRoot.querySelector('.input') as\n      | HTMLInputElement\n      | HTMLTextAreaElement;\n  }\n}\n"]}