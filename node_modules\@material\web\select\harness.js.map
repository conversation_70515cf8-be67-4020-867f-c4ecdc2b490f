{"version": 3, "file": "harness.js", "sourceRoot": "", "sources": ["harness.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EAAC,YAAY,EAAC,MAAM,qBAAqB,CAAC;AAEjD,OAAO,EAAC,OAAO,EAAC,MAAM,uBAAuB,CAAC;AAG9C,OAAO,EAAC,mBAAmB,EAAC,MAAM,oCAAoC,CAAC;AAEvE;;GAEG;AACH,MAAM,OAAO,aAAc,SAAQ,OAAe;IACtC,QAAQ;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAU,CAAC;IAClE,CAAC;IACD;;OAEG;IACgB,KAAK,CAAC,qBAAqB;QAC5C,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;QAClC,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IAEQ,KAAK,CAAC,UAAU;QACvB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACpC,MAAM,OAAO,GAAG,MAAM,IAAI,mBAAmB,CAC3C,KAAK,CACN,CAAC,qBAAqB,EAAE,CAAC;QAC1B,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED,uDAAuD;IACvD,QAAQ;QACN,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAC7B,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,mBAAmB,CAAC,IAAgC,CAAC,CACpE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,KAAK;QACT,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACpC,KAAK,CAAC,KAAK,EAAE,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAa;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,CAAE,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CACV,wEAAwE,CACzE,CAAC;QACJ,CAAC;QACD,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,qBAAqB,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;IACjE,CAAC;IAED,IAAI,MAAM;QACR,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,CAAE,CAAC;QAC/D,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CACb,uGAAuG,CACxG,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;CACF;AAED,+BAA+B;AAC/B,MAAM,mBAAoB,SAAQ,YAAY;IAC5C,4EAA4E;IACnE,qBAAqB;QAC5B,OAAO,KAAK,CAAC,qBAAqB,EAAE,CAAC;IACvC,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {LitElement} from 'lit';\n\nimport {FieldHarness} from '../field/harness.js';\nimport {Field} from '../field/internal/field.js';\nimport {Harness} from '../testing/harness.js';\n\nimport {Select} from './internal/select.js';\nimport {SelectOptionHarness} from './internal/selectoption/harness.js';\n\n/**\n * Test harness for menu.\n */\nexport class SelectHarness extends Harness<Select> {\n  protected getField() {\n    return this.element.renderRoot.querySelector('.field') as Field;\n  }\n  /**\n   * Shows the menu and returns the first list item element.\n   */\n  protected override async getInteractiveElement() {\n    await this.element.updateComplete;\n    return this.getField();\n  }\n\n  override async startHover() {\n    const field = await this.getField();\n    const element = await new SelectFieldHardness(\n      field,\n    ).getInteractiveElement();\n    this.simulateStartHover(element);\n  }\n\n  /** @return ListItem harnesses for the menu's items. */\n  getItems() {\n    return this.element.options.map(\n      (item) => new SelectOptionHarness(item as typeof item & LitElement),\n    );\n  }\n\n  async click() {\n    const field = await this.getField();\n    field.click();\n  }\n\n  async clickOption(index: number) {\n    const menu = this.element.renderRoot.querySelector('md-menu')!;\n    if (!menu.open) {\n      console.warn(\n        'Internal menu is not open. Try calling SelectHarness.prototype.click()',\n      );\n    }\n    (await this.getItems()[index].getInteractiveElement()).click();\n  }\n\n  get isOpen() {\n    const menu = this.element.renderRoot.querySelector('md-menu')!;\n    if (!menu) {\n      throw new Error(\n        'Internal md-menu is not found. md-select may not have finished rendering when isOpen has been checked',\n      );\n    }\n    return menu.open;\n  }\n}\n\n// Private class (not exported)\nclass SelectFieldHardness extends FieldHarness {\n  /* Expose so that we can call it from our internal code in SelectHarness. */\n  override getInteractiveElement() {\n    return super.getInteractiveElement();\n  }\n}\n"]}