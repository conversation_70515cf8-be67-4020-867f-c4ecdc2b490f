{"version": 3, "file": "select-option.js", "sourceRoot": "", "sources": ["select-option.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,iCAAiC,CAAC;AACzC,OAAO,4BAA4B,CAAC;AACpC,OAAO,2BAA2B,CAAC;AAEnC,OAAO,EAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAC,MAAM,KAAK,CAAC;AAC9C,OAAO,EACL,QAAQ,EACR,KAAK,EACL,qBAAqB,EACrB,kBAAkB,GACnB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAY,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAGhE,OAAO,EAAC,kBAAkB,EAAC,MAAM,oCAAoC,CAAC;AAGtE,OAAO,EAAC,sBAAsB,EAAC,MAAM,6BAA6B,CAAC;AA4BnE,wCAAwC;AACxC,MAAM,qBAAqB,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAE7D;;;;;;;;GAQG;AACH,MAAM,OAAO,cACX,SAAQ,qBAAqB;IAD/B;;QAUE;;WAEG;QACuC,aAAQ,GAAG,KAAK,CAAC;QAE3D;;WAEG;QAEH,eAAU,GAAG,IAAI,CAAC;QAElB;;WAEG;QACwB,aAAQ,GAAG,KAAK,CAAC;QAC5C;;WAEG;QACS,UAAK,GAAG,EAAE,CAAC;QAWvB,SAAI,GAAG,QAAiB,CAAC;QA4BR,2BAAsB,GAAG,IAAI,sBAAsB,CAAC,IAAI,EAAE;YACzE,mBAAmB,EAAE,GAAG,EAAE;gBACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAC/B,CAAC;YACD,yBAAyB,EAAE,GAAG,EAAE;gBAC9B,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACrC,CAAC;YACD,kBAAkB,EAAE,GAAG,EAAE;gBACvB,OAAO,IAAI,CAAC,eAAe,CAAC;YAC9B,CAAC;YACD,qBAAqB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY;SAC/C,CAAC,CAAC;IAyFL,CAAC;IA9HC;;;OAGG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;IACnD,CAAC;IAGD,IAAI,aAAa,CAAC,IAAY;QAC5B,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC;IACjD,CAAC;IAGD,IAAI,WAAW,CAAC,IAAY;QAC1B,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;IAekB,MAAM;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAA;;;YAGvB,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,eAAe,EAAE;;;;UAI/C,IAAI,CAAC,UAAU,EAAE;;KAEtB,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACO,cAAc,CAAC,OAAgB;QACvC,OAAO,IAAI,CAAA;;;mBAGI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;eAC1B,IAAI,CAAC,sBAAsB,CAAC,IAAI;qBACzB,IAAwB,CAAC,SAAS,IAAI,OAAO;wBAC1C,IAAwB,CAAC,YAAY,IAAI,OAAO;uBACjD,IAAwB,CAAC,WAAW,IAAI,OAAO;wBAC9C,IAAwB,CAAC,YAAY,IAAI,OAAO;wBAChD,IAAwB,CAAC,YAAY,IAAI,OAAO;2BAC9C,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;iBAC3C,IAAI,CAAC,sBAAsB,CAAC,OAAO;mBACjC,IAAI,CAAC,sBAAsB,CAAC,SAAS;WAC7C,OAAO;;KAEb,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,YAAY;QACpB,OAAO,IAAI,CAAA;;;kBAGG,IAAI,CAAC,QAAQ,eAAe,CAAC;IAC7C,CAAC;IAED;;OAEG;IACO,eAAe;QACvB,OAAO,IAAI,CAAA;;;8BAGe,CAAC;IAC7B,CAAC;IAED;;OAEG;IACO,gBAAgB;QACxB,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,QAAQ;YACzB,UAAU,EAAE,IAAI,CAAC,QAAQ;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,UAAU;QAClB,OAAO,IAAI,CAAA;;;;;;;;KAQV,CAAC;IACJ,CAAC;IAEQ,KAAK;QACZ,wEAAwE;QACxE,qDAAqD;QACrD,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC;IAC7B,CAAC;;AAlKD,kBAAkB;AACF,gCAAiB,GAAG;IAClC,GAAG,UAAU,CAAC,iBAAiB;IAC/B,cAAc,EAAE,IAAI;CACrB,AAHgC,CAG/B;AAKwC;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;gDAAkB;AAM3D;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;kDAClD;AAKS;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;gDAAkB;AAIhC;IAAX,QAAQ,EAAE;6CAAY;AAEiB;IAAvC,KAAK,CAAC,YAAY,CAAC;oDAAsD;AAGvD;IADlB,qBAAqB,CAAC,EAAC,IAAI,EAAE,UAAU,EAAC,CAAC;wDACU;AAEjC;IADlB,qBAAqB,CAAC,EAAC,IAAI,EAAE,iBAAiB,EAAC,CAAC;8DACS;AAEvC;IADlB,kBAAkB,CAAC,EAAC,IAAI,EAAE,EAAE,EAAC,CAAC;uDACgB;AAa/C;IADC,QAAQ,CAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAC;mDAGvC;AAWD;IADC,QAAQ,CAAC,EAAC,SAAS,EAAE,cAAc,EAAC,CAAC;iDAGrC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../../focus/md-focus-ring.js';\nimport '../../../labs/item/item.js';\nimport '../../../ripple/ripple.js';\n\nimport {html, LitElement, nothing} from 'lit';\nimport {\n  property,\n  query,\n  queryAssignedElements,\n  queryAssignedNodes,\n} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\nimport {MenuItem} from '../../../menu/internal/controllers/menuItemController.js';\n\nimport {SelectOptionController} from './selectOptionController.js';\n\n/**\n * The interface specific to a Select Option\n */\ninterface SelectOptionSelf {\n  /**\n   * The form value associated with the Select Option. (Note: the visual portion\n   * of the SelectOption is the headline defined in ListItem)\n   */\n  value: string;\n  /**\n   * Whether or not the SelectOption is selected.\n   */\n  selected: boolean;\n  /**\n   * The text to display in the select when selected. Defaults to the\n   * textContent of the Element slotted into the headline.\n   */\n  displayText: string;\n}\n\n/**\n * The interface to implement for a select option. Additionally, the element\n * must have `md-list-item` and `md-menu-item` attributes on the host.\n */\nexport type SelectOption = SelectOptionSelf & MenuItem;\n\n// Separate variable needed for closure.\nconst selectOptionBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * @fires close-menu {CustomEvent<{initiator: SelectOption, reason: Reason, itemPath: SelectOption[]}>}\n * Closes the encapsulating menu on closable interaction. --bubbles --composed\n * @fires request-selection {Event} Requests the parent md-select to select this\n * element (and deselect others if single-selection) when `selected` changed to\n * `true`. --bubbles --composed\n * @fires request-deselection {Event} Requests the parent md-select to deselect\n * this element when `selected` changed to `false`. --bubbles --composed\n */\nexport class SelectOptionEl\n  extends selectOptionBaseClass\n  implements SelectOption\n{\n  /** @nocollapse */\n  static override shadowRootOptions = {\n    ...LitElement.shadowRootOptions,\n    delegatesFocus: true,\n  };\n\n  /**\n   * Disables the item and makes it non-selectable and non-interactive.\n   */\n  @property({type: Boolean, reflect: true}) disabled = false;\n\n  /**\n   * READONLY: self-identifies as a menu item and sets its identifying attribute\n   */\n  @property({type: Boolean, attribute: 'md-menu-item', reflect: true})\n  isMenuItem = true;\n\n  /**\n   * Sets the item in the selected visual state when a submenu is opened.\n   */\n  @property({type: Boolean}) selected = false;\n  /**\n   * Form value of the option.\n   */\n  @property() value = '';\n\n  @query('.list-item') protected readonly listItemRoot!: HTMLElement | null;\n\n  @queryAssignedElements({slot: 'headline'})\n  protected readonly headlineElements!: HTMLElement[];\n  @queryAssignedElements({slot: 'supporting-text'})\n  protected readonly supportingTextElements!: HTMLElement[];\n  @queryAssignedNodes({slot: ''})\n  protected readonly defaultElements!: Element[];\n\n  type = 'option' as const;\n\n  /**\n   * The text that is selectable via typeahead. If not set, defaults to the\n   * innerText of the item slotted into the `\"headline\"` slot.\n   */\n  get typeaheadText() {\n    return this.selectOptionController.typeaheadText;\n  }\n\n  @property({attribute: 'typeahead-text'})\n  set typeaheadText(text: string) {\n    this.selectOptionController.setTypeaheadText(text);\n  }\n\n  /**\n   * The text that is displayed in the select field when selected. If not set,\n   * defaults to the textContent of the item slotted into the `\"headline\"` slot.\n   */\n  get displayText() {\n    return this.selectOptionController.displayText;\n  }\n\n  @property({attribute: 'display-text'})\n  set displayText(text: string) {\n    this.selectOptionController.setDisplayText(text);\n  }\n\n  private readonly selectOptionController = new SelectOptionController(this, {\n    getHeadlineElements: () => {\n      return this.headlineElements;\n    },\n    getSupportingTextElements: () => {\n      return this.supportingTextElements;\n    },\n    getDefaultElements: () => {\n      return this.defaultElements;\n    },\n    getInteractiveElement: () => this.listItemRoot,\n  });\n\n  protected override render() {\n    return this.renderListItem(html`\n      <md-item>\n        <div slot=\"container\">\n          ${this.renderRipple()} ${this.renderFocusRing()}\n        </div>\n        <slot name=\"start\" slot=\"start\"></slot>\n        <slot name=\"end\" slot=\"end\"></slot>\n        ${this.renderBody()}\n      </md-item>\n    `);\n  }\n\n  /**\n   * Renders the root list item.\n   *\n   * @param content the child content of the list item.\n   */\n  protected renderListItem(content: unknown) {\n    return html`\n      <li\n        id=\"item\"\n        tabindex=${this.disabled ? -1 : 0}\n        role=${this.selectOptionController.role}\n        aria-label=${(this as ARIAMixinStrict).ariaLabel || nothing}\n        aria-selected=${(this as ARIAMixinStrict).ariaSelected || nothing}\n        aria-checked=${(this as ARIAMixinStrict).ariaChecked || nothing}\n        aria-expanded=${(this as ARIAMixinStrict).ariaExpanded || nothing}\n        aria-haspopup=${(this as ARIAMixinStrict).ariaHasPopup || nothing}\n        class=\"list-item ${classMap(this.getRenderClasses())}\"\n        @click=${this.selectOptionController.onClick}\n        @keydown=${this.selectOptionController.onKeydown}\n        >${content}</li\n      >\n    `;\n  }\n\n  /**\n   * Handles rendering of the ripple element.\n   */\n  protected renderRipple() {\n    return html` <md-ripple\n      part=\"ripple\"\n      for=\"item\"\n      ?disabled=${this.disabled}></md-ripple>`;\n  }\n\n  /**\n   * Handles rendering of the focus ring.\n   */\n  protected renderFocusRing() {\n    return html` <md-focus-ring\n      part=\"focus-ring\"\n      for=\"item\"\n      inward></md-focus-ring>`;\n  }\n\n  /**\n   * Classes applied to the list item root.\n   */\n  protected getRenderClasses(): ClassInfo {\n    return {\n      'disabled': this.disabled,\n      'selected': this.selected,\n    };\n  }\n\n  /**\n   * Handles rendering the headline and supporting text.\n   */\n  protected renderBody() {\n    return html`\n      <slot></slot>\n      <slot name=\"overline\" slot=\"overline\"></slot>\n      <slot name=\"headline\" slot=\"headline\"></slot>\n      <slot name=\"supporting-text\" slot=\"supporting-text\"></slot>\n      <slot\n        name=\"trailing-supporting-text\"\n        slot=\"trailing-supporting-text\"></slot>\n    `;\n  }\n\n  override focus() {\n    // TODO(b/300334509): needed for some cases where delegatesFocus doesn't\n    // work programmatically like in FF and select-option\n    this.listItemRoot?.focus();\n  }\n}\n"]}