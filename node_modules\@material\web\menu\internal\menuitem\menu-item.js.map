{"version": 3, "file": "menu-item.js", "sourceRoot": "", "sources": ["menu-item.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,iCAAiC,CAAC;AACzC,OAAO,4BAA4B,CAAC;AACpC,OAAO,2BAA2B,CAAC;AAEnC,OAAO,EAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAiB,MAAM,KAAK,CAAC;AAC9D,OAAO,EACL,QAAQ,EACR,KAAK,EACL,qBAAqB,EACrB,kBAAkB,GACnB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAY,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAChE,OAAO,EAAC,OAAO,EAAE,IAAI,IAAI,UAAU,EAAc,MAAM,oBAAoB,CAAC;AAG5E,OAAO,EAAC,kBAAkB,EAAC,MAAM,oCAAoC,CAAC;AACtE,OAAO,EAEL,kBAAkB,GAEnB,MAAM,sCAAsC,CAAC;AAE9C,wCAAwC;AACxC,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAEzD;;;GAGG;AACH,MAAM,OAAO,UAAW,SAAQ,iBAAiB;IAAjD;;QAOE;;WAEG;QACuC,aAAQ,GAAG,KAAK,CAAC;QAE3D;;WAEG;QACS,SAAI,GAAiB,UAAU,CAAC;QAE5C;;WAEG;QACS,SAAI,GAAG,EAAE,CAAC;QAEtB;;;WAGG;QACS,WAAM,GAAiD,EAAE,CAAC;QAEtE;;WAEG;QACgD,aAAQ,GAAG,KAAK,CAAC;QAEpE;;WAEG;QACwB,aAAQ,GAAG,KAAK,CAAC;QAwB3B,uBAAkB,GAAG,IAAI,kBAAkB,CAAC,IAAI,EAAE;YACjE,mBAAmB,EAAE,GAAG,EAAE;gBACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAC/B,CAAC;YACD,yBAAyB,EAAE,GAAG,EAAE;gBAC9B,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACrC,CAAC;YACD,kBAAkB,EAAE,GAAG,EAAE;gBACvB,OAAO,IAAI,CAAC,eAAe,CAAC;YAC9B,CAAC;YACD,qBAAqB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY;SAC/C,CAAC,CAAC;IA4GL,CAAC;IApIC;;;OAGG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC;IAC/C,CAAC;IAGD,IAAI,aAAa,CAAC,IAAY;QAC5B,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAekB,MAAM;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAA;;;YAGvB,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,eAAe,EAAE;;;;UAI/C,IAAI,CAAC,UAAU,EAAE;;KAEtB,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACO,cAAc,CAAC,OAAgB;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC;QACtC,IAAI,GAAgB,CAAC;QACrB,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YACxC,KAAK,GAAG;gBACN,GAAG,GAAG,OAAO,CAAA,GAAG,CAAC;gBACjB,MAAM;YACR,KAAK,QAAQ;gBACX,GAAG,GAAG,OAAO,CAAA,QAAQ,CAAC;gBACtB,MAAM;YACR,QAAQ;YACR,KAAK,IAAI;gBACP,GAAG,GAAG,OAAO,CAAA,IAAI,CAAC;gBAClB,MAAM;QACV,CAAC;QAED,2EAA2E;QAC3E,0DAA0D;QAC1D,MAAM,MAAM,GAAG,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;QACjE,OAAO,UAAU,CAAA;SACZ,GAAG;;mBAEO,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;eACvC,IAAI,CAAC,kBAAkB,CAAC,IAAI;qBACrB,IAAwB,CAAC,SAAS,IAAI,OAAO;wBAC1C,IAAwB,CAAC,YAAY,IAAI,OAAO;uBACjD,IAAwB,CAAC,WAAW,IAAI,OAAO;wBAC9C,IAAwB,CAAC,YAAY,IAAI,OAAO;wBAChD,IAAwB,CAAC,YAAY,IAAI,OAAO;2BAC9C,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;eAC7C,IAAI,CAAC,IAAI,IAAI,OAAO;iBAClB,MAAM;iBACN,IAAI,CAAC,kBAAkB,CAAC,OAAO;mBAC7B,IAAI,CAAC,kBAAkB,CAAC,SAAS;SAC3C,OAAO,KAAK,GAAG;KACnB,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,YAAY;QACpB,OAAO,IAAI,CAAA;;;kBAGG,IAAI,CAAC,QAAQ,eAAe,CAAC;IAC7C,CAAC;IAED;;OAEG;IACO,eAAe;QACvB,OAAO,IAAI,CAAA;;;8BAGe,CAAC;IAC7B,CAAC;IAED;;OAEG;IACO,gBAAgB;QACxB,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,QAAQ;YACzB,UAAU,EAAE,IAAI,CAAC,QAAQ;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,UAAU;QAClB,OAAO,IAAI,CAAA;;;;;;;;KAQV,CAAC;IACJ,CAAC;IAEQ,KAAK;QACZ,wEAAwE;QACxE,qDAAqD;QACrD,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC;IAC7B,CAAC;;AAjLD,kBAAkB;AACF,4BAAiB,GAAG;IAClC,GAAG,UAAU,CAAC,iBAAiB;IAC/B,cAAc,EAAE,IAAI;CACrB,AAHgC,CAG/B;AAKwC;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;4CAAkB;AAK/C;IAAX,QAAQ,EAAE;wCAAiC;AAKhC;IAAX,QAAQ,EAAE;wCAAW;AAMV;IAAX,QAAQ,EAAE;0CAA2D;AAKnB;IAAlD,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAC,CAAC;4CAAkB;AAKzC;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;4CAAkB;AAEJ;IAAvC,KAAK,CAAC,YAAY,CAAC;gDAAsD;AAGvD;IADlB,qBAAqB,CAAC,EAAC,IAAI,EAAE,UAAU,EAAC,CAAC;oDACU;AAEjC;IADlB,qBAAqB,CAAC,EAAC,IAAI,EAAE,iBAAiB,EAAC,CAAC;0DACS;AAEvC;IADlB,kBAAkB,CAAC,EAAC,IAAI,EAAE,EAAE,EAAC,CAAC;mDACa;AAW5C;IADC,QAAQ,CAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAC;+CAGvC", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../../focus/md-focus-ring.js';\nimport '../../../labs/item/item.js';\nimport '../../../ripple/ripple.js';\n\nimport {html, LitElement, nothing, TemplateResult} from 'lit';\nimport {\n  property,\n  query,\n  queryAssignedElements,\n  queryAssignedNodes,\n} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\nimport {literal, html as staticHtml, StaticValue} from 'lit/static-html.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\nimport {\n  MenuItem,\n  MenuItemController,\n  type MenuItemType,\n} from '../controllers/menuItemController.js';\n\n// Separate variable needed for closure.\nconst menuItemBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * @fires close-menu {CustomEvent<{initiator: SelectOption, reason: Reason, itemPath: SelectOption[]}>}\n * Closes the encapsulating menu on closable interaction. --bubbles --composed\n */\nexport class MenuItemEl extends menuItemBaseClass implements MenuItem {\n  /** @nocollapse */\n  static override shadowRootOptions = {\n    ...LitElement.shadowRootOptions,\n    delegatesFocus: true,\n  };\n\n  /**\n   * Disables the item and makes it non-selectable and non-interactive.\n   */\n  @property({type: Boolean, reflect: true}) disabled = false;\n\n  /**\n   * Sets the behavior and role of the menu item, defaults to \"menuitem\".\n   */\n  @property() type: MenuItemType = 'menuitem';\n\n  /**\n   * Sets the underlying `HTMLAnchorElement`'s `href` resource attribute.\n   */\n  @property() href = '';\n\n  /**\n   * Sets the underlying `HTMLAnchorElement`'s `target` attribute when `href` is\n   * set.\n   */\n  @property() target: '_blank' | '_parent' | '_self' | '_top' | '' = '';\n\n  /**\n   * Keeps the menu open if clicked or keyboard selected.\n   */\n  @property({type: Boolean, attribute: 'keep-open'}) keepOpen = false;\n\n  /**\n   * Sets the item in the selected visual state when a submenu is opened.\n   */\n  @property({type: Boolean}) selected = false;\n\n  @query('.list-item') protected readonly listItemRoot!: HTMLElement | null;\n\n  @queryAssignedElements({slot: 'headline'})\n  protected readonly headlineElements!: HTMLElement[];\n  @queryAssignedElements({slot: 'supporting-text'})\n  protected readonly supportingTextElements!: HTMLElement[];\n  @queryAssignedNodes({slot: ''})\n  protected readonly defaultElements!: Node[];\n\n  /**\n   * The text that is selectable via typeahead. If not set, defaults to the\n   * innerText of the item slotted into the `\"headline\"` slot.\n   */\n  get typeaheadText() {\n    return this.menuItemController.typeaheadText;\n  }\n\n  @property({attribute: 'typeahead-text'})\n  set typeaheadText(text: string) {\n    this.menuItemController.setTypeaheadText(text);\n  }\n\n  private readonly menuItemController = new MenuItemController(this, {\n    getHeadlineElements: () => {\n      return this.headlineElements;\n    },\n    getSupportingTextElements: () => {\n      return this.supportingTextElements;\n    },\n    getDefaultElements: () => {\n      return this.defaultElements;\n    },\n    getInteractiveElement: () => this.listItemRoot,\n  });\n\n  protected override render() {\n    return this.renderListItem(html`\n      <md-item>\n        <div slot=\"container\">\n          ${this.renderRipple()} ${this.renderFocusRing()}\n        </div>\n        <slot name=\"start\" slot=\"start\"></slot>\n        <slot name=\"end\" slot=\"end\"></slot>\n        ${this.renderBody()}\n      </md-item>\n    `);\n  }\n\n  /**\n   * Renders the root list item.\n   *\n   * @param content the child content of the list item.\n   */\n  protected renderListItem(content: unknown) {\n    const isAnchor = this.type === 'link';\n    let tag: StaticValue;\n    switch (this.menuItemController.tagName) {\n      case 'a':\n        tag = literal`a`;\n        break;\n      case 'button':\n        tag = literal`button`;\n        break;\n      default:\n      case 'li':\n        tag = literal`li`;\n        break;\n    }\n\n    // TODO(b/265339866): announce \"button\"/\"link\" inside of a list item. Until\n    // then all are \"menuitem\" roles for correct announcement.\n    const target = isAnchor && !!this.target ? this.target : nothing;\n    return staticHtml`\n      <${tag}\n        id=\"item\"\n        tabindex=${this.disabled && !isAnchor ? -1 : 0}\n        role=${this.menuItemController.role}\n        aria-label=${(this as ARIAMixinStrict).ariaLabel || nothing}\n        aria-selected=${(this as ARIAMixinStrict).ariaSelected || nothing}\n        aria-checked=${(this as ARIAMixinStrict).ariaChecked || nothing}\n        aria-expanded=${(this as ARIAMixinStrict).ariaExpanded || nothing}\n        aria-haspopup=${(this as ARIAMixinStrict).ariaHasPopup || nothing}\n        class=\"list-item ${classMap(this.getRenderClasses())}\"\n        href=${this.href || nothing}\n        target=${target}\n        @click=${this.menuItemController.onClick}\n        @keydown=${this.menuItemController.onKeydown}\n      >${content}</${tag}>\n    `;\n  }\n\n  /**\n   * Handles rendering of the ripple element.\n   */\n  protected renderRipple(): TemplateResult | typeof nothing {\n    return html` <md-ripple\n      part=\"ripple\"\n      for=\"item\"\n      ?disabled=${this.disabled}></md-ripple>`;\n  }\n\n  /**\n   * Handles rendering of the focus ring.\n   */\n  protected renderFocusRing(): TemplateResult | typeof nothing {\n    return html` <md-focus-ring\n      part=\"focus-ring\"\n      for=\"item\"\n      inward></md-focus-ring>`;\n  }\n\n  /**\n   * Classes applied to the list item root.\n   */\n  protected getRenderClasses(): ClassInfo {\n    return {\n      'disabled': this.disabled,\n      'selected': this.selected,\n    };\n  }\n\n  /**\n   * Handles rendering the headline and supporting text.\n   */\n  protected renderBody() {\n    return html`\n      <slot></slot>\n      <slot name=\"overline\" slot=\"overline\"></slot>\n      <slot name=\"headline\" slot=\"headline\"></slot>\n      <slot name=\"supporting-text\" slot=\"supporting-text\"></slot>\n      <slot\n        name=\"trailing-supporting-text\"\n        slot=\"trailing-supporting-text\"></slot>\n    `;\n  }\n\n  override focus() {\n    // TODO(b/300334509): needed for some cases where delegatesFocus doesn't\n    // work programmatically like in FF and select-option\n    this.listItemRoot?.focus();\n  }\n}\n"]}