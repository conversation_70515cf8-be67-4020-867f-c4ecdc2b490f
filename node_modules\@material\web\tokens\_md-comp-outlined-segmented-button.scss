//
// Copyright 2023 Google LLC
// SPDX-License-Ide

// go/keep-sorted start
@use 'sass:map';
@use 'sass:string';
// go/keep-sorted end
// go/keep-sorted start
@use './internal/shape';
@use './internal/validate';
@use './md-sys-color';
@use './md-sys-shape';
@use './md-sys-state';
@use './md-sys-typescale';
@use './v0_192/md-comp-outlined-segmented-button';
// go/keep-sorted end

$supported-tokens: (
  // go/keep-sorted start
  'container-height',
  'disabled-icon-color',
  'disabled-label-text-color',
  'disabled-outline-color',
  'hover-state-layer-opacity',
  'icon-size',
  'label-text-font',
  'label-text-line-height',
  'label-text-size',
  'label-text-weight',
  'outline-color',
  'pressed-state-layer-opacity',
  'selected-container-color',
  'selected-focus-icon-color',
  'selected-focus-label-text-color',
  'selected-hover-icon-color',
  'selected-hover-label-text-color',
  'selected-hover-state-layer-color',
  'selected-icon-color',
  'selected-label-text-color',
  'selected-pressed-icon-color',
  'selected-pressed-label-text-color',
  'selected-pressed-state-layer-color',
  'shape',
  'shape-end-end',
  'shape-end-start',
  'shape-start-end',
  'shape-start-start',
  'unselected-focus-icon-color',
  'unselected-focus-label-text-color',
  'unselected-hover-icon-color',
  'unselected-hover-label-text-color',
  'unselected-hover-state-layer-color',
  'unselected-icon-color',
  'unselected-label-text-color',
  'unselected-pressed-icon-color',
  'unselected-pressed-label-text-color',
  'unselected-pressed-state-layer-color',
  // go/keep-sorted end
);

$unsupported-tokens: (
  // go/keep-sorted start
  'disabled-icon-opacity',
  'disabled-label-text-opacity',
  'disabled-outline-opacity',
  'focus-state-layer-opacity',
  'label-text-tracking',
  'label-text-type',
  'outline-width',
  'selected-focus-state-layer-color',
  'unselected-focus-state-layer-color',
  // go/keep-sorted end
);

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $tokens: md-comp-outlined-segmented-button.values(
    $deps,
    $exclude-hardcoded-values
  );

  $new-tokens: shape.get-new-logical-shape-tokens($tokens, 'shape');
  $tokens: validate.values(
    $tokens,
    $supported-tokens: $supported-tokens,
    $unsupported-tokens: $unsupported-tokens,
    $new-tokens: $new-tokens,
    $renamed-tokens: (
      // Remove "with-*" prefixes (b/273534858)
      'with-icon-icon-size': 'icon-size',
      'selected-with-icon-icon-color': 'selected-icon-color',
      'unselected-with-icon-icon-color': 'unselected-icon-color'
    )
  );

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      @if string.index($token, 'shape-') == 1 {
        // Add fallback to shorthand for logical shape properties.
        $value: var(--md-outlined-segmented-button-shape, #{$value});
      }

      $tokens: map.set(
        $tokens,
        $token,
        var(--md-outlined-segmented-button-#{$token}, #{$value})
      );
    }
  }

  @return $tokens;
}
