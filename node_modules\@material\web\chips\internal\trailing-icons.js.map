{"version": 3, "file": "trailing-icons.js", "sourceRoot": "", "sources": ["trailing-icons.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,8BAA8B,CAAC;AACtC,OAAO,wBAAwB,CAAC;AAEhC,OAAO,EAAC,IAAI,EAAE,OAAO,EAAC,MAAM,KAAK,CAAC;AAWlC,iBAAiB;AACjB,MAAM,UAAU,kBAAkB,CAAC,EACjC,SAAS,EACT,QAAQ,EACR,aAAa,EACb,QAAQ,GAAG,KAAK,GACO;IACvB,4EAA4E;IAC5E,8EAA8E;IAC9E,yEAAyE;IACzE,OAAO,IAAI,CAAA;;;;mBAIM,SAAS,IAAI,OAAO;wBACf,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,OAAO;iBAClD,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;eAC1B,iBAAiB;eACjB,aAAa;;6BAEC,QAAQ;;;;;;;;;;;GAWlC,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAa,KAAY;IACjD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;QACvC,OAAO;IACT,CAAC;IAED,KAAK,CAAC,eAAe,EAAE,CAAC;IACxB,MAAM,cAAc,GAAG,CAAC,IAAI,CAAC,aAAa,CACxC,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAC,UAAU,EAAE,IAAI,EAAC,CAAC,CACxC,CAAC;IACF,IAAI,cAAc,EAAE,CAAC;QACnB,OAAO;IACT,CAAC;IAED,IAAI,CAAC,MAAM,EAAE,CAAC;AAChB,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, nothing} from 'lit';\n\nimport {Chip} from './chip.js';\n\ninterface RemoveButtonProperties {\n  ariaLabel: string | null;\n  disabled: boolean;\n  focusListener: EventListener;\n  tabbable?: boolean;\n}\n\n/** @protected */\nexport function renderRemoveButton({\n  ariaLabel,\n  disabled,\n  focusListener,\n  tabbable = false,\n}: RemoveButtonProperties) {\n  // When an aria-label is not provided, we use two spans with aria-labelledby\n  // to create the \"Remove <textContent>\" label for the remove button. The first\n  // is this #remove-label span, the second is the chip's #label slot span.\n  return html`\n    <span id=\"remove-label\" hidden aria-hidden=\"true\">Remove</span>\n    <button\n      class=\"trailing action\"\n      aria-label=${ariaLabel || nothing}\n      aria-labelledby=${!ariaLabel ? 'remove-label label' : nothing}\n      tabindex=${!tabbable ? -1 : nothing}\n      @click=${handleRemoveClick}\n      @focus=${focusListener}>\n      <md-focus-ring part=\"trailing-focus-ring\"></md-focus-ring>\n      <md-ripple ?disabled=${disabled}></md-ripple>\n      <span class=\"trailing icon\" aria-hidden=\"true\">\n        <slot name=\"remove-trailing-icon\">\n          <svg viewBox=\"0 96 960 960\">\n            <path\n              d=\"m249 849-42-42 231-231-231-231 42-42 231 231 231-231 42 42-231 231 231 231-42 42-231-231-231 231Z\" />\n          </svg>\n        </slot>\n      </span>\n      <span class=\"touch\"></span>\n    </button>\n  `;\n}\n\nfunction handleRemoveClick(this: Chip, event: Event) {\n  if (this.disabled || this.softDisabled) {\n    return;\n  }\n\n  event.stopPropagation();\n  const preventDefault = !this.dispatchEvent(\n    new Event('remove', {cancelable: true}),\n  );\n  if (preventDefault) {\n    return;\n  }\n\n  this.remove();\n}\n"]}