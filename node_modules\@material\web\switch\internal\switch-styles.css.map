{"version": 3, "sourceRoot": "", "sources": ["_switch.scss", "../../focus/internal/_focus-ring.scss", "_track.scss", "_handle.scss", "../../ripple/internal/_ripple.scss", "_icon.scss"], "names": [], "mappings": "AAiCE,mBACA,cACE,MACE,oBACA,aACA,mBACA,0CACA,eAGF,kBACE,eAGF,8BACE,mEAGF,cC5BE,4jBDuCF,QACE,mBACA,oBACA,cACA,kBACA,yCACA,2CAGA,0IACA,sIACA,kIACA,sIAIF,MACE,gBACA,0DACA,aACA,SACA,kBACA,yDACA,UACA,eACA,QACA,SACA,gCAGF,iCACE,cEnFJ,cACE,OACE,kBACA,WACA,YACA,sBAEA,sBAGA,aACA,uBACA,mBAIF,eACE,WACA,aACA,kBACA,YACA,WACA,sBACA,sBACA,6CACA,kCACA,yBAGF,iBACE,+BACA,2BAGF,iDAEE,gBACA,sDAGF,yBACE,4BAGF,yBACE,6FAGF,+BACE,mGAGF,sCACE,mGAGF,gCACE,qGAGF,0BACE,2BAGF,kCACE,yGAGF,2BACE,sGACA,wFACA,mBACA,uDAGF,iCACE,4GACA,8FAGF,yCACE,4GACA,8FAGF,kCACE,8GACA,gGAGF,oCACE,+GACA,qGAIJ,WACE,8BACE,yBACE,sBACA,wBAGF,yBACE,sBACA,UAGF,kCACE,sBCtGN,cACE,kBACE,aACA,qBACA,mBACA,kBAEA,gEAOF,4BACE,oBALO,+EAQT,8BACE,kBATO,+EAYT,4BACE,gBAGF,QAEE,4IACA,wIACA,oIACA,wIACA,4CACA,0CAEA,wBACA,iCACA,gCACA,iFACA,UAGF,gBACE,WACA,aACA,QACA,kBACA,sBACA,sBACA,wCAGF,4CAEE,gBAGF,kBACE,qDACA,mDAGF,kBACE,sDACA,oDAGF,kFAEE,oDACA,kDACA,kCACA,0BAGF,0BACE,8FAGF,gCACE,8GAGF,uCACE,8GAGF,iCACE,gHAGF,mCACE,uGACA,6DAGF,4BACE,qFAGF,kCACE,sGAGF,yCACE,sGAGF,mCACE,wGAGF,qCACE,iGACA,uDAGF,UACE,0FACA,+CACA,YACA,8CAGF,oBCxHE,oZDsIF,sBCtIE,uXDkJJ,WACE,8BACE,4BACE,sBAGF,0BACE,UAGF,qCACE,sBElKN,cACE,OACE,kBACA,YACA,WAGF,MACE,kBACA,QACA,YACA,aACA,mBACA,uBACA,kBAEA,2FAEA,UAGF,gBACE,gBAGF,2CAEE,UAIF,8CACE,yBAGF,WACE,uCACA,wCACA,0FAGF,6BACE,gGAGF,oCACE,gGAGF,8BACE,kGAGF,gCACE,mGACA,qDAGF,UACE,gDACA,iDACA,8FAGF,0BACE,oGAGF,iCACE,oGAGF,2BACE,sGAGF,6BACE,6FACA,+DAIJ,WACE,8BACE,WACE,YAGF,UACE,gBAGF,6DAEE,UAGF,oBACE", "file": "switch-styles.css"}