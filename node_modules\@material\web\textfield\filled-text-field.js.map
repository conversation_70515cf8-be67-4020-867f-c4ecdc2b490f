{"version": 3, "file": "filled-text-field.js", "sourceRoot": "", "sources": ["filled-text-field.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,0BAA0B,CAAC;AAGlC,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAC,OAAO,EAAC,MAAM,oBAAoB,CAAC;AAE3C,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AACnE,OAAO,EAAC,eAAe,EAAC,MAAM,iCAAiC,CAAC;AAChE,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAUnE;;;;GAIG;AAEI,IAAM,iBAAiB,GAAvB,MAAM,iBAAkB,SAAQ,eAAe;IAA/C;;QAGuB,aAAQ,GAAG,OAAO,CAAA,iBAAiB,CAAC;IAClE,CAAC;;AAHiB,wBAAM,GAAwB,CAAC,YAAY,EAAE,YAAY,CAAC,AAApD,CAAqD;AADhE,iBAAiB;IAD7B,aAAa,CAAC,sBAAsB,CAAC;GACzB,iBAAiB,CAI7B", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../field/filled-field.js';\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\nimport {literal} from 'lit/static-html.js';\n\nimport {styles as filledStyles} from './internal/filled-styles.js';\nimport {FilledTextField} from './internal/filled-text-field.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\nexport {type TextFieldType} from './internal/text-field.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-filled-text-field': MdFilledTextField;\n  }\n}\n\n/**\n * TODO(b/228525797): Add docs\n * @final\n * @suppress {visibility}\n */\n@customElement('md-filled-text-field')\nexport class MdFilledTextField extends FilledTextField {\n  static override styles: CSSResultOrNative[] = [sharedStyles, filledStyles];\n\n  protected override readonly fieldTag = literal`md-filled-field`;\n}\n"]}