{"version": 3, "file": "switch.js", "sourceRoot": "", "sources": ["switch.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,MAAM,EAAC,MAAM,sBAAsB,CAAC;AAC5C,OAAO,EAAC,MAAM,EAAC,MAAM,6BAA6B,CAAC;AAQnD;;;;;;;;;GASG;AAEI,IAAM,QAAQ,GAAd,MAAM,QAAS,SAAQ,MAAM;;AAClB,eAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAD5C,QAAQ;IADpB,aAAa,CAAC,WAAW,CAAC;GACd,QAAQ,CAEpB", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Switch} from './internal/switch.js';\nimport {styles} from './internal/switch-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-switch': MdSwitch;\n  }\n}\n\n/**\n * @summary Switches toggle the state of a single item on or off.\n *\n * @description\n * There's one type of switch in Material. Use this selection control when the\n * user needs to toggle a single item on or off.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-switch')\nexport class MdSwitch extends Switch {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}