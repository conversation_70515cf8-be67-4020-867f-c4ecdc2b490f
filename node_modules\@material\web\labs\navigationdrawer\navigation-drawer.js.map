{"version": 3, "file": "navigation-drawer.js", "sourceRoot": "", "sources": ["navigation-drawer.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,gBAAgB,EAAC,MAAM,iCAAiC,CAAC;AACjE,OAAO,EAAC,MAAM,EAAC,MAAM,wCAAwC,CAAC;AAC9D,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAQnE;;;GAGG;AAEI,IAAM,kBAAkB,GAAxB,MAAM,kBAAmB,SAAQ,gBAAgB;;AAC7B,yBAAM,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,AAAzB,CAA0B;AAD9C,kBAAkB;IAD9B,aAAa,CAAC,sBAAsB,CAAC;GACzB,kBAAkB,CAE9B", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {customElement} from 'lit/decorators.js';\n\nimport {NavigationDrawer} from './internal/navigation-drawer.js';\nimport {styles} from './internal/navigation-drawer-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-navigation-drawer': MdNavigationDrawer;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-navigation-drawer')\nexport class MdNavigationDrawer extends NavigationDrawer {\n  static override readonly styles = [sharedStyles, styles];\n}\n"]}