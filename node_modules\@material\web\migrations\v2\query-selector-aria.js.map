{"version": 3, "file": "query-selector-aria.js", "sourceRoot": "", "sources": ["query-selector-aria.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,MAAM,wBAAwB,GAAG,iBAAiB,CAAC;AAEnD;;;;;;;;;;;;;;;GAeG;AACH,MAAM,UAAU,YAAY,CAAC,QAAgB;IAC3C,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC7C,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,MAAM,uBAAuB,GAAG,QAAQ,CAAC,UAAU,CACjD,wBAAwB,EACxB,UAAU,CACX,CAAC;IACF,OAAO,GAAG,QAAQ,IAAI,uBAAuB,EAAE,CAAC;AAClD,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nconst HAS_ARIA_ATTRIBUTE_REGEX = /\\[(aria-|role)/g;\n\n/**\n * Patches a CSS selector string to include `data-*` shifting `role` and\n * `aria-*` attributes. Use this with `querySelector()` and `querySelectorAll()`\n * for MWC elements.\n *\n * @example\n * ```ts\n * const agreeCheckbox = document.querySelector(\n *   ariaSelector('md-checkbox[aria-label=\"Agree\"]')\n * );\n * ```\n *\n * @param selector A CSS selector string.\n * @return A CSS selector string that includes `data-*` shifting aria\n *     attributes.\n */\nexport function ariaSelector(selector: string) {\n  if (!HAS_ARIA_ATTRIBUTE_REGEX.test(selector)) {\n    return selector;\n  }\n\n  const selectorWithDataShifted = selector.replaceAll(\n    HAS_ARIA_ATTRIBUTE_REGEX,\n    '[data-$1',\n  );\n  return `${selector},${selectorWithDataShifted}`;\n}\n"]}