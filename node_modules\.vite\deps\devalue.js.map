{"version": 3, "sources": ["../../devalue/src/utils.js", "../../devalue/src/uneval.js", "../../devalue/src/base64.js", "../../devalue/src/constants.js", "../../devalue/src/parse.js", "../../devalue/src/stringify.js"], "sourcesContent": ["/** @type {Record<string, string>} */\nexport const escaped = {\n\t'<': '\\\\u003C',\n\t'\\\\': '\\\\\\\\',\n\t'\\b': '\\\\b',\n\t'\\f': '\\\\f',\n\t'\\n': '\\\\n',\n\t'\\r': '\\\\r',\n\t'\\t': '\\\\t',\n\t'\\u2028': '\\\\u2028',\n\t'\\u2029': '\\\\u2029'\n};\n\nexport class DevalueError extends Error {\n\t/**\n\t * @param {string} message\n\t * @param {string[]} keys\n\t */\n\tconstructor(message, keys) {\n\t\tsuper(message);\n\t\tthis.name = 'DevalueError';\n\t\tthis.path = keys.join('');\n\t}\n}\n\n/** @param {any} thing */\nexport function is_primitive(thing) {\n\treturn Object(thing) !== thing;\n}\n\nconst object_proto_names = /* @__PURE__ */ Object.getOwnPropertyNames(\n\tObject.prototype\n)\n\t.sort()\n\t.join('\\0');\n\n/** @param {any} thing */\nexport function is_plain_object(thing) {\n\tconst proto = Object.getPrototypeOf(thing);\n\n\treturn (\n\t\tproto === Object.prototype ||\n\t\tproto === null ||\n\t\tObject.getOwnPropertyNames(proto).sort().join('\\0') === object_proto_names\n\t);\n}\n\n/** @param {any} thing */\nexport function get_type(thing) {\n\treturn Object.prototype.toString.call(thing).slice(8, -1);\n}\n\n/** @param {string} char */\nfunction get_escaped_char(char) {\n\tswitch (char) {\n\t\tcase '\"':\n\t\t\treturn '\\\\\"';\n\t\tcase '<':\n\t\t\treturn '\\\\u003C';\n\t\tcase '\\\\':\n\t\t\treturn '\\\\\\\\';\n\t\tcase '\\n':\n\t\t\treturn '\\\\n';\n\t\tcase '\\r':\n\t\t\treturn '\\\\r';\n\t\tcase '\\t':\n\t\t\treturn '\\\\t';\n\t\tcase '\\b':\n\t\t\treturn '\\\\b';\n\t\tcase '\\f':\n\t\t\treturn '\\\\f';\n\t\tcase '\\u2028':\n\t\t\treturn '\\\\u2028';\n\t\tcase '\\u2029':\n\t\t\treturn '\\\\u2029';\n\t\tdefault:\n\t\t\treturn char < ' '\n\t\t\t\t? `\\\\u${char.charCodeAt(0).toString(16).padStart(4, '0')}`\n\t\t\t\t: '';\n\t}\n}\n\n/** @param {string} str */\nexport function stringify_string(str) {\n\tlet result = '';\n\tlet last_pos = 0;\n\tconst len = str.length;\n\n\tfor (let i = 0; i < len; i += 1) {\n\t\tconst char = str[i];\n\t\tconst replacement = get_escaped_char(char);\n\t\tif (replacement) {\n\t\t\tresult += str.slice(last_pos, i) + replacement;\n\t\t\tlast_pos = i + 1;\n\t\t}\n\t}\n\n\treturn `\"${last_pos === 0 ? str : result + str.slice(last_pos)}\"`;\n}\n\n/** @param {Record<string | symbol, any>} object */\nexport function enumerable_symbols(object) {\n\treturn Object.getOwnPropertySymbols(object).filter(\n\t\t(symbol) => Object.getOwnPropertyDescriptor(object, symbol).enumerable\n\t);\n}\n\nconst is_identifier = /^[a-zA-Z_$][a-zA-Z_$0-9]*$/;\n\n/** @param {string} key */\nexport function stringify_key(key) {\n\treturn is_identifier.test(key) ? '.' + key : '[' + JSON.stringify(key) + ']';\n}\n", "import {\n\tDevalueError,\n\tenumerable_symbols,\n\tescaped,\n\tget_type,\n\tis_plain_object,\n\tis_primitive,\n\tstringify_key,\n\tstringify_string\n} from './utils.js';\n\nconst chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_$';\nconst unsafe_chars = /[<\\b\\f\\n\\r\\t\\0\\u2028\\u2029]/g;\nconst reserved =\n\t/^(?:do|if|in|for|int|let|new|try|var|byte|case|char|else|enum|goto|long|this|void|with|await|break|catch|class|const|final|float|short|super|throw|while|yield|delete|double|export|import|native|return|switch|throws|typeof|boolean|default|extends|finally|package|private|abstract|continue|debugger|function|volatile|interface|protected|transient|implements|instanceof|synchronized)$/;\n\n/**\n * Turn a value into the JavaScript that creates an equivalent value\n * @param {any} value\n * @param {(value: any) => string | void} [replacer]\n */\nexport function uneval(value, replacer) {\n\tconst counts = new Map();\n\n\t/** @type {string[]} */\n\tconst keys = [];\n\n\tconst custom = new Map();\n\n\t/** @param {any} thing */\n\tfunction walk(thing) {\n\t\tif (typeof thing === 'function') {\n\t\t\tthrow new DevalueError(`Cannot stringify a function`, keys);\n\t\t}\n\n\t\tif (!is_primitive(thing)) {\n\t\t\tif (counts.has(thing)) {\n\t\t\t\tcounts.set(thing, counts.get(thing) + 1);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tcounts.set(thing, 1);\n\n\t\t\tif (replacer) {\n\t\t\t\tconst str = replacer(thing);\n\n\t\t\t\tif (typeof str === 'string') {\n\t\t\t\t\tcustom.set(thing, str);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst type = get_type(thing);\n\n\t\t\tswitch (type) {\n\t\t\t\tcase 'Number':\n\t\t\t\tcase 'BigInt':\n\t\t\t\tcase 'String':\n\t\t\t\tcase 'Boolean':\n\t\t\t\tcase 'Date':\n\t\t\t\tcase 'RegExp':\n\t\t\t\t\treturn;\n\n\t\t\t\tcase 'Array':\n\t\t\t\t\t/** @type {any[]} */ (thing).forEach((value, i) => {\n\t\t\t\t\t\tkeys.push(`[${i}]`);\n\t\t\t\t\t\twalk(value);\n\t\t\t\t\t\tkeys.pop();\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'Set':\n\t\t\t\t\tArray.from(thing).forEach(walk);\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'Map':\n\t\t\t\t\tfor (const [key, value] of thing) {\n\t\t\t\t\t\tkeys.push(\n\t\t\t\t\t\t\t`.get(${is_primitive(key) ? stringify_primitive(key) : '...'})`\n\t\t\t\t\t\t);\n\t\t\t\t\t\twalk(value);\n\t\t\t\t\t\tkeys.pop();\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\t\n\t\t\t\tcase \"Int8Array\":\n\t\t\t\tcase \"Uint8Array\":\n\t\t\t\tcase \"Uint8ClampedArray\":\n\t\t\t\tcase \"Int16Array\":\n\t\t\t\tcase \"Uint16Array\":\n\t\t\t\tcase \"Int32Array\":\n\t\t\t\tcase \"Uint32Array\":\n\t\t\t\tcase \"Float32Array\":\n\t\t\t\tcase \"Float64Array\":\n\t\t\t\tcase \"BigInt64Array\":\n\t\t\t\tcase \"BigUint64Array\":\n\t\t\t\t\treturn;\n\t\t\t\t\n\t\t\t\tcase \"ArrayBuffer\":\n\t\t\t\t\treturn;\n\n\t\t\t\tdefault:\n\t\t\t\t\tif (!is_plain_object(thing)) {\n\t\t\t\t\t\tthrow new DevalueError(\n\t\t\t\t\t\t\t`Cannot stringify arbitrary non-POJOs`,\n\t\t\t\t\t\t\tkeys\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (enumerable_symbols(thing).length > 0) {\n\t\t\t\t\t\tthrow new DevalueError(\n\t\t\t\t\t\t\t`Cannot stringify POJOs with symbolic keys`,\n\t\t\t\t\t\t\tkeys\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\tfor (const key in thing) {\n\t\t\t\t\t\tkeys.push(stringify_key(key));\n\t\t\t\t\t\twalk(thing[key]);\n\t\t\t\t\t\tkeys.pop();\n\t\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\twalk(value);\n\n\tconst names = new Map();\n\n\tArray.from(counts)\n\t\t.filter((entry) => entry[1] > 1)\n\t\t.sort((a, b) => b[1] - a[1])\n\t\t.forEach((entry, i) => {\n\t\t\tnames.set(entry[0], get_name(i));\n\t\t});\n\n\t/**\n\t * @param {any} thing\n\t * @returns {string}\n\t */\n\tfunction stringify(thing) {\n\t\tif (names.has(thing)) {\n\t\t\treturn names.get(thing);\n\t\t}\n\n\t\tif (is_primitive(thing)) {\n\t\t\treturn stringify_primitive(thing);\n\t\t}\n\n\t\tif (custom.has(thing)) {\n\t\t\treturn custom.get(thing);\n\t\t}\n\n\t\tconst type = get_type(thing);\n\n\t\tswitch (type) {\n\t\t\tcase 'Number':\n\t\t\tcase 'String':\n\t\t\tcase 'Boolean':\n\t\t\t\treturn `Object(${stringify(thing.valueOf())})`;\n\n\t\t\tcase 'RegExp':\n\t\t\t\treturn `new RegExp(${stringify_string(thing.source)}, \"${\n\t\t\t\t\tthing.flags\n\t\t\t\t}\")`;\n\n\t\t\tcase 'Date':\n\t\t\t\treturn `new Date(${thing.getTime()})`;\n\n\t\t\tcase 'Array':\n\t\t\t\tconst members = /** @type {any[]} */ (thing).map((v, i) =>\n\t\t\t\t\ti in thing ? stringify(v) : ''\n\t\t\t\t);\n\t\t\t\tconst tail = thing.length === 0 || thing.length - 1 in thing ? '' : ',';\n\t\t\t\treturn `[${members.join(',')}${tail}]`;\n\n\t\t\tcase 'Set':\n\t\t\tcase 'Map':\n\t\t\t\treturn `new ${type}([${Array.from(thing).map(stringify).join(',')}])`;\n\t\t\t\n\t\t\tcase \"Int8Array\":\n\t\t\tcase \"Uint8Array\":\n\t\t\tcase \"Uint8ClampedArray\":\n\t\t\tcase \"Int16Array\":\n\t\t\tcase \"Uint16Array\":\n\t\t\tcase \"Int32Array\":\n\t\t\tcase \"Uint32Array\":\n\t\t\tcase \"Float32Array\":\n\t\t\tcase \"Float64Array\":\n\t\t\tcase \"BigInt64Array\":\n\t\t\tcase \"BigUint64Array\": {\n\t\t\t\t/** @type {import(\"./types.js\").TypedArray} */\n\t\t\t\tconst typedArray = thing;\n\t\t\t\treturn `new ${type}([${typedArray.toString()}])`;\n\t\t\t}\n\t\t\t\t\n\t\t\tcase \"ArrayBuffer\": {\n\t\t\t\tconst ui8 = new Uint8Array(thing);\n\t\t\t\treturn `new Uint8Array([${ui8.toString()}]).buffer`;\n\t\t\t}\n\n\t\t\tdefault:\n\t\t\t\tconst obj = `{${Object.keys(thing)\n\t\t\t\t\t.map((key) => `${safe_key(key)}:${stringify(thing[key])}`)\n\t\t\t\t\t.join(',')}}`;\n\t\t\t\tconst proto = Object.getPrototypeOf(thing);\n\t\t\t\tif (proto === null) {\n\t\t\t\t\treturn Object.keys(thing).length > 0\n\t\t\t\t\t\t? `Object.assign(Object.create(null),${obj})`\n\t\t\t\t\t\t: `Object.create(null)`;\n\t\t\t\t}\n\n\t\t\t\treturn obj;\n\t\t}\n\t}\n\n\tconst str = stringify(value);\n\n\tif (names.size) {\n\t\t/** @type {string[]} */\n\t\tconst params = [];\n\n\t\t/** @type {string[]} */\n\t\tconst statements = [];\n\n\t\t/** @type {string[]} */\n\t\tconst values = [];\n\n\t\tnames.forEach((name, thing) => {\n\t\t\tparams.push(name);\n\n\t\t\tif (custom.has(thing)) {\n\t\t\t\tvalues.push(/** @type {string} */ (custom.get(thing)));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (is_primitive(thing)) {\n\t\t\t\tvalues.push(stringify_primitive(thing));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst type = get_type(thing);\n\n\t\t\tswitch (type) {\n\t\t\t\tcase 'Number':\n\t\t\t\tcase 'String':\n\t\t\t\tcase 'Boolean':\n\t\t\t\t\tvalues.push(`Object(${stringify(thing.valueOf())})`);\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'RegExp':\n\t\t\t\t\tvalues.push(thing.toString());\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'Date':\n\t\t\t\t\tvalues.push(`new Date(${thing.getTime()})`);\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'Array':\n\t\t\t\t\tvalues.push(`Array(${thing.length})`);\n\t\t\t\t\t/** @type {any[]} */ (thing).forEach((v, i) => {\n\t\t\t\t\t\tstatements.push(`${name}[${i}]=${stringify(v)}`);\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'Set':\n\t\t\t\t\tvalues.push(`new Set`);\n\t\t\t\t\tstatements.push(\n\t\t\t\t\t\t`${name}.${Array.from(thing)\n\t\t\t\t\t\t\t.map((v) => `add(${stringify(v)})`)\n\t\t\t\t\t\t\t.join('.')}`\n\t\t\t\t\t);\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'Map':\n\t\t\t\t\tvalues.push(`new Map`);\n\t\t\t\t\tstatements.push(\n\t\t\t\t\t\t`${name}.${Array.from(thing)\n\t\t\t\t\t\t\t.map(([k, v]) => `set(${stringify(k)}, ${stringify(v)})`)\n\t\t\t\t\t\t\t.join('.')}`\n\t\t\t\t\t);\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tvalues.push(\n\t\t\t\t\t\tObject.getPrototypeOf(thing) === null ? 'Object.create(null)' : '{}'\n\t\t\t\t\t);\n\t\t\t\t\tObject.keys(thing).forEach((key) => {\n\t\t\t\t\t\tstatements.push(\n\t\t\t\t\t\t\t`${name}${safe_prop(key)}=${stringify(thing[key])}`\n\t\t\t\t\t\t);\n\t\t\t\t\t});\n\t\t\t}\n\t\t});\n\n\t\tstatements.push(`return ${str}`);\n\n\t\treturn `(function(${params.join(',')}){${statements.join(\n\t\t\t';'\n\t\t)}}(${values.join(',')}))`;\n\t} else {\n\t\treturn str;\n\t}\n}\n\n/** @param {number} num */\nfunction get_name(num) {\n\tlet name = '';\n\n\tdo {\n\t\tname = chars[num % chars.length] + name;\n\t\tnum = ~~(num / chars.length) - 1;\n\t} while (num >= 0);\n\n\treturn reserved.test(name) ? `${name}0` : name;\n}\n\n/** @param {string} c */\nfunction escape_unsafe_char(c) {\n\treturn escaped[c] || c;\n}\n\n/** @param {string} str */\nfunction escape_unsafe_chars(str) {\n\treturn str.replace(unsafe_chars, escape_unsafe_char);\n}\n\n/** @param {string} key */\nfunction safe_key(key) {\n\treturn /^[_$a-zA-Z][_$a-zA-Z0-9]*$/.test(key)\n\t\t? key\n\t\t: escape_unsafe_chars(JSON.stringify(key));\n}\n\n/** @param {string} key */\nfunction safe_prop(key) {\n\treturn /^[_$a-zA-Z][_$a-zA-Z0-9]*$/.test(key)\n\t\t? `.${key}`\n\t\t: `[${escape_unsafe_chars(JSON.stringify(key))}]`;\n}\n\n/** @param {any} thing */\nfunction stringify_primitive(thing) {\n\tif (typeof thing === 'string') return stringify_string(thing);\n\tif (thing === void 0) return 'void 0';\n\tif (thing === 0 && 1 / thing < 0) return '-0';\n\tconst str = String(thing);\n\tif (typeof thing === 'number') return str.replace(/^(-)?0\\./, '$1.');\n\tif (typeof thing === 'bigint') return thing + 'n';\n\treturn str;\n}\n", "/**\n * Base64 Encodes an arraybuffer\n * @param {ArrayBuffer} arraybuffer\n * @returns {string}\n */\nexport function encode64(arraybuffer) {\n  const dv = new DataView(arraybuffer);\n  let binaryString = \"\";\n\n  for (let i = 0; i < arraybuffer.byteLength; i++) {\n    binaryString += String.fromCharCode(dv.getUint8(i));\n  }\n\n  return binaryToAscii(binaryString);\n}\n\n/**\n * Decodes a base64 string into an arraybuffer\n * @param {string} string\n * @returns {ArrayBuffer}\n */\nexport function decode64(string) {\n  const binaryString = asciiToBinary(string);\n  const arraybuffer = new ArrayBuffer(binaryString.length);\n  const dv = new DataView(arraybuffer);\n\n  for (let i = 0; i < arraybuffer.byteLength; i++) {\n    dv.setUint8(i, binaryString.charCodeAt(i));\n  }\n\n  return arraybuffer;\n}\n\nconst KEY_STRING =\n  \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdef<PERSON>ijklmnopqrstuvwxyz0123456789+/\";\n\n/**\n * Substitute for atob since it's deprecated in node.\n * Does not do any input validation.\n *\n * @see https://github.com/jsdom/abab/blob/master/lib/atob.js\n *\n * @param {string} data\n * @returns {string}\n */\nfunction asciiToBinary(data) {\n  if (data.length % 4 === 0) {\n    data = data.replace(/==?$/, \"\");\n  }\n\n  let output = \"\";\n  let buffer = 0;\n  let accumulatedBits = 0;\n\n  for (let i = 0; i < data.length; i++) {\n    buffer <<= 6;\n    buffer |= KEY_STRING.indexOf(data[i]);\n    accumulatedBits += 6;\n    if (accumulatedBits === 24) {\n      output += String.fromCharCode((buffer & 0xff0000) >> 16);\n      output += String.fromCharCode((buffer & 0xff00) >> 8);\n      output += String.fromCharCode(buffer & 0xff);\n      buffer = accumulatedBits = 0;\n    }\n  }\n  if (accumulatedBits === 12) {\n    buffer >>= 4;\n    output += String.fromCharCode(buffer);\n  } else if (accumulatedBits === 18) {\n    buffer >>= 2;\n    output += String.fromCharCode((buffer & 0xff00) >> 8);\n    output += String.fromCharCode(buffer & 0xff);\n  }\n  return output;\n}\n\n/**\n * Substitute for btoa since it's deprecated in node.\n * Does not do any input validation.\n *\n * @see https://github.com/jsdom/abab/blob/master/lib/btoa.js\n *\n * @param {string} str\n * @returns {string}\n */\nfunction binaryToAscii(str) {\n  let out = \"\";\n  for (let i = 0; i < str.length; i += 3) {\n    /** @type {[number, number, number, number]} */\n    const groupsOfSix = [undefined, undefined, undefined, undefined];\n    groupsOfSix[0] = str.charCodeAt(i) >> 2;\n    groupsOfSix[1] = (str.charCodeAt(i) & 0x03) << 4;\n    if (str.length > i + 1) {\n      groupsOfSix[1] |= str.charCodeAt(i + 1) >> 4;\n      groupsOfSix[2] = (str.charCodeAt(i + 1) & 0x0f) << 2;\n    }\n    if (str.length > i + 2) {\n      groupsOfSix[2] |= str.charCodeAt(i + 2) >> 6;\n      groupsOfSix[3] = str.charCodeAt(i + 2) & 0x3f;\n    }\n    for (let j = 0; j < groupsOfSix.length; j++) {\n      if (typeof groupsOfSix[j] === \"undefined\") {\n        out += \"=\";\n      } else {\n        out += KEY_STRING[groupsOfSix[j]];\n      }\n    }\n  }\n  return out;\n}\n", "export const UNDEFINED = -1;\nexport const HOLE = -2;\nexport const NAN = -3;\nexport const POSITIVE_INFINITY = -4;\nexport const NEGATIVE_INFINITY = -5;\nexport const NEGATIVE_ZERO = -6;\n", "import { decode64 } from './base64.js';\nimport {\n\tHOL<PERSON>,\n\tNAN,\n\tNEGATIVE_INFINITY,\n\tNEGATIVE_ZERO,\n\tPOSITIVE_INFINITY,\n\tUNDEFINED\n} from './constants.js';\n\n/**\n * Revive a value serialized with `devalue.stringify`\n * @param {string} serialized\n * @param {Record<string, (value: any) => any>} [revivers]\n */\nexport function parse(serialized, revivers) {\n\treturn unflatten(JSON.parse(serialized), revivers);\n}\n\n/**\n * Revive a value flattened with `devalue.stringify`\n * @param {number | any[]} parsed\n * @param {Record<string, (value: any) => any>} [revivers]\n */\nexport function unflatten(parsed, revivers) {\n\tif (typeof parsed === 'number') return hydrate(parsed, true);\n\n\tif (!Array.isArray(parsed) || parsed.length === 0) {\n\t\tthrow new Error('Invalid input');\n\t}\n\n\tconst values = /** @type {any[]} */ (parsed);\n\n\tconst hydrated = Array(values.length);\n\n\t/**\n\t * @param {number} index\n\t * @returns {any}\n\t */\n\tfunction hydrate(index, standalone = false) {\n\t\tif (index === UNDEFINED) return undefined;\n\t\tif (index === NAN) return NaN;\n\t\tif (index === POSITIVE_INFINITY) return Infinity;\n\t\tif (index === NEGATIVE_INFINITY) return -Infinity;\n\t\tif (index === NEGATIVE_ZERO) return -0;\n\n\t\tif (standalone) throw new Error(`Invalid input`);\n\n\t\tif (index in hydrated) return hydrated[index];\n\n\t\tconst value = values[index];\n\n\t\tif (!value || typeof value !== 'object') {\n\t\t\thydrated[index] = value;\n\t\t} else if (Array.isArray(value)) {\n\t\t\tif (typeof value[0] === 'string') {\n\t\t\t\tconst type = value[0];\n\n\t\t\t\tconst reviver = revivers?.[type];\n\t\t\t\tif (reviver) {\n\t\t\t\t\treturn (hydrated[index] = reviver(hydrate(value[1])));\n\t\t\t\t}\n\n\t\t\t\tswitch (type) {\n\t\t\t\t\tcase 'Date':\n\t\t\t\t\t\thydrated[index] = new Date(value[1]);\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'Set':\n\t\t\t\t\t\tconst set = new Set();\n\t\t\t\t\t\thydrated[index] = set;\n\t\t\t\t\t\tfor (let i = 1; i < value.length; i += 1) {\n\t\t\t\t\t\t\tset.add(hydrate(value[i]));\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'Map':\n\t\t\t\t\t\tconst map = new Map();\n\t\t\t\t\t\thydrated[index] = map;\n\t\t\t\t\t\tfor (let i = 1; i < value.length; i += 2) {\n\t\t\t\t\t\t\tmap.set(hydrate(value[i]), hydrate(value[i + 1]));\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'RegExp':\n\t\t\t\t\t\thydrated[index] = new RegExp(value[1], value[2]);\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'Object':\n\t\t\t\t\t\thydrated[index] = Object(value[1]);\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'BigInt':\n\t\t\t\t\t\thydrated[index] = BigInt(value[1]);\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'null':\n\t\t\t\t\t\tconst obj = Object.create(null);\n\t\t\t\t\t\thydrated[index] = obj;\n\t\t\t\t\t\tfor (let i = 1; i < value.length; i += 2) {\n\t\t\t\t\t\t\tobj[value[i]] = hydrate(value[i + 1]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\n          case \"Int8Array\":\n          case \"Uint8Array\":\n          case \"Uint8ClampedArray\":\n          case \"Int16Array\":\n          case \"Uint16Array\":\n          case \"Int32Array\":\n          case \"Uint32Array\":\n          case \"Float32Array\":\n          case \"Float64Array\":\n          case \"BigInt64Array\":\n          case \"BigUint64Array\": {\n            const TypedArrayConstructor = globalThis[type];\n            const base64 = value[1];\n            const arraybuffer = decode64(base64);\n            const typedArray = new TypedArrayConstructor(arraybuffer);\n            hydrated[index] = typedArray;\n            break;\n          }\n\n          case \"ArrayBuffer\": {\n            const base64 = value[1];\n            const arraybuffer = decode64(base64);\n            hydrated[index] = arraybuffer;\n            break;\n          }\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tthrow new Error(`Unknown type ${type}`);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tconst array = new Array(value.length);\n\t\t\t\thydrated[index] = array;\n\n\t\t\t\tfor (let i = 0; i < value.length; i += 1) {\n\t\t\t\t\tconst n = value[i];\n\t\t\t\t\tif (n === HOLE) continue;\n\n\t\t\t\t\tarray[i] = hydrate(n);\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\t/** @type {Record<string, any>} */\n\t\t\tconst object = {};\n\t\t\thydrated[index] = object;\n\n\t\t\tfor (const key in value) {\n\t\t\t\tconst n = value[key];\n\t\t\t\tobject[key] = hydrate(n);\n\t\t\t}\n\t\t}\n\n\t\treturn hydrated[index];\n\t}\n\n\treturn hydrate(0);\n}\n", "import {\n\tDevalueError,\n\tenumerable_symbols,\n\tget_type,\n\tis_plain_object,\n\tis_primitive,\n\tstringify_key,\n\tstringify_string\n} from './utils.js';\nimport {\n\tHOLE,\n\tNAN,\n\tNEGATIVE_INFINITY,\n\tNEGATIVE_ZERO,\n\tPOSITIVE_INFINITY,\n\tUNDEFINED\n} from './constants.js';\nimport { encode64 } from './base64.js';\n\n/**\n * Turn a value into a JSON string that can be parsed with `devalue.parse`\n * @param {any} value\n * @param {Record<string, (value: any) => any>} [reducers]\n */\nexport function stringify(value, reducers) {\n\t/** @type {any[]} */\n\tconst stringified = [];\n\n\t/** @type {Map<any, number>} */\n\tconst indexes = new Map();\n\n\t/** @type {Array<{ key: string, fn: (value: any) => any }>} */\n\tconst custom = [];\n\tif (reducers) {\n\t\tfor (const key of Object.getOwnPropertyNames(reducers)) {\n\t\t\tcustom.push({ key, fn: reducers[key] });\n\t\t}\n\t}\n\n\t/** @type {string[]} */\n\tconst keys = [];\n\n\tlet p = 0;\n\n\t/** @param {any} thing */\n\tfunction flatten(thing) {\n\t\tif (typeof thing === 'function') {\n\t\t\tthrow new DevalueError(`Cannot stringify a function`, keys);\n\t\t}\n\n\t\tif (indexes.has(thing)) return indexes.get(thing);\n\n\t\tif (thing === undefined) return UNDEFINED;\n\t\tif (Number.isNaN(thing)) return NAN;\n\t\tif (thing === Infinity) return POSITIVE_INFINITY;\n\t\tif (thing === -Infinity) return NEGATIVE_INFINITY;\n\t\tif (thing === 0 && 1 / thing < 0) return NEGATIVE_ZERO;\n\n\t\tconst index = p++;\n\t\tindexes.set(thing, index);\n\n\t\tfor (const { key, fn } of custom) {\n\t\t\tconst value = fn(thing);\n\t\t\tif (value) {\n\t\t\t\tstringified[index] = `[\"${key}\",${flatten(value)}]`;\n\t\t\t\treturn index;\n\t\t\t}\n\t\t}\n\n\t\tlet str = '';\n\n\t\tif (is_primitive(thing)) {\n\t\t\tstr = stringify_primitive(thing);\n\t\t} else {\n\t\t\tconst type = get_type(thing);\n\n\t\t\tswitch (type) {\n\t\t\t\tcase 'Number':\n\t\t\t\tcase 'String':\n\t\t\t\tcase 'Boolean':\n\t\t\t\t\tstr = `[\"Object\",${stringify_primitive(thing)}]`;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'BigInt':\n\t\t\t\t\tstr = `[\"BigInt\",${thing}]`;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'Date':\n\t\t\t\t\tconst valid = !isNaN(thing.getDate());\n\t\t\t\t\tstr = `[\"Date\",\"${valid ? thing.toISOString() : ''}\"]`;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'RegExp':\n\t\t\t\t\tconst { source, flags } = thing;\n\t\t\t\t\tstr = flags\n\t\t\t\t\t\t? `[\"RegExp\",${stringify_string(source)},\"${flags}\"]`\n\t\t\t\t\t\t: `[\"RegExp\",${stringify_string(source)}]`;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'Array':\n\t\t\t\t\tstr = '[';\n\n\t\t\t\t\tfor (let i = 0; i < thing.length; i += 1) {\n\t\t\t\t\t\tif (i > 0) str += ',';\n\n\t\t\t\t\t\tif (i in thing) {\n\t\t\t\t\t\t\tkeys.push(`[${i}]`);\n\t\t\t\t\t\t\tstr += flatten(thing[i]);\n\t\t\t\t\t\t\tkeys.pop();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tstr += HOLE;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tstr += ']';\n\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'Set':\n\t\t\t\t\tstr = '[\"Set\"';\n\n\t\t\t\t\tfor (const value of thing) {\n\t\t\t\t\t\tstr += `,${flatten(value)}`;\n\t\t\t\t\t}\n\n\t\t\t\t\tstr += ']';\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'Map':\n\t\t\t\t\tstr = '[\"Map\"';\n\n\t\t\t\t\tfor (const [key, value] of thing) {\n\t\t\t\t\t\tkeys.push(\n\t\t\t\t\t\t\t`.get(${is_primitive(key) ? stringify_primitive(key) : '...'})`\n\t\t\t\t\t\t);\n\t\t\t\t\t\tstr += `,${flatten(key)},${flatten(value)}`;\n\t\t\t\t\t\tkeys.pop();\n\t\t\t\t\t}\n\n\t\t\t\t\tstr += ']';\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase \"Int8Array\":\n\t\t\t\tcase \"Uint8Array\":\n\t\t\t\tcase \"Uint8ClampedArray\":\n\t\t\t\tcase \"Int16Array\":\n\t\t\t\tcase \"Uint16Array\":\n\t\t\t\tcase \"Int32Array\":\n\t\t\t\tcase \"Uint32Array\":\n\t\t\t\tcase \"Float32Array\":\n\t\t\t\tcase \"Float64Array\":\n\t\t\t\tcase \"BigInt64Array\":\n\t\t\t\tcase \"BigUint64Array\": {\n\t\t\t\t\t/** @type {import(\"./types.js\").TypedArray} */\n\t\t\t\t\tconst typedArray = thing;\n\t\t\t\t\tconst base64 = encode64(typedArray.buffer);\n\t\t\t\t\tstr = '[\"' + type + '\",\"' + base64 + '\"]';\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\tcase \"ArrayBuffer\": {\n\t\t\t\t\t/** @type {ArrayBuffer} */\n\t\t\t\t\tconst arraybuffer = thing;\n\t\t\t\t\tconst base64 = encode64(arraybuffer);\n\t\t\t\t\t\n\t\t\t\t\tstr = `[\"ArrayBuffer\",\"${base64}\"]`;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tdefault:\n\t\t\t\t\tif (!is_plain_object(thing)) {\n\t\t\t\t\t\tthrow new DevalueError(\n\t\t\t\t\t\t\t`Cannot stringify arbitrary non-POJOs`,\n\t\t\t\t\t\t\tkeys\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (enumerable_symbols(thing).length > 0) {\n\t\t\t\t\t\tthrow new DevalueError(\n\t\t\t\t\t\t\t`Cannot stringify POJOs with symbolic keys`,\n\t\t\t\t\t\t\tkeys\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (Object.getPrototypeOf(thing) === null) {\n\t\t\t\t\t\tstr = '[\"null\"';\n\t\t\t\t\t\tfor (const key in thing) {\n\t\t\t\t\t\t\tkeys.push(stringify_key(key));\n\t\t\t\t\t\t\tstr += `,${stringify_string(key)},${flatten(thing[key])}`;\n\t\t\t\t\t\t\tkeys.pop();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tstr += ']';\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstr = '{';\n\t\t\t\t\t\tlet started = false;\n\t\t\t\t\t\tfor (const key in thing) {\n\t\t\t\t\t\t\tif (started) str += ',';\n\t\t\t\t\t\t\tstarted = true;\n\t\t\t\t\t\t\tkeys.push(stringify_key(key));\n\t\t\t\t\t\t\tstr += `${stringify_string(key)}:${flatten(thing[key])}`;\n\t\t\t\t\t\t\tkeys.pop();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tstr += '}';\n\t\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tstringified[index] = str;\n\t\treturn index;\n\t}\n\n\tconst index = flatten(value);\n\n\t// special case — value is represented as a negative index\n\tif (index < 0) return `${index}`;\n\n\treturn `[${stringified.join(',')}]`;\n}\n\n/**\n * @param {any} thing\n * @returns {string}\n */\nfunction stringify_primitive(thing) {\n\tconst type = typeof thing;\n\tif (type === 'string') return stringify_string(thing);\n\tif (thing instanceof String) return stringify_string(thing.toString());\n\tif (thing === void 0) return UNDEFINED.toString();\n\tif (thing === 0 && 1 / thing < 0) return NEGATIVE_ZERO.toString();\n\tif (type === 'bigint') return `[\"BigInt\",\"${thing}\"]`;\n\treturn String(thing);\n}\n"], "mappings": ";;;AACO,IAAM,UAAU;AAAA,EACtB,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AACX;AAEO,IAAM,eAAN,cAA2B,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvC,YAAY,SAAS,MAAM;AAC1B,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,OAAO,KAAK,KAAK,EAAE;AAAA,EACzB;AACD;AAGO,SAAS,aAAa,OAAO;AACnC,SAAO,OAAO,KAAK,MAAM;AAC1B;AAEA,IAAM,qBAAqC,OAAO;AAAA,EACjD,OAAO;AACR,EACE,KAAK,EACL,KAAK,IAAI;AAGJ,SAAS,gBAAgB,OAAO;AACtC,QAAM,QAAQ,OAAO,eAAe,KAAK;AAEzC,SACC,UAAU,OAAO,aACjB,UAAU,QACV,OAAO,oBAAoB,KAAK,EAAE,KAAK,EAAE,KAAK,IAAI,MAAM;AAE1D;AAGO,SAAS,SAAS,OAAO;AAC/B,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE;AACzD;AAGA,SAAS,iBAAiB,MAAM;AAC/B,UAAQ,MAAM;AAAA,IACb,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR;AACC,aAAO,OAAO,MACX,MAAM,KAAK,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,KACtD;AAAA,EACL;AACD;AAGO,SAAS,iBAAiB,KAAK;AACrC,MAAI,SAAS;AACb,MAAI,WAAW;AACf,QAAM,MAAM,IAAI;AAEhB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAChC,UAAM,OAAO,IAAI,CAAC;AAClB,UAAM,cAAc,iBAAiB,IAAI;AACzC,QAAI,aAAa;AAChB,gBAAU,IAAI,MAAM,UAAU,CAAC,IAAI;AACnC,iBAAW,IAAI;AAAA,IAChB;AAAA,EACD;AAEA,SAAO,IAAI,aAAa,IAAI,MAAM,SAAS,IAAI,MAAM,QAAQ,CAAC;AAC/D;AAGO,SAAS,mBAAmB,QAAQ;AAC1C,SAAO,OAAO,sBAAsB,MAAM,EAAE;AAAA,IAC3C,CAAC,WAAW,OAAO,yBAAyB,QAAQ,MAAM,EAAE;AAAA,EAC7D;AACD;AAEA,IAAM,gBAAgB;AAGf,SAAS,cAAc,KAAK;AAClC,SAAO,cAAc,KAAK,GAAG,IAAI,MAAM,MAAM,MAAM,KAAK,UAAU,GAAG,IAAI;AAC1E;;;ACrGA,IAAM,QAAQ;AACd,IAAM,eAAe;AACrB,IAAM,WACL;AAOM,SAAS,OAAO,OAAO,UAAU;AACvC,QAAM,SAAS,oBAAI,IAAI;AAGvB,QAAM,OAAO,CAAC;AAEd,QAAM,SAAS,oBAAI,IAAI;AAGvB,WAAS,KAAK,OAAO;AACpB,QAAI,OAAO,UAAU,YAAY;AAChC,YAAM,IAAI,aAAa,+BAA+B,IAAI;AAAA,IAC3D;AAEA,QAAI,CAAC,aAAa,KAAK,GAAG;AACzB,UAAI,OAAO,IAAI,KAAK,GAAG;AACtB,eAAO,IAAI,OAAO,OAAO,IAAI,KAAK,IAAI,CAAC;AACvC;AAAA,MACD;AAEA,aAAO,IAAI,OAAO,CAAC;AAEnB,UAAI,UAAU;AACb,cAAMA,OAAM,SAAS,KAAK;AAE1B,YAAI,OAAOA,SAAQ,UAAU;AAC5B,iBAAO,IAAI,OAAOA,IAAG;AACrB;AAAA,QACD;AAAA,MACD;AAEA,YAAM,OAAO,SAAS,KAAK;AAE3B,cAAQ,MAAM;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACJ;AAAA,QAED,KAAK;AACiB,UAAC,MAAO,QAAQ,CAACC,QAAO,MAAM;AAClD,iBAAK,KAAK,IAAI,CAAC,GAAG;AAClB,iBAAKA,MAAK;AACV,iBAAK,IAAI;AAAA,UACV,CAAC;AACD;AAAA,QAED,KAAK;AACJ,gBAAM,KAAK,KAAK,EAAE,QAAQ,IAAI;AAC9B;AAAA,QAED,KAAK;AACJ,qBAAW,CAAC,KAAKA,MAAK,KAAK,OAAO;AACjC,iBAAK;AAAA,cACJ,QAAQ,aAAa,GAAG,IAAI,oBAAoB,GAAG,IAAI,KAAK;AAAA,YAC7D;AACA,iBAAKA,MAAK;AACV,iBAAK,IAAI;AAAA,UACV;AACA;AAAA,QAED,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACJ;AAAA,QAED,KAAK;AACJ;AAAA,QAED;AACC,cAAI,CAAC,gBAAgB,KAAK,GAAG;AAC5B,kBAAM,IAAI;AAAA,cACT;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAEA,cAAI,mBAAmB,KAAK,EAAE,SAAS,GAAG;AACzC,kBAAM,IAAI;AAAA,cACT;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAEA,qBAAW,OAAO,OAAO;AACxB,iBAAK,KAAK,cAAc,GAAG,CAAC;AAC5B,iBAAK,MAAM,GAAG,CAAC;AACf,iBAAK,IAAI;AAAA,UACV;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAEA,OAAK,KAAK;AAEV,QAAM,QAAQ,oBAAI,IAAI;AAEtB,QAAM,KAAK,MAAM,EACf,OAAO,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,EAC9B,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAC1B,QAAQ,CAAC,OAAO,MAAM;AACtB,UAAM,IAAI,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,EAChC,CAAC;AAMF,WAASC,WAAU,OAAO;AACzB,QAAI,MAAM,IAAI,KAAK,GAAG;AACrB,aAAO,MAAM,IAAI,KAAK;AAAA,IACvB;AAEA,QAAI,aAAa,KAAK,GAAG;AACxB,aAAO,oBAAoB,KAAK;AAAA,IACjC;AAEA,QAAI,OAAO,IAAI,KAAK,GAAG;AACtB,aAAO,OAAO,IAAI,KAAK;AAAA,IACxB;AAEA,UAAM,OAAO,SAAS,KAAK;AAE3B,YAAQ,MAAM;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACJ,eAAO,UAAUA,WAAU,MAAM,QAAQ,CAAC,CAAC;AAAA,MAE5C,KAAK;AACJ,eAAO,cAAc,iBAAiB,MAAM,MAAM,CAAC,MAClD,MAAM,KACP;AAAA,MAED,KAAK;AACJ,eAAO,YAAY,MAAM,QAAQ,CAAC;AAAA,MAEnC,KAAK;AACJ,cAAM;AAAA;AAAA,UAAgC,MAAO;AAAA,YAAI,CAAC,GAAG,MACpD,KAAK,QAAQA,WAAU,CAAC,IAAI;AAAA,UAC7B;AAAA;AACA,cAAM,OAAO,MAAM,WAAW,KAAK,MAAM,SAAS,KAAK,QAAQ,KAAK;AACpE,eAAO,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,IAAI;AAAA,MAEpC,KAAK;AAAA,MACL,KAAK;AACJ,eAAO,OAAO,IAAI,KAAK,MAAM,KAAK,KAAK,EAAE,IAAIA,UAAS,EAAE,KAAK,GAAG,CAAC;AAAA,MAElE,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,kBAAkB;AAEtB,cAAM,aAAa;AACnB,eAAO,OAAO,IAAI,KAAK,WAAW,SAAS,CAAC;AAAA,MAC7C;AAAA,MAEA,KAAK,eAAe;AACnB,cAAM,MAAM,IAAI,WAAW,KAAK;AAChC,eAAO,mBAAmB,IAAI,SAAS,CAAC;AAAA,MACzC;AAAA,MAEA;AACC,cAAM,MAAM,IAAI,OAAO,KAAK,KAAK,EAC/B,IAAI,CAAC,QAAQ,GAAG,SAAS,GAAG,CAAC,IAAIA,WAAU,MAAM,GAAG,CAAC,CAAC,EAAE,EACxD,KAAK,GAAG,CAAC;AACX,cAAM,QAAQ,OAAO,eAAe,KAAK;AACzC,YAAI,UAAU,MAAM;AACnB,iBAAO,OAAO,KAAK,KAAK,EAAE,SAAS,IAChC,qCAAqC,GAAG,MACxC;AAAA,QACJ;AAEA,eAAO;AAAA,IACT;AAAA,EACD;AAEA,QAAM,MAAMA,WAAU,KAAK;AAE3B,MAAI,MAAM,MAAM;AAEf,UAAM,SAAS,CAAC;AAGhB,UAAM,aAAa,CAAC;AAGpB,UAAM,SAAS,CAAC;AAEhB,UAAM,QAAQ,CAAC,MAAM,UAAU;AAC9B,aAAO,KAAK,IAAI;AAEhB,UAAI,OAAO,IAAI,KAAK,GAAG;AACtB,eAAO;AAAA;AAAA,UAA4B,OAAO,IAAI,KAAK;AAAA,QAAE;AACrD;AAAA,MACD;AAEA,UAAI,aAAa,KAAK,GAAG;AACxB,eAAO,KAAK,oBAAoB,KAAK,CAAC;AACtC;AAAA,MACD;AAEA,YAAM,OAAO,SAAS,KAAK;AAE3B,cAAQ,MAAM;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACJ,iBAAO,KAAK,UAAUA,WAAU,MAAM,QAAQ,CAAC,CAAC,GAAG;AACnD;AAAA,QAED,KAAK;AACJ,iBAAO,KAAK,MAAM,SAAS,CAAC;AAC5B;AAAA,QAED,KAAK;AACJ,iBAAO,KAAK,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC1C;AAAA,QAED,KAAK;AACJ,iBAAO,KAAK,SAAS,MAAM,MAAM,GAAG;AACf,UAAC,MAAO,QAAQ,CAAC,GAAG,MAAM;AAC9C,uBAAW,KAAK,GAAG,IAAI,IAAI,CAAC,KAAKA,WAAU,CAAC,CAAC,EAAE;AAAA,UAChD,CAAC;AACD;AAAA,QAED,KAAK;AACJ,iBAAO,KAAK,SAAS;AACrB,qBAAW;AAAA,YACV,GAAG,IAAI,IAAI,MAAM,KAAK,KAAK,EACzB,IAAI,CAAC,MAAM,OAAOA,WAAU,CAAC,CAAC,GAAG,EACjC,KAAK,GAAG,CAAC;AAAA,UACZ;AACA;AAAA,QAED,KAAK;AACJ,iBAAO,KAAK,SAAS;AACrB,qBAAW;AAAA,YACV,GAAG,IAAI,IAAI,MAAM,KAAK,KAAK,EACzB,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,OAAOA,WAAU,CAAC,CAAC,KAAKA,WAAU,CAAC,CAAC,GAAG,EACvD,KAAK,GAAG,CAAC;AAAA,UACZ;AACA;AAAA,QAED;AACC,iBAAO;AAAA,YACN,OAAO,eAAe,KAAK,MAAM,OAAO,wBAAwB;AAAA,UACjE;AACA,iBAAO,KAAK,KAAK,EAAE,QAAQ,CAAC,QAAQ;AACnC,uBAAW;AAAA,cACV,GAAG,IAAI,GAAG,UAAU,GAAG,CAAC,IAAIA,WAAU,MAAM,GAAG,CAAC,CAAC;AAAA,YAClD;AAAA,UACD,CAAC;AAAA,MACH;AAAA,IACD,CAAC;AAED,eAAW,KAAK,UAAU,GAAG,EAAE;AAE/B,WAAO,aAAa,OAAO,KAAK,GAAG,CAAC,KAAK,WAAW;AAAA,MACnD;AAAA,IACD,CAAC,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,EACvB,OAAO;AACN,WAAO;AAAA,EACR;AACD;AAGA,SAAS,SAAS,KAAK;AACtB,MAAI,OAAO;AAEX,KAAG;AACF,WAAO,MAAM,MAAM,MAAM,MAAM,IAAI;AACnC,UAAM,CAAC,EAAE,MAAM,MAAM,UAAU;AAAA,EAChC,SAAS,OAAO;AAEhB,SAAO,SAAS,KAAK,IAAI,IAAI,GAAG,IAAI,MAAM;AAC3C;AAGA,SAAS,mBAAmB,GAAG;AAC9B,SAAO,QAAQ,CAAC,KAAK;AACtB;AAGA,SAAS,oBAAoB,KAAK;AACjC,SAAO,IAAI,QAAQ,cAAc,kBAAkB;AACpD;AAGA,SAAS,SAAS,KAAK;AACtB,SAAO,6BAA6B,KAAK,GAAG,IACzC,MACA,oBAAoB,KAAK,UAAU,GAAG,CAAC;AAC3C;AAGA,SAAS,UAAU,KAAK;AACvB,SAAO,6BAA6B,KAAK,GAAG,IACzC,IAAI,GAAG,KACP,IAAI,oBAAoB,KAAK,UAAU,GAAG,CAAC,CAAC;AAChD;AAGA,SAAS,oBAAoB,OAAO;AACnC,MAAI,OAAO,UAAU,SAAU,QAAO,iBAAiB,KAAK;AAC5D,MAAI,UAAU,OAAQ,QAAO;AAC7B,MAAI,UAAU,KAAK,IAAI,QAAQ,EAAG,QAAO;AACzC,QAAM,MAAM,OAAO,KAAK;AACxB,MAAI,OAAO,UAAU,SAAU,QAAO,IAAI,QAAQ,YAAY,KAAK;AACnE,MAAI,OAAO,UAAU,SAAU,QAAO,QAAQ;AAC9C,SAAO;AACR;;;ACzVO,SAAS,SAAS,aAAa;AACpC,QAAM,KAAK,IAAI,SAAS,WAAW;AACnC,MAAI,eAAe;AAEnB,WAAS,IAAI,GAAG,IAAI,YAAY,YAAY,KAAK;AAC/C,oBAAgB,OAAO,aAAa,GAAG,SAAS,CAAC,CAAC;AAAA,EACpD;AAEA,SAAO,cAAc,YAAY;AACnC;AAOO,SAAS,SAAS,QAAQ;AAC/B,QAAM,eAAe,cAAc,MAAM;AACzC,QAAM,cAAc,IAAI,YAAY,aAAa,MAAM;AACvD,QAAM,KAAK,IAAI,SAAS,WAAW;AAEnC,WAAS,IAAI,GAAG,IAAI,YAAY,YAAY,KAAK;AAC/C,OAAG,SAAS,GAAG,aAAa,WAAW,CAAC,CAAC;AAAA,EAC3C;AAEA,SAAO;AACT;AAEA,IAAM,aACJ;AAWF,SAAS,cAAc,MAAM;AAC3B,MAAI,KAAK,SAAS,MAAM,GAAG;AACzB,WAAO,KAAK,QAAQ,QAAQ,EAAE;AAAA,EAChC;AAEA,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,kBAAkB;AAEtB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,eAAW;AACX,cAAU,WAAW,QAAQ,KAAK,CAAC,CAAC;AACpC,uBAAmB;AACnB,QAAI,oBAAoB,IAAI;AAC1B,gBAAU,OAAO,cAAc,SAAS,aAAa,EAAE;AACvD,gBAAU,OAAO,cAAc,SAAS,UAAW,CAAC;AACpD,gBAAU,OAAO,aAAa,SAAS,GAAI;AAC3C,eAAS,kBAAkB;AAAA,IAC7B;AAAA,EACF;AACA,MAAI,oBAAoB,IAAI;AAC1B,eAAW;AACX,cAAU,OAAO,aAAa,MAAM;AAAA,EACtC,WAAW,oBAAoB,IAAI;AACjC,eAAW;AACX,cAAU,OAAO,cAAc,SAAS,UAAW,CAAC;AACpD,cAAU,OAAO,aAAa,SAAS,GAAI;AAAA,EAC7C;AACA,SAAO;AACT;AAWA,SAAS,cAAc,KAAK;AAC1B,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AAEtC,UAAM,cAAc,CAAC,QAAW,QAAW,QAAW,MAAS;AAC/D,gBAAY,CAAC,IAAI,IAAI,WAAW,CAAC,KAAK;AACtC,gBAAY,CAAC,KAAK,IAAI,WAAW,CAAC,IAAI,MAAS;AAC/C,QAAI,IAAI,SAAS,IAAI,GAAG;AACtB,kBAAY,CAAC,KAAK,IAAI,WAAW,IAAI,CAAC,KAAK;AAC3C,kBAAY,CAAC,KAAK,IAAI,WAAW,IAAI,CAAC,IAAI,OAAS;AAAA,IACrD;AACA,QAAI,IAAI,SAAS,IAAI,GAAG;AACtB,kBAAY,CAAC,KAAK,IAAI,WAAW,IAAI,CAAC,KAAK;AAC3C,kBAAY,CAAC,IAAI,IAAI,WAAW,IAAI,CAAC,IAAI;AAAA,IAC3C;AACA,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,UAAI,OAAO,YAAY,CAAC,MAAM,aAAa;AACzC,eAAO;AAAA,MACT,OAAO;AACL,eAAO,WAAW,YAAY,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;AC7GO,IAAM,YAAY;AAClB,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,oBAAoB;AAC1B,IAAM,oBAAoB;AAC1B,IAAM,gBAAgB;;;ACUtB,SAAS,MAAM,YAAY,UAAU;AAC3C,SAAO,UAAU,KAAK,MAAM,UAAU,GAAG,QAAQ;AAClD;AAOO,SAAS,UAAU,QAAQ,UAAU;AAC3C,MAAI,OAAO,WAAW,SAAU,QAAO,QAAQ,QAAQ,IAAI;AAE3D,MAAI,CAAC,MAAM,QAAQ,MAAM,KAAK,OAAO,WAAW,GAAG;AAClD,UAAM,IAAI,MAAM,eAAe;AAAA,EAChC;AAEA,QAAM;AAAA;AAAA,IAA+B;AAAA;AAErC,QAAM,WAAW,MAAM,OAAO,MAAM;AAMpC,WAAS,QAAQ,OAAO,aAAa,OAAO;AAC3C,QAAI,UAAU,UAAW,QAAO;AAChC,QAAI,UAAU,IAAK,QAAO;AAC1B,QAAI,UAAU,kBAAmB,QAAO;AACxC,QAAI,UAAU,kBAAmB,QAAO;AACxC,QAAI,UAAU,cAAe,QAAO;AAEpC,QAAI,WAAY,OAAM,IAAI,MAAM,eAAe;AAE/C,QAAI,SAAS,SAAU,QAAO,SAAS,KAAK;AAE5C,UAAM,QAAQ,OAAO,KAAK;AAE1B,QAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACxC,eAAS,KAAK,IAAI;AAAA,IACnB,WAAW,MAAM,QAAQ,KAAK,GAAG;AAChC,UAAI,OAAO,MAAM,CAAC,MAAM,UAAU;AACjC,cAAM,OAAO,MAAM,CAAC;AAEpB,cAAM,UAAU,qCAAW;AAC3B,YAAI,SAAS;AACZ,iBAAQ,SAAS,KAAK,IAAI,QAAQ,QAAQ,MAAM,CAAC,CAAC,CAAC;AAAA,QACpD;AAEA,gBAAQ,MAAM;AAAA,UACb,KAAK;AACJ,qBAAS,KAAK,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC;AACnC;AAAA,UAED,KAAK;AACJ,kBAAM,MAAM,oBAAI,IAAI;AACpB,qBAAS,KAAK,IAAI;AAClB,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACzC,kBAAI,IAAI,QAAQ,MAAM,CAAC,CAAC,CAAC;AAAA,YAC1B;AACA;AAAA,UAED,KAAK;AACJ,kBAAM,MAAM,oBAAI,IAAI;AACpB,qBAAS,KAAK,IAAI;AAClB,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACzC,kBAAI,IAAI,QAAQ,MAAM,CAAC,CAAC,GAAG,QAAQ,MAAM,IAAI,CAAC,CAAC,CAAC;AAAA,YACjD;AACA;AAAA,UAED,KAAK;AACJ,qBAAS,KAAK,IAAI,IAAI,OAAO,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAC/C;AAAA,UAED,KAAK;AACJ,qBAAS,KAAK,IAAI,OAAO,MAAM,CAAC,CAAC;AACjC;AAAA,UAED,KAAK;AACJ,qBAAS,KAAK,IAAI,OAAO,MAAM,CAAC,CAAC;AACjC;AAAA,UAED,KAAK;AACJ,kBAAM,MAAM,uBAAO,OAAO,IAAI;AAC9B,qBAAS,KAAK,IAAI;AAClB,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACzC,kBAAI,MAAM,CAAC,CAAC,IAAI,QAAQ,MAAM,IAAI,CAAC,CAAC;AAAA,YACrC;AACA;AAAA,UAEI,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,kBAAkB;AACrB,kBAAM,wBAAwB,WAAW,IAAI;AAC7C,kBAAM,SAAS,MAAM,CAAC;AACtB,kBAAM,cAAc,SAAS,MAAM;AACnC,kBAAM,aAAa,IAAI,sBAAsB,WAAW;AACxD,qBAAS,KAAK,IAAI;AAClB;AAAA,UACF;AAAA,UAEA,KAAK,eAAe;AAClB,kBAAM,SAAS,MAAM,CAAC;AACtB,kBAAM,cAAc,SAAS,MAAM;AACnC,qBAAS,KAAK,IAAI;AAClB;AAAA,UACF;AAAA,UAEL;AACC,kBAAM,IAAI,MAAM,gBAAgB,IAAI,EAAE;AAAA,QACxC;AAAA,MACD,OAAO;AACN,cAAM,QAAQ,IAAI,MAAM,MAAM,MAAM;AACpC,iBAAS,KAAK,IAAI;AAElB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACzC,gBAAM,IAAI,MAAM,CAAC;AACjB,cAAI,MAAM,KAAM;AAEhB,gBAAM,CAAC,IAAI,QAAQ,CAAC;AAAA,QACrB;AAAA,MACD;AAAA,IACD,OAAO;AAEN,YAAM,SAAS,CAAC;AAChB,eAAS,KAAK,IAAI;AAElB,iBAAW,OAAO,OAAO;AACxB,cAAM,IAAI,MAAM,GAAG;AACnB,eAAO,GAAG,IAAI,QAAQ,CAAC;AAAA,MACxB;AAAA,IACD;AAEA,WAAO,SAAS,KAAK;AAAA,EACtB;AAEA,SAAO,QAAQ,CAAC;AACjB;;;ACvIO,SAAS,UAAU,OAAO,UAAU;AAE1C,QAAM,cAAc,CAAC;AAGrB,QAAM,UAAU,oBAAI,IAAI;AAGxB,QAAM,SAAS,CAAC;AAChB,MAAI,UAAU;AACb,eAAW,OAAO,OAAO,oBAAoB,QAAQ,GAAG;AACvD,aAAO,KAAK,EAAE,KAAK,IAAI,SAAS,GAAG,EAAE,CAAC;AAAA,IACvC;AAAA,EACD;AAGA,QAAM,OAAO,CAAC;AAEd,MAAI,IAAI;AAGR,WAAS,QAAQ,OAAO;AACvB,QAAI,OAAO,UAAU,YAAY;AAChC,YAAM,IAAI,aAAa,+BAA+B,IAAI;AAAA,IAC3D;AAEA,QAAI,QAAQ,IAAI,KAAK,EAAG,QAAO,QAAQ,IAAI,KAAK;AAEhD,QAAI,UAAU,OAAW,QAAO;AAChC,QAAI,OAAO,MAAM,KAAK,EAAG,QAAO;AAChC,QAAI,UAAU,SAAU,QAAO;AAC/B,QAAI,UAAU,UAAW,QAAO;AAChC,QAAI,UAAU,KAAK,IAAI,QAAQ,EAAG,QAAO;AAEzC,UAAMC,SAAQ;AACd,YAAQ,IAAI,OAAOA,MAAK;AAExB,eAAW,EAAE,KAAK,GAAG,KAAK,QAAQ;AACjC,YAAMC,SAAQ,GAAG,KAAK;AACtB,UAAIA,QAAO;AACV,oBAAYD,MAAK,IAAI,KAAK,GAAG,KAAK,QAAQC,MAAK,CAAC;AAChD,eAAOD;AAAA,MACR;AAAA,IACD;AAEA,QAAI,MAAM;AAEV,QAAI,aAAa,KAAK,GAAG;AACxB,YAAME,qBAAoB,KAAK;AAAA,IAChC,OAAO;AACN,YAAM,OAAO,SAAS,KAAK;AAE3B,cAAQ,MAAM;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACJ,gBAAM,aAAaA,qBAAoB,KAAK,CAAC;AAC7C;AAAA,QAED,KAAK;AACJ,gBAAM,aAAa,KAAK;AACxB;AAAA,QAED,KAAK;AACJ,gBAAM,QAAQ,CAAC,MAAM,MAAM,QAAQ,CAAC;AACpC,gBAAM,YAAY,QAAQ,MAAM,YAAY,IAAI,EAAE;AAClD;AAAA,QAED,KAAK;AACJ,gBAAM,EAAE,QAAQ,MAAM,IAAI;AAC1B,gBAAM,QACH,aAAa,iBAAiB,MAAM,CAAC,KAAK,KAAK,OAC/C,aAAa,iBAAiB,MAAM,CAAC;AACxC;AAAA,QAED,KAAK;AACJ,gBAAM;AAEN,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACzC,gBAAI,IAAI,EAAG,QAAO;AAElB,gBAAI,KAAK,OAAO;AACf,mBAAK,KAAK,IAAI,CAAC,GAAG;AAClB,qBAAO,QAAQ,MAAM,CAAC,CAAC;AACvB,mBAAK,IAAI;AAAA,YACV,OAAO;AACN,qBAAO;AAAA,YACR;AAAA,UACD;AAEA,iBAAO;AAEP;AAAA,QAED,KAAK;AACJ,gBAAM;AAEN,qBAAWD,UAAS,OAAO;AAC1B,mBAAO,IAAI,QAAQA,MAAK,CAAC;AAAA,UAC1B;AAEA,iBAAO;AACP;AAAA,QAED,KAAK;AACJ,gBAAM;AAEN,qBAAW,CAAC,KAAKA,MAAK,KAAK,OAAO;AACjC,iBAAK;AAAA,cACJ,QAAQ,aAAa,GAAG,IAAIC,qBAAoB,GAAG,IAAI,KAAK;AAAA,YAC7D;AACA,mBAAO,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQD,MAAK,CAAC;AACzC,iBAAK,IAAI;AAAA,UACV;AAEA,iBAAO;AACP;AAAA,QAED,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,kBAAkB;AAEtB,gBAAM,aAAa;AACnB,gBAAM,SAAS,SAAS,WAAW,MAAM;AACzC,gBAAM,OAAO,OAAO,QAAQ,SAAS;AACrC;AAAA,QACD;AAAA,QAEA,KAAK,eAAe;AAEnB,gBAAM,cAAc;AACpB,gBAAM,SAAS,SAAS,WAAW;AAEnC,gBAAM,mBAAmB,MAAM;AAC/B;AAAA,QACD;AAAA,QAEA;AACC,cAAI,CAAC,gBAAgB,KAAK,GAAG;AAC5B,kBAAM,IAAI;AAAA,cACT;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAEA,cAAI,mBAAmB,KAAK,EAAE,SAAS,GAAG;AACzC,kBAAM,IAAI;AAAA,cACT;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAEA,cAAI,OAAO,eAAe,KAAK,MAAM,MAAM;AAC1C,kBAAM;AACN,uBAAW,OAAO,OAAO;AACxB,mBAAK,KAAK,cAAc,GAAG,CAAC;AAC5B,qBAAO,IAAI,iBAAiB,GAAG,CAAC,IAAI,QAAQ,MAAM,GAAG,CAAC,CAAC;AACvD,mBAAK,IAAI;AAAA,YACV;AACA,mBAAO;AAAA,UACR,OAAO;AACN,kBAAM;AACN,gBAAI,UAAU;AACd,uBAAW,OAAO,OAAO;AACxB,kBAAI,QAAS,QAAO;AACpB,wBAAU;AACV,mBAAK,KAAK,cAAc,GAAG,CAAC;AAC5B,qBAAO,GAAG,iBAAiB,GAAG,CAAC,IAAI,QAAQ,MAAM,GAAG,CAAC,CAAC;AACtD,mBAAK,IAAI;AAAA,YACV;AACA,mBAAO;AAAA,UACR;AAAA,MACF;AAAA,IACD;AAEA,gBAAYD,MAAK,IAAI;AACrB,WAAOA;AAAA,EACR;AAEA,QAAM,QAAQ,QAAQ,KAAK;AAG3B,MAAI,QAAQ,EAAG,QAAO,GAAG,KAAK;AAE9B,SAAO,IAAI,YAAY,KAAK,GAAG,CAAC;AACjC;AAMA,SAASE,qBAAoB,OAAO;AACnC,QAAM,OAAO,OAAO;AACpB,MAAI,SAAS,SAAU,QAAO,iBAAiB,KAAK;AACpD,MAAI,iBAAiB,OAAQ,QAAO,iBAAiB,MAAM,SAAS,CAAC;AACrE,MAAI,UAAU,OAAQ,QAAO,UAAU,SAAS;AAChD,MAAI,UAAU,KAAK,IAAI,QAAQ,EAAG,QAAO,cAAc,SAAS;AAChE,MAAI,SAAS,SAAU,QAAO,cAAc,KAAK;AACjD,SAAO,OAAO,KAAK;AACpB;", "names": ["str", "value", "stringify", "index", "value", "stringify_primitive"]}