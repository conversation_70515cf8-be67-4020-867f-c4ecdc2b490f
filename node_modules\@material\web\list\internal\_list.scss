//
// Copyright 2022 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:list';
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use '../../tokens';
@use './listitem/list-item';
// go/keep-sorted end

@mixin theme($tokens) {
  $supported-tokens: tokens.$md-comp-list-supported-tokens;

  @each $token, $value in $tokens {
    @if list.index($supported-tokens, $token) == null {
      @error 'Token `#{$token}` is not a supported token.';
    }

    @if $value {
      --md-list-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $tokens: tokens.md-comp-list-values();

  :host {
    background: map.get($tokens, 'container-color');
    color: unset;
    display: flex;
    flex-direction: column;
    outline: none;
    padding: 8px 0;
    // Add position so the elevation overlay (which is absolutely positioned)
    // can be positioned relative to the list root.
    position: relative;
  }
}
