{"version": 3, "file": "outlined-segmented-button-set.js", "sourceRoot": "", "sources": ["outlined-segmented-button-set.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,0BAA0B,EAAC,MAAM,6CAA6C,CAAC;AACvF,OAAO,EAAC,MAAM,IAAI,cAAc,EAAC,MAAM,+BAA+B,CAAC;AACvE,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAQnE;;;;;GAKG;AAEI,IAAM,4BAA4B,GAAlC,MAAM,4BAA6B,SAAQ,0BAA0B;;AAC1D,mCAAM,GAAwB,CAAC,YAAY,EAAE,cAAc,CAAC,AAAtD,CAAuD;AADlE,4BAA4B;IADxC,aAAa,CAAC,kCAAkC,CAAC;GACrC,4BAA4B,CAExC", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {OutlinedSegmentedButtonSet} from './internal/outlined-segmented-button-set.js';\nimport {styles as outlinedStyles} from './internal/outlined-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-outlined-segmented-button-set': MdOutlinedSegmentedButtonSet;\n  }\n}\n\n/**\n * MdOutlinedSegmentedButtonSet is the custom element for the Material\n * Design outlined segmented button set component.\n * @final\n * @suppress {visibility}\n */\n@customElement('md-outlined-segmented-button-set')\nexport class MdOutlinedSegmentedButtonSet extends OutlinedSegmentedButtonSet {\n  static override styles: CSSResultOrNative[] = [sharedStyles, outlinedStyles];\n}\n"]}