//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use './internal/validate';
@use './md-sys-color';
@use './md-sys-state';
@use './v0_192/md-comp-radio-button';
// go/keep-sorted end

$supported-tokens: (
  // go/keep-sorted start
  'disabled-selected-icon-color',
  'disabled-selected-icon-opacity',
  'disabled-unselected-icon-color',
  'disabled-unselected-icon-opacity',
  'focus-icon-color',
  'hover-icon-color',
  'hover-state-layer-color',
  'hover-state-layer-opacity',
  'icon-color',
  'icon-size',
  'pressed-icon-color',
  'pressed-state-layer-color',
  'pressed-state-layer-opacity',
  'selected-focus-icon-color',
  'selected-hover-icon-color',
  'selected-hover-state-layer-color',
  'selected-hover-state-layer-opacity',
  'selected-icon-color',
  'selected-pressed-icon-color',
  'selected-pressed-state-layer-color',
  'selected-pressed-state-layer-opacity',
  'state-layer-size',
  // go/keep-sorted end
);

$unsupported-tokens: (
  // go/keep-sorted start
  'selected-focus-state-layer-color',
  'selected-focus-state-layer-opacity',
  'unselected-focus-state-layer-color',
  'unselected-focus-state-layer-opacity',
  // go/keep-sorted end
);

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-state': md-sys-state.values(),
);

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $tokens: validate.values(
    md-comp-radio-button.values($deps, $exclude-hardcoded-values),
    $supported-tokens: $supported-tokens,
    $unsupported-tokens: $unsupported-tokens,
    $renamed-tokens: (
      // Remove default "unselected" prefix (b/292244480)
      'unselected-focus-icon-color': 'focus-icon-color',
      'unselected-hover-icon-color': 'hover-icon-color',
      'unselected-hover-state-layer-color': 'hover-state-layer-color',
      'unselected-hover-state-layer-opacity': 'hover-state-layer-opacity',
      'unselected-icon-color': 'icon-color',
      'unselected-pressed-icon-color': 'pressed-icon-color',
      'unselected-pressed-state-layer-color': 'pressed-state-layer-color',
      'unselected-pressed-state-layer-opacity': 'pressed-state-layer-opacity'
    )
  );

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      $tokens: map.set($tokens, $token, var(--md-radio-#{$token}, #{$value}));
    }
  }

  @return $tokens;
}
