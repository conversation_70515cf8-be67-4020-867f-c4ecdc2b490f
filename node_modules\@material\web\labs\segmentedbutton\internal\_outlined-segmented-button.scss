//
// Copyright 2022 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// stylelint-disable selector-class-pattern --
// Selector '.md3-*' should only be used in this project.

// go/keep-sorted start
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use '../../../tokens';
// go/keep-sorted end

@mixin styles() {
  $tokens: tokens.md-comp-outlined-segmented-button-values();
  // b/198759625 - Remove spacing tokens once provided.
  $tokens: map.merge(
    $tokens,
    (
      'spacing-leading': 12px,
      'spacing-trailing': 12px,
    )
  );

  :host {
    @each $token, $value in $tokens {
      --_#{$token}: #{$value};
    }
  }

  .md3-segmented-button__outline {
    border-radius: inherit;
    border-style: solid;
    // Move border-width and inset into theme.
    border-width: 1px;
    inset: 0px -0.5px;
    pointer-events: none;
    position: absolute;
  }
}
