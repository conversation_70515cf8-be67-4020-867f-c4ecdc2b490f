{"version": 3, "file": "focusable.js", "sourceRoot": "", "sources": ["focusable.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAe3C;;;GAGG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;AAEjD,MAAM,kBAAkB,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACxD,MAAM,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;AACpD,MAAM,kBAAkB,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACxD,MAAM,cAAc,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAEhD;;;;;;;;;;;;;;;GAeG;AACH,MAAM,UAAU,cAAc,CAC5B,IAAO;;IAEP,MAAe,gBAAiB,SAAQ,IAAI;QAA5C;;YAiBE,QAAoB,GAAG,IAAI,CAAC;YAC5B,QAAkB,GAAkB,IAAI,CAAC;YACzC,QAAoB,GAAG,KAAK,CAAC;QAyC/B,CAAC;QAxDC,IAAI,CAAC,WAAW,CAAC;YACf,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,CAAC,KAAc;YAC9B,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,EAAE,CAAC;gBAChC,OAAO;YACT,CAAC;YAED,IAAI,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC;YACjC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;QACzB,CAAC;QAMQ,iBAAiB;YACxB,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;QACzB,CAAC;QAEQ,wBAAwB,CAC/B,IAAY,EACZ,GAAkB,EAClB,KAAoB;YAEpB,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;gBACxB,KAAK,CAAC,wBAAwB,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBACjD,OAAO;YACT,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,IAAI,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC7B,sCAAsC;gBACtC,OAAO;YACT,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;gBACnC,4DAA4D;gBAC5D,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;gBAC9B,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;gBACvB,OAAO;YACT,CAAC;YAED,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;QACzC,CAAC;QAED,OAnCC,kBAAkB,OAClB,gBAAgB,OAChB,kBAAkB,EAiClB,cAAc,EAAC;YACd,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,gBAAgB,CAAC;YAEpE,IAAI,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC;YAChC,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC;YACjC,IAAI,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC;QACnC,CAAC;KACF;IA1DS;QADP,QAAQ,CAAC,EAAC,UAAU,EAAE,IAAI,EAAC,CAAC;sDACJ;IA4D3B,OAAO,gBAAgB,CAAC;AAC1B,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {LitElement} from 'lit';\nimport {property} from 'lit/decorators.js';\n\nimport {MixinBase, MixinReturn} from './mixin.js';\n\n/**\n * An element that can enable and disable `tabindex` focusability.\n */\nexport interface Focusable {\n  /**\n   * Whether or not the element can be focused. Defaults to true. Set to false\n   * to disable focusing (unless a user has set a `tabindex`).\n   */\n  [isFocusable]: boolean;\n}\n\n/**\n * A property symbol that indicates whether or not a `Focusable` element can be\n * focused.\n */\nexport const isFocusable = Symbol('isFocusable');\n\nconst privateIsFocusable = Symbol('privateIsFocusable');\nconst externalTabIndex = Symbol('externalTabIndex');\nconst isUpdatingTabIndex = Symbol('isUpdatingTabIndex');\nconst updateTabIndex = Symbol('updateTabIndex');\n\n/**\n * Mixes in focusable functionality for a class.\n *\n * Elements can enable and disable their focusability with the `isFocusable`\n * symbol property. Changing `tabIndex` will trigger a lit render, meaning\n * `this.tabIndex` can be used in template expressions.\n *\n * This mixin will preserve externally-set tabindices. If an element turns off\n * focusability, but a user sets `tabindex=\"0\"`, it will still be focusable.\n *\n * To remove user overrides and restore focusability control to the element,\n * remove the `tabindex` attribute.\n *\n * @param base The class to mix functionality into.\n * @return The provided class with `Focusable` mixed in.\n */\nexport function mixinFocusable<T extends MixinBase<LitElement>>(\n  base: T,\n): MixinReturn<T, Focusable> {\n  abstract class FocusableElement extends base implements Focusable {\n    @property({noAccessor: true})\n    declare tabIndex: number;\n\n    get [isFocusable]() {\n      return this[privateIsFocusable];\n    }\n\n    set [isFocusable](value: boolean) {\n      if (this[isFocusable] === value) {\n        return;\n      }\n\n      this[privateIsFocusable] = value;\n      this[updateTabIndex]();\n    }\n\n    [privateIsFocusable] = true;\n    [externalTabIndex]: number | null = null;\n    [isUpdatingTabIndex] = false;\n\n    override connectedCallback() {\n      super.connectedCallback();\n      this[updateTabIndex]();\n    }\n\n    override attributeChangedCallback(\n      name: string,\n      old: string | null,\n      value: string | null,\n    ) {\n      if (name !== 'tabindex') {\n        super.attributeChangedCallback(name, old, value);\n        return;\n      }\n\n      this.requestUpdate('tabIndex', Number(old ?? -1));\n      if (this[isUpdatingTabIndex]) {\n        // Not an externally-initiated update.\n        return;\n      }\n\n      if (!this.hasAttribute('tabindex')) {\n        // User removed the attribute, can now use internal tabIndex\n        this[externalTabIndex] = null;\n        this[updateTabIndex]();\n        return;\n      }\n\n      this[externalTabIndex] = this.tabIndex;\n    }\n\n    [updateTabIndex]() {\n      const internalTabIndex = this[isFocusable] ? 0 : -1;\n      const computedTabIndex = this[externalTabIndex] ?? internalTabIndex;\n\n      this[isUpdatingTabIndex] = true;\n      this.tabIndex = computedTabIndex;\n      this[isUpdatingTabIndex] = false;\n    }\n  }\n\n  return FocusableElement;\n}\n"]}