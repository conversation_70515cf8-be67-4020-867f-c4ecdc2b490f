{"version": 3, "file": "on-report-validity.js", "sourceRoot": "", "sources": ["on-report-validity.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAa,QAAQ,EAAC,MAAM,KAAK,CAAC;AAGzC,OAAO,EAAuB,SAAS,EAAC,MAAM,wBAAwB,CAAC;AAiCvE;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAE3D,uDAAuD;AACvD,MAAM,2BAA2B,GAAG,MAAM,CAAC,6BAA6B,CAAC,CAAC;AAC1E,MAAM,yBAAyB,GAAG,MAAM,CAAC,2BAA2B,CAAC,CAAC;AACtE,MAAM,8BAA8B,GAAG,MAAM,CAAC,gCAAgC,CAAC,CAAC;AAChF,MAAM,2BAA2B,GAAG,MAAM,CAAC,6BAA6B,CAAC,CAAC;AAE1E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,MAAM,UAAU,qBAAqB,CAEnC,IAAO;;IACP,MAAe,uBACb,SAAQ,IAAI;QAqBZ,uDAAuD;QACvD,kCAAkC;QAClC,YAAY,GAAG,IAAW;YACxB,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;YArBjB;;eAEG;YACH,QAA6B,GAAG,IAAI,eAAe,EAAE,CAAC;YAEtD;;;eAGG;YACH,QAA2B,GAAG,KAAK,CAAC;YAEpC;;;;eAIG;YACH,QAAgC,GAAG,KAAK,CAAC;YAMvC,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO;YACT,CAAC;YAED,IAAI,CAAC,gBAAgB,CACnB,SAAS,EACT,CAAC,YAAY,EAAE,EAAE;gBACf,sEAAsE;gBACtE,sEAAsE;gBACtE,mEAAmE;gBACnE,+DAA+D;gBAC/D,WAAW;gBACX,IAAI,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;oBAC/D,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC,gBAAgB,CACnB,SAAS,EACT,GAAG,EAAE;oBACH,gEAAgE;oBAChE,gEAAgE;oBAChE,kBAAkB;oBAClB,IAAI,CAAC,2BAA2B,CAAC,CAAC,YAAY,CAAC,CAAC;gBAClD,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,EAAC,CACb,CAAC;YACJ,CAAC,EACD;gBACE,gEAAgE;gBAChE,mEAAmE;gBACnE,mEAAmE;gBACnE,iEAAiE;gBACjE,OAAO,EAAE,IAAI;aACd,CACF,CAAC;QACJ,CAAC;QAEQ,aAAa;YACpB,IAAI,CAAC,yBAAyB,CAAC,GAAG,IAAI,CAAC;YACvC,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;YACpC,IAAI,CAAC,yBAAyB,CAAC,GAAG,KAAK,CAAC;YACxC,OAAO,KAAK,CAAC;QACf,CAAC;QAEQ,cAAc;YACrB,IAAI,CAAC,8BAA8B,CAAC,GAAG,IAAI,CAAC;YAC5C,MAAM,KAAK,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;YACrC,uEAAuE;YACvE,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC,2BAA2B,CAAC,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC;YAED,IAAI,CAAC,8BAA8B,CAAC,GAAG,KAAK,CAAC;YAC7C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OA3EC,2BAA2B,OAM3B,yBAAyB,OAOzB,8BAA8B,EA8D9B,2BAA2B,EAAC,CAAC,YAA0B;YACtD,sEAAsE;YACtE,uEAAuE;YACvE,kEAAkE;YAClE,2DAA2D;YAC3D,MAAM,WAAW,GAAG,YAAY,EAAE,gBAAgB,CAAC;YACnD,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO;YACT,CAAC;YAED,IAAI,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC;YAErC,uEAAuE;YACvE,yEAAyE;YACzE,qBAAqB;YACrB,MAAM,2BAA2B,GAC/B,CAAC,WAAW,IAAI,YAAY,EAAE,gBAAgB,CAAC;YACjD,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBACjC,OAAO;YACT,CAAC;YAED,sCAAsC;YACtC,2DAA2D;YAC3D,wEAAwE;YACxE,qBAAqB;YACrB,IACE,IAAI,CAAC,8BAA8B,CAAC;gBACpC,2BAA2B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EACvD,CAAC;gBACD,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC;QACH,CAAC;QAED,CAAC,gBAAgB,CAAC,CAAC,YAA0B;YAC3C,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;QAEQ,sBAAsB,CAAC,IAA4B;YAC1D,4DAA4D;YAC5D,IAAI,KAAK,CAAC,sBAAsB,EAAE,CAAC;gBACjC,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YACrC,CAAC;YAED,oCAAoC;YACpC,IAAI,CAAC,2BAA2B,CAAC,CAAC,KAAK,EAAE,CAAC;YAC1C,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO;YACT,CAAC;YAED,IAAI,CAAC,2BAA2B,CAAC,GAAG,IAAI,eAAe,EAAE,CAAC;YAE1D,yEAAyE;YACzE,gEAAgE;YAChE,EAAE;YACF,qEAAqE;YACrE,2CAA2C;YAC3C,0BAA0B,CACxB,IAAI,EACJ,IAAI,EACJ,GAAG,EAAE;gBACH,IAAI,CAAC,2BAA2B,CAAC,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC,EACD,IAAI,CAAC,2BAA2B,CAAC,CAAC,MAAM,CACzC,CAAC;QACJ,CAAC;KACF;IAED,OAAO,uBAAuB,CAAC;AACjC,CAAC;AAED;;;;;;;;;GASG;AACH,SAAS,0BAA0B,CACjC,OAAgB,EAChB,IAAqB,EACrB,cAA0B,EAC1B,OAAoB;IAEpB,MAAM,aAAa,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAEjD,8EAA8E;IAC9E,4EAA4E;IAC5E,SAAS;IACT,IAAI,mBAAmB,GAAG,KAAK,CAAC;IAChC,IAAI,sBAAmD,CAAC;IACxD,IAAI,oBAAoB,GAAG,KAAK,CAAC;IACjC,aAAa,CAAC,gBAAgB,CAC5B,QAAQ,EACR,GAAG,EAAE;QACH,oBAAoB,GAAG,IAAI,CAAC;QAC5B,sBAAsB,GAAG,IAAI,eAAe,EAAE,CAAC;QAC/C,mBAAmB,GAAG,KAAK,CAAC;QAC5B,OAAO,CAAC,gBAAgB,CACtB,SAAS,EACT,GAAG,EAAE;YACH,mBAAmB,GAAG,IAAI,CAAC;QAC7B,CAAC,EACD;YACE,MAAM,EAAE,sBAAsB,CAAC,MAAM;SACtC,CACF,CAAC;IACJ,CAAC,EACD,EAAC,MAAM,EAAE,OAAO,EAAC,CAClB,CAAC;IAEF,aAAa,CAAC,gBAAgB,CAC5B,OAAO,EACP,GAAG,EAAE;QACH,oBAAoB,GAAG,KAAK,CAAC;QAC7B,sBAAsB,EAAE,KAAK,EAAE,CAAC;QAChC,IAAI,mBAAmB,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QAED,cAAc,EAAE,CAAC;IACnB,CAAC,EACD,EAAC,MAAM,EAAE,OAAO,EAAC,CAClB,CAAC;IAEF,mEAAmE;IACnE,wDAAwD;IACxD,+DAA+D;IAC/D,uEAAuE;IACvE,IAAI,CAAC,gBAAgB,CACnB,QAAQ,EACR,GAAG,EAAE;QACH,uEAAuE;QACvE,YAAY;QACZ,IAAI,oBAAoB,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QAED,cAAc,EAAE,CAAC;IACnB,CAAC,EACD;QACE,MAAM,EAAE,OAAO;KAChB,CACF,CAAC;IAEF,0EAA0E;IAC1E,wEAAwE;IACxE,4CAA4C;IAC5C,EAAE;IACF,6EAA6E;IAC7E,2DAA2D;IAC3D,EAAE;IACF,2EAA2E;IAC3E,6EAA6E;IAC7E,iDAAiD;AACnD,CAAC;AAED,MAAM,mBAAmB,GAAG,IAAI,OAAO,EAAgC,CAAC;AAExE;;;;;;;;;GASG;AACH,SAAS,oBAAoB,CAAC,IAAqB;IACjD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QACnC,4EAA4E;QAC5E,2EAA2E;QAC3E,qCAAqC;QACrC,EAAE;QACF,2EAA2E;QAC3E,0EAA0E;QAC1E,sEAAsE;QACtE,4BAA4B;QAC5B,EAAE;QACF,4EAA4E;QAC5E,sCAAsC;QACtC,MAAM,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC;QAChC,mBAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAErC,4EAA4E;QAC5E,8BAA8B;QAC9B,qEAAqE;QACrE,KAAK,MAAM,UAAU,IAAI,CAAC,gBAAgB,EAAE,eAAe,CAAU,EAAE,CAAC;YACtE,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;YACrC,IAAI,CAAC,UAAU,CAAC,GAAG;gBACjB,KAAK,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACzC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3D,KAAK,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;gBACxC,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;AACxC,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,2BAA2B,CAClC,IAA4B,EAC5B,OAAoB;IAEpB,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,mBAAwC,CAAC;IAC7C,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QACpC,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YAChC,mBAAmB,GAAG,OAAO,CAAC;YAC9B,MAAM;QACR,CAAC;IACH,CAAC;IAED,OAAO,mBAAmB,KAAK,OAAO,CAAC;AACzC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {LitElement, isServer} from 'lit';\n\nimport {ConstraintValidation} from './constraint-validation.js';\nimport {WithElementInternals, internals} from './element-internals.js';\nimport {MixinBase, MixinReturn} from './mixin.js';\n\n/**\n * A constraint validation element that has a callback for when the element\n * should report validity styles and error messages to the user.\n *\n * This is commonly used in text-field-like controls that display error styles\n * and error messages.\n */\nexport interface OnReportValidity extends ConstraintValidation {\n  /**\n   * A callback that is invoked when validity should be reported. Components\n   * that can display their own error state can use this and update their\n   * styles.\n   *\n   * If an invalid event is provided, the element is invalid. If `null`, the\n   * element is valid.\n   *\n   * The invalid event's `preventDefault()` may be called to stop the platform\n   * popup from displaying.\n   *\n   * @param invalidEvent The `invalid` event dispatched when an element is\n   *     invalid, or `null` if the element is valid.\n   */\n  [onReportValidity](invalidEvent: Event | null): void;\n\n  // `mixinOnReportValidity()` implements this optional method. If overriden,\n  // call `super.formAssociatedCallback(form)`.\n  // (inherit jsdoc from `FormAssociated`)\n  formAssociatedCallback(form: HTMLFormElement | null): void;\n}\n\n/**\n * A symbol property used for a callback when validity has been reported.\n */\nexport const onReportValidity = Symbol('onReportValidity');\n\n// Private symbol members, used to avoid name clashing.\nconst privateCleanupFormListeners = Symbol('privateCleanupFormListeners');\nconst privateDoNotReportInvalid = Symbol('privateDoNotReportInvalid');\nconst privateIsSelfReportingValidity = Symbol('privateIsSelfReportingValidity');\nconst privateCallOnReportValidity = Symbol('privateCallOnReportValidity');\n\n/**\n * Mixes in a callback for constraint validation when validity should be\n * styled and reported to the user.\n *\n * This is commonly used in text-field-like controls that display error styles\n * and error messages.\n *\n * @example\n * ```ts\n * const baseClass = mixinOnReportValidity(\n *   mixinConstraintValidation(\n *     mixinFormAssociated(mixinElementInternals(LitElement)),\n *   ),\n * );\n *\n * class MyField extends baseClass {\n *   \\@property({type: Boolean}) error = false;\n *   \\@property() errorMessage = '';\n *\n *   [onReportValidity](invalidEvent: Event | null) {\n *     this.error = !!invalidEvent;\n *     this.errorMessage = this.validationMessage;\n *\n *     // Optionally prevent platform popup from displaying\n *     invalidEvent?.preventDefault();\n *   }\n * }\n * ```\n *\n * @param base The class to mix functionality into.\n * @return The provided class with `OnReportValidity` mixed in.\n */\nexport function mixinOnReportValidity<\n  T extends MixinBase<LitElement & ConstraintValidation & WithElementInternals>,\n>(base: T): MixinReturn<T, OnReportValidity> {\n  abstract class OnReportValidityElement\n    extends base\n    implements OnReportValidity\n  {\n    /**\n     * Used to clean up event listeners when a new form is associated.\n     */\n    [privateCleanupFormListeners] = new AbortController();\n\n    /**\n     * Used to determine if an invalid event should report validity. Invalid\n     * events from `checkValidity()` do not trigger reporting.\n     */\n    [privateDoNotReportInvalid] = false;\n\n    /**\n     * Used to determine if the control is reporting validity from itself, or\n     * if a `<form>` is causing the validity report. Forms have different\n     * control focusing behavior.\n     */\n    [privateIsSelfReportingValidity] = false;\n\n    // Mixins must have a constructor with `...args: any[]`\n    // tslint:disable-next-line:no-any\n    constructor(...args: any[]) {\n      super(...args);\n      if (isServer) {\n        return;\n      }\n\n      this.addEventListener(\n        'invalid',\n        (invalidEvent) => {\n          // Listen for invalid events dispatched by a `<form>` when it tries to\n          // submit and the element is invalid. We ignore events dispatched when\n          // calling `checkValidity()` as well as untrusted events, since the\n          // `reportValidity()` and `<form>`-dispatched events are always\n          // trusted.\n          if (this[privateDoNotReportInvalid] || !invalidEvent.isTrusted) {\n            return;\n          }\n\n          this.addEventListener(\n            'invalid',\n            () => {\n              // A normal bubbling phase event listener. By adding it here, we\n              // ensure it's the last event listener that is called during the\n              // bubbling phase.\n              this[privateCallOnReportValidity](invalidEvent);\n            },\n            {once: true},\n          );\n        },\n        {\n          // Listen during the capture phase, which will happen before the\n          // bubbling phase. That way, we can add a final event listener that\n          // will run after other event listeners, and we can check if it was\n          // default prevented. This works because invalid does not bubble.\n          capture: true,\n        },\n      );\n    }\n\n    override checkValidity() {\n      this[privateDoNotReportInvalid] = true;\n      const valid = super.checkValidity();\n      this[privateDoNotReportInvalid] = false;\n      return valid;\n    }\n\n    override reportValidity() {\n      this[privateIsSelfReportingValidity] = true;\n      const valid = super.reportValidity();\n      // Constructor's invalid listener will handle reporting invalid events.\n      if (valid) {\n        this[privateCallOnReportValidity](null);\n      }\n\n      this[privateIsSelfReportingValidity] = false;\n      return valid;\n    }\n\n    [privateCallOnReportValidity](invalidEvent: Event | null) {\n      // Since invalid events do not bubble to parent listeners, and because\n      // our invalid listeners are added lazily after other listeners, we can\n      // reliably read `defaultPrevented` synchronously without worrying\n      // about waiting for another listener that could cancel it.\n      const wasCanceled = invalidEvent?.defaultPrevented;\n      if (wasCanceled) {\n        return;\n      }\n\n      this[onReportValidity](invalidEvent);\n\n      // If an implementation calls invalidEvent.preventDefault() to stop the\n      // platform popup from displaying, focusing is also prevented, so we need\n      // to manually focus.\n      const implementationCanceledFocus =\n        !wasCanceled && invalidEvent?.defaultPrevented;\n      if (!implementationCanceledFocus) {\n        return;\n      }\n\n      // The control should be focused when:\n      // - `control.reportValidity()` is called (self-reporting).\n      // - a form is reporting validity for its controls and this is the first\n      //   invalid control.\n      if (\n        this[privateIsSelfReportingValidity] ||\n        isFirstInvalidControlInForm(this[internals].form, this)\n      ) {\n        this.focus();\n      }\n    }\n\n    [onReportValidity](invalidEvent: Event | null) {\n      throw new Error('Implement [onReportValidity]');\n    }\n\n    override formAssociatedCallback(form: HTMLFormElement | null) {\n      // can't use super.formAssociatedCallback?.() due to closure\n      if (super.formAssociatedCallback) {\n        super.formAssociatedCallback(form);\n      }\n\n      // Clean up previous form listeners.\n      this[privateCleanupFormListeners].abort();\n      if (!form) {\n        return;\n      }\n\n      this[privateCleanupFormListeners] = new AbortController();\n\n      // Add a listener that fires when the form runs constraint validation and\n      // the control is valid, so that it may remove its error styles.\n      //\n      // This happens on `form.reportValidity()` and `form.requestSubmit()`\n      // (both when the submit fails and passes).\n      addFormReportValidListener(\n        this,\n        form,\n        () => {\n          this[privateCallOnReportValidity](null);\n        },\n        this[privateCleanupFormListeners].signal,\n      );\n    }\n  }\n\n  return OnReportValidityElement;\n}\n\n/**\n * Add a listener that fires when a form runs constraint validation on a control\n * and it is valid. This is needed to clear previously invalid styles.\n *\n * @param control The control of the form to listen for valid events.\n * @param form The control's form that can run constraint validation.\n * @param onControlValid A listener that is called when the form runs constraint\n *     validation and the control is valid.\n * @param cleanup A cleanup signal to remove the listener.\n */\nfunction addFormReportValidListener(\n  control: Element,\n  form: HTMLFormElement,\n  onControlValid: () => void,\n  cleanup: AbortSignal,\n) {\n  const validateHooks = getFormValidateHooks(form);\n\n  // When a form validates its controls, check if an invalid event is dispatched\n  // on the control. If it is not, then inform the control to report its valid\n  // state.\n  let controlFiredInvalid = false;\n  let cleanupInvalidListener: AbortController | undefined;\n  let isNextSubmitFromHook = false;\n  validateHooks.addEventListener(\n    'before',\n    () => {\n      isNextSubmitFromHook = true;\n      cleanupInvalidListener = new AbortController();\n      controlFiredInvalid = false;\n      control.addEventListener(\n        'invalid',\n        () => {\n          controlFiredInvalid = true;\n        },\n        {\n          signal: cleanupInvalidListener.signal,\n        },\n      );\n    },\n    {signal: cleanup},\n  );\n\n  validateHooks.addEventListener(\n    'after',\n    () => {\n      isNextSubmitFromHook = false;\n      cleanupInvalidListener?.abort();\n      if (controlFiredInvalid) {\n        return;\n      }\n\n      onControlValid();\n    },\n    {signal: cleanup},\n  );\n\n  // The above hooks handle imperatively submitting the form, but not\n  // declaratively submitting the form. This happens when:\n  // 1. A non-custom element `<button type=\"submit\">` is clicked.\n  // 2. Enter is pressed on a non-custom element text editable `<input>`.\n  form.addEventListener(\n    'submit',\n    () => {\n      // This submit was from `form.requestSubmit()`, which already calls the\n      // listener.\n      if (isNextSubmitFromHook) {\n        return;\n      }\n\n      onControlValid();\n    },\n    {\n      signal: cleanup,\n    },\n  );\n\n  // Note: it is a known limitation that we cannot detect if a form tries to\n  // submit declaratively, but fails to do so because an unrelated sibling\n  // control failed its constraint validation.\n  //\n  // Since we cannot detect when that happens, a previously invalid control may\n  // not clear its error styling when it becomes valid again.\n  //\n  // To work around this, call `form.reportValidity()` when submitting a form\n  // declaratively. This can be down on the `<button type=\"submit\">`'s click or\n  // the text editable `<input>`'s 'Enter' keydown.\n}\n\nconst FORM_VALIDATE_HOOKS = new WeakMap<HTMLFormElement, EventTarget>();\n\n/**\n * Get a hooks `EventTarget` that dispatches 'before' and 'after' events that\n * fire before a form runs constraint validation and immediately after it\n * finishes running constraint validation on its controls.\n *\n * This happens during `form.reportValidity()` and `form.requestSubmit()`.\n *\n * @param form The form to get or set up hooks for.\n * @return A hooks `EventTarget` to add listeners to.\n */\nfunction getFormValidateHooks(form: HTMLFormElement) {\n  if (!FORM_VALIDATE_HOOKS.has(form)) {\n    // Patch form methods to add event listener hooks. These are needed to react\n    // to form behaviors that do not dispatch events, such as a form asking its\n    // controls to report their validity.\n    //\n    // We should only patch the methods once, since multiple controls and other\n    // forces may want to patch this method. We cannot reliably clean it up if\n    // there are multiple patched and re-patched methods referring holding\n    // references to each other.\n    //\n    // Instead, we never clean up the patch but add and clean up event listeners\n    // added to the hooks after the patch.\n    const hooks = new EventTarget();\n    FORM_VALIDATE_HOOKS.set(form, hooks);\n\n    // Add hooks to support notifying before and after a form has run constraint\n    // validation on its controls.\n    // Note: `form.submit()` does not run constraint validation per spec.\n    for (const methodName of ['reportValidity', 'requestSubmit'] as const) {\n      const superMethod = form[methodName];\n      form[methodName] = function (this: HTMLFormElement) {\n        hooks.dispatchEvent(new Event('before'));\n        const result = Reflect.apply(superMethod, this, arguments);\n        hooks.dispatchEvent(new Event('after'));\n        return result;\n      };\n    }\n  }\n\n  return FORM_VALIDATE_HOOKS.get(form)!;\n}\n\n/**\n * Checks if a control is the first invalid control in a form.\n *\n * @param form The control's form. When `null`, the control doesn't have a form\n *     and the method returns true.\n * @param control The control to check.\n * @return True if there is no form or if the control is the form's first\n *     invalid control.\n */\nfunction isFirstInvalidControlInForm(\n  form: HTMLFormElement | null,\n  control: HTMLElement,\n) {\n  if (!form) {\n    return true;\n  }\n\n  let firstInvalidControl: Element | undefined;\n  for (const element of form.elements) {\n    if (element.matches(':invalid')) {\n      firstInvalidControl = element;\n      break;\n    }\n  }\n\n  return firstInvalidControl === control;\n}\n"]}