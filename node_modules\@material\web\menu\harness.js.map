{"version": 3, "file": "harness.js", "sourceRoot": "", "sources": ["harness.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EAAC,OAAO,EAAC,MAAM,uBAAuB,CAAC;AAG9C,OAAO,EAAC,eAAe,EAAC,MAAM,gCAAgC,CAAC;AAE/D,OAAO,EAAC,eAAe,EAAC,MAAM,gCAAgC,CAAC;AAE/D;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,OAAa;IAC5C;;OAEG;IACgB,KAAK,CAAC,qBAAqB;QAC5C,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;QAClC,OAAO,IAAI,CAAC,OAAe,CAAC;IAC9B,CAAC;IAED,uDAAuD;IACvD,QAAQ;QACN,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAC3B,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,eAAe,CAAC,IAAgC,CAAC,CAChE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,IAAI;QACR,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;QAC1B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACrC,IAAI,CAAC,gBAAgB,CACnB,QAAQ,EACR,GAAG,EAAE;gBACH,OAAO,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,EAAC,CACb,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,MAAM,MAAM,CAAC;IACf,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {LitElement} from 'lit';\n\nimport {Harness} from '../testing/harness.js';\n\nimport {Menu} from './internal/menu.js';\nimport {MenuItemHarness} from './internal/menuitem/harness.js';\n\nexport {MenuItemHarness} from './internal/menuitem/harness.js';\n\n/**\n * Test harness for menu.\n */\nexport class MenuHarness extends Harness<Menu> {\n  /**\n   * Shows the menu and returns the first list item element.\n   */\n  protected override async getInteractiveElement() {\n    await this.element.updateComplete;\n    return this.element as Menu;\n  }\n\n  /** @return ListItem harnesses for the menu's items. */\n  getItems() {\n    return this.element.items.map(\n      (item) => new MenuItemHarness(item as typeof item & LitElement),\n    );\n  }\n\n  async show() {\n    const menu = this.element;\n    if (menu.open) {\n      return;\n    }\n\n    const opened = new Promise((resolve) => {\n      menu.addEventListener(\n        'opened',\n        () => {\n          resolve(true);\n        },\n        {once: true},\n      );\n    });\n\n    menu.show();\n    await opened;\n  }\n}\n"]}