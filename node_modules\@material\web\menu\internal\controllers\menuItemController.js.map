{"version": 3, "file": "menuItemController.js", "sourceRoot": "", "sources": ["menuItemController.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EACL,WAAW,EACX,2BAA2B,EAC3B,aAAa,GACd,MAAM,aAAa,CAAC;AAgFrB;;;GAGG;AACH,MAAM,OAAO,kBAAkB;IAO7B;;;OAGG;IACH,YACmB,IAAuC,EACxD,MAAgC;QADf,SAAI,GAAJ,IAAI,CAAmC;QAXlD,0BAAqB,GAAkB,IAAI,CAAC;QAqGpD;;;WAGG;QACH,YAAO,GAAG,GAAG,EAAE;YACb,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ;gBAAE,OAAO;YAE/B,IAAI,CAAC,IAAI,CAAC,aAAa,CACrB,2BAA2B,CAAC,IAAI,CAAC,IAAI,EAAE;gBACrC,IAAI,EAAE,WAAW,CAAC,eAAe;aAClC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC;QAEF;;;WAGG;QACH,cAAS,GAAG,CAAC,KAAoB,EAAE,EAAE;YACnC,sEAAsE;YACtE,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC7C,MAAM,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBACxD,IAAI,kBAAkB,YAAY,iBAAiB,EAAE,CAAC;oBACpD,kBAAkB,CAAC,KAAK,EAAE,CAAC;gBAC7B,CAAC;YACH,CAAC;YAED,IAAI,KAAK,CAAC,gBAAgB;gBAAE,OAAO;YAEnC,yEAAyE;YACzE,yEAAyE;YACzE,qBAAqB;YACrB,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;YAC3B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,OAAO,KAAK,QAAQ;gBAAE,OAAO;YAEvD,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC3B,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,IAAI,CAAC,aAAa,CACrB,2BAA2B,CAAC,IAAI,CAAC,IAAI,EAAE;oBACrC,IAAI,EAAE,WAAW,CAAC,OAAO;oBACzB,GAAG,EAAE,OAAO;iBACb,CAAC,CACH,CAAC;YACJ,CAAC;QACH,CAAC,CAAC;QAnIA,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,CAAC;QACtD,IAAI,CAAC,yBAAyB,GAAG,MAAM,CAAC,yBAAyB,CAAC;QAClE,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC;QACpD,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC,qBAAqB,CAAC;QAC1D,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;;;;OAKG;IACH,IAAI,aAAa;QACf,IAAI,IAAI,CAAC,qBAAqB,KAAK,IAAI,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEpD,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,gBAAgB,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,EAAE;YAC3C,IAAI,eAAe,CAAC,WAAW,IAAI,eAAe,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC;gBACtE,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;YACrD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,2EAA2E;QAC3E,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,kBAAkB,EAAE,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;gBACnD,IAAI,cAAc,CAAC,WAAW,IAAI,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC;oBACpE,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;gBACpD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,gEAAgE;QAChE,qCAAqC;QACrC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,yBAAyB,EAAE,CAAC,OAAO,CAAC,CAAC,qBAAqB,EAAE,EAAE;gBACjE,IACE,qBAAqB,CAAC,WAAW;oBACjC,qBAAqB,CAAC,WAAW,CAAC,IAAI,EAAE,EACxC,CAAC;oBACD,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC3D,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAE5B,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,MAAM;gBACT,OAAO,GAAY,CAAC;YACtB,KAAK,QAAQ;gBACX,OAAO,QAAiB,CAAC;YAC3B,QAAQ;YACR,KAAK,UAAU,CAAC;YAChB,KAAK,QAAQ;gBACX,OAAO,IAAa,CAAC;QACzB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC;IAC7D,CAAC;IAED,aAAa;QACX,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IAClD,CAAC;IAED,UAAU;QACR,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;QAC1B,CAAC;IACH,CAAC;IAgDD;;OAEG;IACH,gBAAgB,CAAC,IAAY;QAC3B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;IACpC,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {Reactive<PERSON><PERSON>roller, ReactiveControllerHost} from 'lit';\n\nimport {\n  CloseReason,\n  createDefaultCloseMenuEvent,\n  isClosableKey,\n} from './shared.js';\n\n/**\n * Interface specific to menu item and not HTMLElement.\n *\n * NOTE: required properties are expected to be reactive.\n */\ninterface MenuItemAdditions {\n  /**\n   * Whether or not the item is in the disabled state.\n   */\n  disabled: boolean;\n  /**\n   * The text of the item that will be used for typeahead. If not set, defaults\n   * to the textContent of the element slotted into the headline.\n   */\n  typeaheadText: string;\n  /**\n   * Whether or not the item is in the selected visual state.\n   */\n  selected: boolean;\n  /**\n   * Sets the behavior and role of the menu item, defaults to \"menuitem\".\n   */\n  type: MenuItemType;\n  /**\n   * Whether it should keep the menu open after click.\n   */\n  keepOpen?: boolean;\n  /**\n   * Sets the underlying `HTMLAnchorElement`'s `href` resource attribute.\n   */\n  href?: string;\n  /**\n   * Focuses the item.\n   */\n  focus: () => void;\n}\n\n/**\n * The interface of every menu item interactive with a menu. All menu items\n * should implement this interface to be compatible with md-menu. Additionally\n * it should have the `md-menu-item` attribute set.\n *\n * NOTE, the required properties are recommended to be reactive properties.\n */\nexport type MenuItem = MenuItemAdditions & HTMLElement;\n\n/**\n * Supported behaviors for a menu item.\n */\nexport type MenuItemType = 'menuitem' | 'option' | 'button' | 'link';\n\n/**\n * The options used to inialize MenuItemController.\n */\nexport interface MenuItemControllerConfig {\n  /**\n   * A function that returns the headline element of the menu item.\n   */\n  getHeadlineElements: () => HTMLElement[];\n\n  /**\n   * A function that returns the supporting-text element of the menu item.\n   */\n  getSupportingTextElements: () => HTMLElement[];\n\n  /**\n   * A function that returns the default slot / misc content.\n   */\n  getDefaultElements: () => Node[];\n\n  /**\n   * The HTML Element that accepts user interactions like click. Used for\n   * occasions like programmatically clicking anchor tags when `Enter` is\n   * pressed.\n   */\n  getInteractiveElement: () => HTMLElement | null;\n}\n\n/**\n * A controller that provides most functionality of an element that implements\n * the MenuItem interface.\n */\nexport class MenuItemController implements ReactiveController {\n  private internalTypeaheadText: string | null = null;\n  private readonly getHeadlineElements: MenuItemControllerConfig['getHeadlineElements'];\n  private readonly getSupportingTextElements: MenuItemControllerConfig['getSupportingTextElements'];\n  private readonly getDefaultElements: MenuItemControllerConfig['getDefaultElements'];\n  private readonly getInteractiveElement: MenuItemControllerConfig['getInteractiveElement'];\n\n  /**\n   * @param host The MenuItem in which to attach this controller to.\n   * @param config The object that configures this controller's behavior.\n   */\n  constructor(\n    private readonly host: ReactiveControllerHost & MenuItem,\n    config: MenuItemControllerConfig,\n  ) {\n    this.getHeadlineElements = config.getHeadlineElements;\n    this.getSupportingTextElements = config.getSupportingTextElements;\n    this.getDefaultElements = config.getDefaultElements;\n    this.getInteractiveElement = config.getInteractiveElement;\n    this.host.addController(this);\n  }\n\n  /**\n   * The text that is selectable via typeahead. If not set, defaults to the\n   * innerText of the item slotted into the `\"headline\"` slot, and if there are\n   * no slotted elements into headline, then it checks the _default_ slot, and\n   * then the `\"supporting-text\"` slot if nothing is in _default_.\n   */\n  get typeaheadText() {\n    if (this.internalTypeaheadText !== null) {\n      return this.internalTypeaheadText;\n    }\n\n    const headlineElements = this.getHeadlineElements();\n\n    const textParts: string[] = [];\n    headlineElements.forEach((headlineElement) => {\n      if (headlineElement.textContent && headlineElement.textContent.trim()) {\n        textParts.push(headlineElement.textContent.trim());\n      }\n    });\n\n    // If there are no headline elements, check the default slot's text content\n    if (textParts.length === 0) {\n      this.getDefaultElements().forEach((defaultElement) => {\n        if (defaultElement.textContent && defaultElement.textContent.trim()) {\n          textParts.push(defaultElement.textContent.trim());\n        }\n      });\n    }\n\n    // If there are no headline nor default slot elements, check the\n    //supporting-text slot's text content\n    if (textParts.length === 0) {\n      this.getSupportingTextElements().forEach((supportingTextElement) => {\n        if (\n          supportingTextElement.textContent &&\n          supportingTextElement.textContent.trim()\n        ) {\n          textParts.push(supportingTextElement.textContent.trim());\n        }\n      });\n    }\n\n    return textParts.join(' ');\n  }\n\n  /**\n   * The recommended tag name to render as the list item.\n   */\n  get tagName() {\n    const type = this.host.type;\n\n    switch (type) {\n      case 'link':\n        return 'a' as const;\n      case 'button':\n        return 'button' as const;\n      default:\n      case 'menuitem':\n      case 'option':\n        return 'li' as const;\n    }\n  }\n\n  /**\n   * The recommended role of the menu item.\n   */\n  get role() {\n    return this.host.type === 'option' ? 'option' : 'menuitem';\n  }\n\n  hostConnected() {\n    this.host.toggleAttribute('md-menu-item', true);\n  }\n\n  hostUpdate() {\n    if (this.host.href) {\n      this.host.type = 'link';\n    }\n  }\n\n  /**\n   * Bind this click listener to the interactive element. Handles closing the\n   * menu.\n   */\n  onClick = () => {\n    if (this.host.keepOpen) return;\n\n    this.host.dispatchEvent(\n      createDefaultCloseMenuEvent(this.host, {\n        kind: CloseReason.CLICK_SELECTION,\n      }),\n    );\n  };\n\n  /**\n   * Bind this click listener to the interactive element. Handles closing the\n   * menu.\n   */\n  onKeydown = (event: KeyboardEvent) => {\n    // Check if the interactive element is an anchor tag. If so, click it.\n    if (this.host.href && event.code === 'Enter') {\n      const interactiveElement = this.getInteractiveElement();\n      if (interactiveElement instanceof HTMLAnchorElement) {\n        interactiveElement.click();\n      }\n    }\n\n    if (event.defaultPrevented) return;\n\n    // If the host has keepOpen = true we should ignore clicks & Space/Enter,\n    // however we always maintain the ability to close a menu with a explicit\n    // `escape` keypress.\n    const keyCode = event.code;\n    if (this.host.keepOpen && keyCode !== 'Escape') return;\n\n    if (isClosableKey(keyCode)) {\n      event.preventDefault();\n      this.host.dispatchEvent(\n        createDefaultCloseMenuEvent(this.host, {\n          kind: CloseReason.KEYDOWN,\n          key: keyCode,\n        }),\n      );\n    }\n  };\n\n  /**\n   * Use to set the typeaheadText when it changes.\n   */\n  setTypeaheadText(text: string) {\n    this.internalTypeaheadText = text;\n  }\n}\n"]}