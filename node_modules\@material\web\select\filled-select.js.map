{"version": 3, "file": "filled-select.js", "sourceRoot": "", "sources": ["filled-select.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,YAAY,EAAC,MAAM,6BAA6B,CAAC;AACzD,OAAO,EAAC,MAAM,EAAC,MAAM,oCAAoC,CAAC;AAC1D,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAQnE;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AAEI,IAAM,cAAc,GAApB,MAAM,cAAe,SAAQ,YAAY;;AAC9B,qBAAM,GAAwB,CAAC,YAAY,EAAE,MAAM,CAAC,AAA9C,CAA+C;AAD1D,cAAc;IAD1B,aAAa,CAAC,kBAAkB,CAAC;GACrB,cAAc,CAE1B", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {FilledSelect} from './internal/filled-select.js';\nimport {styles} from './internal/filled-select-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-filled-select': MdFilledSelect;\n  }\n}\n\n/**\n * @summary\n * Select menus display a list of choices on temporary surfaces and display the\n * currently selected menu item above the menu.\n *\n * @description\n * The select component allows users to choose a value from a fixed list of\n * available options. Composed of an interactive anchor button and a menu, it is\n * analogous to the native HTML `<select>` element. This is the \"filled\"\n * variant.\n *\n * @example\n * ```html\n * <md-filled-select label=\"fruits\">\n *   <!-- An empty selected option will give select an \"un-filled\" state -->\n *   <md-select-option selected></md-select-option>\n *   <md-select-option value=\"apple\" headline=\"Apple\"></md-select-option>\n *   <md-select-option value=\"banana\" headline=\"Banana\"></md-select-option>\n *   <md-select-option value=\"kiwi\" headline=\"Kiwi\"></md-select-option>\n *   <md-select-option value=\"orange\" headline=\"Orange\"></md-select-option>\n *   <md-select-option value=\"tomato\" headline=\"Tomato\"></md-select-option>\n * </md-filled-select>\n * ```\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-filled-select')\nexport class MdFilledSelect extends FilledSelect {\n  static override styles: CSSResultOrNative[] = [sharedStyles, styles];\n}\n"]}