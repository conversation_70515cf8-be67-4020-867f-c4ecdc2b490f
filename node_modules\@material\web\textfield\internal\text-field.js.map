{"version": 3, "file": "text-field.js", "sourceRoot": "", "sources": ["text-field.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,EAAC,UAAU,EAAkB,IAAI,EAAE,OAAO,EAAC,MAAM,KAAK,CAAC;AAC9D,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAE,qBAAqB,EAAE,KAAK,EAAC,MAAM,mBAAmB,CAAC;AAChF,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AACrD,OAAO,EAAC,IAAI,EAAC,MAAM,wBAAwB,CAAC;AAC5C,OAAO,EAAY,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAChE,OAAO,EAAc,IAAI,IAAI,UAAU,EAAC,MAAM,oBAAoB,CAAC;AAInE,OAAO,EAAC,kBAAkB,EAAC,MAAM,iCAAiC,CAAC;AACnE,OAAO,EAAC,eAAe,EAAC,MAAM,+CAA+C,CAAC;AAC9E,OAAO,EAAC,eAAe,EAAC,MAAM,2CAA2C,CAAC;AAC1E,OAAO,EACL,eAAe,EACf,iBAAiB,EACjB,yBAAyB,GAC1B,MAAM,+CAA+C,CAAC;AACvD,OAAO,EAAC,qBAAqB,EAAC,MAAM,2CAA2C,CAAC;AAChF,OAAO,EACL,YAAY,EACZ,mBAAmB,GACpB,MAAM,yCAAyC,CAAC;AACjD,OAAO,EACL,qBAAqB,EACrB,gBAAgB,GACjB,MAAM,4CAA4C,CAAC;AACpD,OAAO,EAAC,kBAAkB,EAAC,MAAM,yDAAyD,CAAC;AAyC3F,wCAAwC;AACxC,MAAM,kBAAkB,GAAG,kBAAkB,CAC3C,qBAAqB,CACnB,yBAAyB,CACvB,mBAAmB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,CACvD,CACF,CACF,CAAC;AAEF;;;;;;;;;;;;GAYG;AACH,MAAM,OAAgB,SAAU,SAAQ,kBAAkB;IAA1D;;QAOE;;;;;WAKG;QACuC,UAAK,GAAG,KAAK,CAAC;QAExD;;;;;;;WAOG;QACkC,cAAS,GAAG,EAAE,CAAC;QAEpD;;;;;;;;;WASG;QACS,UAAK,GAAG,EAAE,CAAC;QAEvB;;;WAGG;QACkD,eAAU,GAAG,KAAK,CAAC;QAExE;;;;;;;WAOG;QACuC,aAAQ,GAAG,KAAK,CAAC;QAE3D;;WAEG;QACS,UAAK,GAAG,EAAE,CAAC;QAEvB;;WAEG;QACmC,eAAU,GAAG,EAAE,CAAC;QAEtD;;WAEG;QACmC,eAAU,GAAG,EAAE,CAAC;QAEtD;;WAEG;QAEH,mBAAc,GAAG,KAAK,CAAC;QAEvB;;WAEG;QAEH,oBAAe,GAAG,KAAK,CAAC;QAExB;;;WAGG;QACuC,mBAAc,GAAG,EAAE,CAAC;QAE9D;;;WAGG;QACsC,kBAAa,GAAG,EAAE,CAAC;QAE5D;;;WAGG;QACuB,SAAI,GAAG,CAAC,CAAC;QAEnC;;;WAGG;QACuB,SAAI,GAAG,EAAE,CAAC;QAEpC,qBAAqB;QACe,cAAS,GAAG,EAAE,CAAC;QAEnD;;;;WAIG;QACS,QAAG,GAAG,EAAE,CAAC;QAErB;;;;;WAKG;QACuB,cAAS,GAAG,CAAC,CAAC,CAAC;QAEzC;;;;WAIG;QACS,QAAG,GAAG,EAAE,CAAC;QAErB;;;;;WAKG;QACuB,cAAS,GAAG,CAAC,CAAC,CAAC;QAEzC;;WAEG;QACiD,cAAS,GAAG,KAAK,CAAC;QAEtE;;;;;WAKG;QACS,YAAO,GAAG,EAAE,CAAC;QAEzB;;;;;;;WAOG;QACoD,gBAAW,GAAG,EAAE,CAAC;QAExE;;;;;WAKG;QACuC,aAAQ,GAAG,KAAK,CAAC;QAE3D;;;;WAIG;QACuC,aAAQ,GAAG,KAAK,CAAC;QAgC3D;;;;;WAKG;QACS,SAAI,GAAG,EAAE,CAAC;QAEtB;;;;;;;;;;;;;;;;;;WAkBG;QAEH,SAAI,GAA6C,MAAM,CAAC;QAExD;;;;;WAKG;QACwB,iBAAY,GAAG,EAAE,CAAC;QA8C7C;;;WAGG;QACc,UAAK,GAAG,KAAK,CAAC;QACd,YAAO,GAAG,KAAK,CAAC;QACjC;;WAEG;QACc,gBAAW,GAAG,KAAK,CAAC;QACrC;;;WAGG;QACc,oBAAe,GAAG,EAAE,CAAC;IA8ZxC,CAAC;IA5hBC;;OAEG;IACH,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,CAAC;IACtD,CAAC;IACD,IAAI,kBAAkB,CAAC,KAA6C;QAClE,IAAI,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,YAAY,CAAC;IAChD,CAAC;IACD,IAAI,YAAY,CAAC,KAAoB;QACnC,IAAI,CAAC,kBAAkB,EAAE,CAAC,YAAY,GAAG,KAAK,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,cAAc,CAAC;IAClD,CAAC;IACD,IAAI,cAAc,CAAC,KAAoB;QACrC,IAAI,CAAC,kBAAkB,EAAE,CAAC,cAAc,GAAG,KAAK,CAAC;IACnD,CAAC;IAwCD;;OAEG;IACH,IAAI,aAAa;QACf,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,GAAG,CAAC;QACb,CAAC;QAED,OAAO,KAAK,CAAC,aAAa,CAAC;IAC7B,CAAC;IACD,IAAI,aAAa,CAAC,KAAa;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QAED,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC,WAAW,CAAC;IAC3B,CAAC;IACD,IAAI,WAAW,CAAC,KAAkB;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QAED,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAC3B,CAAC;IAoBD,IAAY,QAAQ;QAClB,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC;IACxC,CAAC;IAaD;;;;OAIG;IACH,MAAM;QACJ,IAAI,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,CAAC;IACrC,CAAC;IAcD,YAAY,CAAC,GAAG,IAAe;QAC7B,uEAAuE;QACvE,8DAA8D;QAC9D,IAAI,CAAC,kBAAkB,EAAE,CAAC,YAAY,CACpC,GAAI,IAAqD,CAC1D,CAAC;QACF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,KAAK,CAAC;IAC/C,CAAC;IAED;;;;;;;;OAQG;IACH,iBAAiB,CACf,KAAoB,EACpB,GAAkB,EAClB,SAA2C;QAE3C,IAAI,CAAC,kBAAkB,EAAE,CAAC,iBAAiB,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;IACrE,CAAC;IAED;;;;;;;OAOG;IACH,UAAU;QACR,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QAED,KAAK,CAAC,UAAU,EAAE,CAAC;IACrB,CAAC;IAED;;;;;;;OAOG;IACH,QAAQ,CAAC,aAAsB;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QAED,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC9B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAC3B,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,aAAsB;QAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QAED,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9C,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;IAC5B,CAAC;IAEQ,wBAAwB,CAC/B,SAAiB,EACjB,QAAuB,EACvB,QAAuB;QAEvB,IAAI,SAAS,KAAK,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACxC,uEAAuE;YACvE,0EAA0E;YAC1E,OAAO;QACT,CAAC;QAED,KAAK,CAAC,wBAAwB,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAChE,CAAC;IAEkB,MAAM;QACvB,MAAM,OAAO,GAAG;YACd,UAAU,EAAE,IAAI,CAAC,QAAQ;YACzB,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;YACxC,UAAU,EAAE,IAAI,CAAC,IAAI,KAAK,UAAU;YACpC,YAAY,EAAE,IAAI,CAAC,SAAS;SAC7B,CAAC;QAEF,OAAO,IAAI,CAAA;gCACiB,QAAQ,CAAC,OAAO,CAAC;UACvC,IAAI,CAAC,WAAW,EAAE;;KAEvB,CAAC;IACJ,CAAC;IAEkB,OAAO,CAAC,iBAAiC;QAC1D,4DAA4D;QAE5D,uEAAuE;QACvE,4DAA4D;QAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,KAAK,CAAC;QAC9C,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;YACzB,qEAAqE;YACrE,wEAAwE;YACxE,6BAA6B;YAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACrB,CAAC;IACH,CAAC;IAEO,WAAW;QACjB,OAAO,UAAU,CAAA,IAAI,IAAI,CAAC,QAAQ;;cAExB,IAAI,CAAC,KAAK,CAAC,MAAM;kBACb,IAAI,CAAC,QAAQ;eAChB,IAAI,CAAC,QAAQ;mBACT,IAAI,CAAC,YAAY,EAAE;iBACrB,IAAI,CAAC,OAAO;iBACZ,IAAI,CAAC,eAAe;mBAClB,IAAI,CAAC,cAAc;cACxB,IAAI,CAAC,KAAK;qBACH,IAAI,CAAC,UAAU;YACxB,IAAI,CAAC,SAAS;mBACP,CAAC,CAAC,IAAI,CAAC,KAAK;kBACb,IAAI,CAAC,QAAQ;mBACZ,IAAI,CAAC,IAAI,KAAK,UAAU;wBACnB,IAAI,CAAC,cAAc;;QAEnC,IAAI,CAAC,iBAAiB,EAAE;QACxB,IAAI,CAAC,qBAAqB,EAAE;QAC5B,IAAI,CAAC,kBAAkB,EAAE;;;QAGzB,IAAI,CAAC,QAAQ,GAAG,CAAC;IACvB,CAAC;IAEO,iBAAiB;QACvB,OAAO,IAAI,CAAA;;gDAEiC,IAAI,CAAC,gBAAgB;;KAEhE,CAAC;IACJ,CAAC;IAEO,kBAAkB;QACxB,OAAO,IAAI,CAAA;;iDAEkC,IAAI,CAAC,gBAAgB;;KAEjE,CAAC;IACJ,CAAC;IAEO,qBAAqB;QAC3B,MAAM,KAAK,GAAc,EAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAC,CAAC;QAC3D,MAAM,SAAS,GACZ,IAAwB,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;QAC/D,mDAAmD;QACnD,kCAAkC;QAClC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAmB,CAAC;QAE9C,uEAAuE;QACvE,qCAAqC;QACrC,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACjD,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACjD,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAA;;;kBAGC,QAAQ,CAAC,KAAK,CAAC;;yBAER,IAAI,CAAC,QAAQ;uBACf,SAAS;yBACP,YAAY,IAAI,OAAO;iBAC/B,IAAI,CAAC,IAAI,IAAI,OAAO;sBACf,IAAI,CAAC,QAAQ;sBACb,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO;sBACvC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO;wBACrC,IAAI,CAAC,WAAW,IAAI,OAAO;sBAC7B,IAAI,CAAC,QAAQ;sBACb,IAAI,CAAC,QAAQ;iBAClB,IAAI,CAAC,IAAI;iBACT,IAAI,CAAC,IAAI;mBACP,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;oBACf,IAAI,CAAC,eAAe;mBACrB,IAAI,CAAC,iBAAiB;kBACvB,IAAI,CAAC,iBAAiB;mBACrB,IAAI,CAAC,WAAW;oBACf,IAAI,CAAC,eAAe;OACjC,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACnC,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEnC,yEAAyE;QACzE,oBAAoB;QACpB,kCAAkC;QAClC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAgB,CAAC;QACxC,OAAO,IAAI,CAAA;;UAEL,MAAM;;;kBAGE,QAAQ,CAAC,KAAK,CAAC;;yBAER,IAAI,CAAC,QAAQ;uBACf,SAAS;yBACP,YAAY,IAAI,OAAO;iBAC/B,IAAI,CAAC,IAAI,IAAI,OAAO;sBACf,IAAI,CAAC,QAAQ;sBACb,SAAS,IAAI,OAAO;gBAC1B,CAAC,IAAI,CAAC,GAAG,IAAI,OAAO,CAAsB;sBACpC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO;gBAC7C,CAAC,IAAI,CAAC,GAAG,IAAI,OAAO,CAAsB;sBACpC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO;oBACzC,IAAI,CAAC,OAAO,IAAI,OAAO;wBACnB,IAAI,CAAC,WAAW,IAAI,OAAO;sBAC7B,IAAI,CAAC,QAAQ;sBACb,IAAI,CAAC,QAAQ;sBACb,IAAI,CAAC,QAAQ;iBAClB,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,CAAsB;iBAC3C,IAAI,CAAC,IAAI;mBACP,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;oBACf,IAAI,CAAC,eAAe;mBACrB,IAAI,CAAC,iBAAiB;kBACvB,IAAI,CAAC,iBAAiB;mBACrB,IAAI,CAAC,WAAW;oBACf,IAAI,CAAC,eAAe;UAC9B,MAAM;;KAEX,CAAC;IACJ,CAAC;IAEO,YAAY;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,KAAK,CAAC,CAAC;IACjE,CAAC;IAEO,YAAY;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC;IAChE,CAAC;IAEO,WAAW,CAAC,IAAY,EAAE,QAAiB;QACjD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,MAAM,OAAO,GAAG;YACd,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE,CAAC,QAAQ;SACpB,CAAC;QAEF,OAAO,IAAI,CAAA,gBAAgB,QAAQ,CAAC,OAAO,CAAC,KAAK,IAAI,SAAS,CAAC;IACjE,CAAC;IAEO,YAAY;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;IAC5D,CAAC;IAEO,iBAAiB;QACvB,wEAAwE;QACxE,mEAAmE;QACnE,0EAA0E;QAC1E,sCAAsC;QACtC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC;IAClE,CAAC;IAEO,WAAW,CAAC,KAAiB;QACnC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,KAAK,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,CAAC;IACxD,CAAC;IAEO,eAAe,CAAC,KAAY;QAClC,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC/B,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,yDAAyD;YACzD,OAAO;YACP,sEAAsE;YACtE,wCAAwC;YACxC,6CAA6C;YAC7C,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,yEAAyE;YACzE,qEAAqE;YACrE,0DAA0D;YAC1D,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC;QAED,OAAO,IAAI,CAAC,eAAgB,CAAC;IAC/B,CAAC;IAEO,QAAQ;QACd,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,kBAAkB,EAAsB,CAAC;IACvD,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;QACnD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;IACvD,CAAC;IAMQ,CAAC,YAAY,CAAC;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAEQ,iBAAiB;QACxB,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAEQ,wBAAwB,CAAC,KAAa;QAC7C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAEQ,KAAK;QACZ,yEAAyE;QACzE,2EAA2E;QAC3E,IAAI,CAAC,kBAAkB,EAAE,CAAC,KAAK,EAAE,CAAC;IACpC,CAAC;IAEQ,CAAC,eAAe,CAAC;QACxB,OAAO,IAAI,kBAAkB,CAAC,GAAG,EAAE,CAAC,CAAC;YACnC,KAAK,EAAE,IAAI;YACX,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC,CAAC,CAAC;IACN,CAAC;IAEQ,CAAC,iBAAiB,CAAC;QAC1B,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAEQ,CAAC,gBAAgB,CAAC,CAAC,YAA0B;QACpD,mCAAmC;QACnC,YAAY,EAAE,cAAc,EAAE,CAAC;QAE/B,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACxC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,YAAY,CAAC;QAClC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAE9C,IAAI,WAAW,KAAK,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YACxC,IAAI,CAAC,KAAK,EAAE,eAAe,EAAE,CAAC;QAChC,CAAC;IACH,CAAC;;AAzsBD,kBAAkB;AACF,2BAAiB,GAAmB;IAClD,GAAG,UAAU,CAAC,iBAAiB;IAC/B,cAAc,EAAE,IAAI;CACrB,AAHgC,CAG/B;AAQwC;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;wCAAe;AAUnB;IAApC,QAAQ,CAAC,EAAC,SAAS,EAAE,YAAY,EAAC,CAAC;4CAAgB;AAYxC;IAAX,QAAQ,EAAE;wCAAY;AAM8B;IAApD,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,aAAa,EAAC,CAAC;6CAAoB;AAU9B;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;2CAAkB;AAK/C;IAAX,QAAQ,EAAE;wCAAY;AAKe;IAArC,QAAQ,CAAC,EAAC,SAAS,EAAE,aAAa,EAAC,CAAC;6CAAiB;AAKhB;IAArC,QAAQ,CAAC,EAAC,SAAS,EAAE,aAAa,EAAC,CAAC;6CAAiB;AAMtD;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAC,CAAC;iDAClC;AAMvB;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,mBAAmB,EAAC,CAAC;kDAClC;AAMkB;IAAzC,QAAQ,CAAC,EAAC,SAAS,EAAE,iBAAiB,EAAC,CAAC;iDAAqB;AAMrB;IAAxC,QAAQ,CAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAC;gDAAoB;AAMlC;IAAzB,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;uCAAU;AAMT;IAAzB,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;uCAAW;AAGA;IAAnC,QAAQ,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;4CAAyB;AAOvC;IAAX,QAAQ,EAAE;sCAAU;AAQK;IAAzB,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;4CAAgB;AAO7B;IAAX,QAAQ,EAAE;sCAAU;AAQK;IAAzB,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;4CAAgB;AAKW;IAAnD,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAC,CAAC;4CAAmB;AAQ1D;IAAX,QAAQ,EAAE;0CAAc;AAU8B;IAAtD,QAAQ,CAAC,EAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAC,CAAC;8CAAkB;AAQ9B;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;2CAAkB;AAOjB;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;2CAAkB;AAsC/C;IAAX,QAAQ,EAAE;uCAAW;AAsBtB;IADC,QAAQ,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;uCAC8B;AAQ7B;IAA1B,QAAQ,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;+CAAmB;AAkD5B;IAAhB,KAAK,EAAE;wCAAuB;AACd;IAAhB,KAAK,EAAE;0CAAyB;AAIhB;IAAhB,KAAK,EAAE;8CAA6B;AAKpB;IAAhB,KAAK,EAAE;kDAA8B;AAOrB;IADhB,KAAK,CAAC,QAAQ,CAAC;kDAIP;AACyB;IAAjC,KAAK,CAAC,QAAQ,CAAC;wCAAuC;AAEtC;IADhB,qBAAqB,CAAC,EAAC,IAAI,EAAE,cAAc,EAAC,CAAC;+CACJ;AAEzB;IADhB,qBAAqB,CAAC,EAAC,IAAI,EAAE,eAAe,EAAC,CAAC;gDACJ", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {LitElement, PropertyValues, html, nothing} from 'lit';\nimport {property, query, queryAssignedElements, state} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\nimport {live} from 'lit/directives/live.js';\nimport {StyleInfo, styleMap} from 'lit/directives/style-map.js';\nimport {StaticValue, html as staticHtml} from 'lit/static-html.js';\n\nimport {Field} from '../../field/internal/field.js';\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\nimport {stringConverter} from '../../internal/controller/string-converter.js';\nimport {redispatchEvent} from '../../internal/events/redispatch-event.js';\nimport {\n  createValidator,\n  getValidityAnchor,\n  mixinConstraintValidation,\n} from '../../labs/behaviors/constraint-validation.js';\nimport {mixinElementInternals} from '../../labs/behaviors/element-internals.js';\nimport {\n  getFormValue,\n  mixinFormAssociated,\n} from '../../labs/behaviors/form-associated.js';\nimport {\n  mixinOnReportValidity,\n  onReportValidity,\n} from '../../labs/behaviors/on-report-validity.js';\nimport {TextFieldValidator} from '../../labs/behaviors/validators/text-field-validator.js';\nimport {Validator} from '../../labs/behaviors/validators/validator.js';\n\n/**\n * Input types that are compatible with the text field.\n */\nexport type TextFieldType =\n  | 'email'\n  | 'number'\n  | 'password'\n  | 'search'\n  | 'tel'\n  | 'text'\n  | 'url'\n  | 'textarea';\n\n/**\n * Input types that are not fully supported for the text field.\n */\nexport type UnsupportedTextFieldType =\n  | 'color'\n  | 'date'\n  | 'datetime-local'\n  | 'file'\n  | 'month'\n  | 'time'\n  | 'week';\n\n/**\n * Input types that are incompatible with the text field.\n */\nexport type InvalidTextFieldType =\n  | 'button'\n  | 'checkbox'\n  | 'hidden'\n  | 'image'\n  | 'radio'\n  | 'range'\n  | 'reset'\n  | 'submit';\n\n// Separate variable needed for closure.\nconst textFieldBaseClass = mixinDelegatesAria(\n  mixinOnReportValidity(\n    mixinConstraintValidation(\n      mixinFormAssociated(mixinElementInternals(LitElement)),\n    ),\n  ),\n);\n\n/**\n * A text field component.\n *\n * @fires select {Event} The native `select` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/select_event)\n * --bubbles\n * @fires change {Event} The native `change` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/change_event)\n * --bubbles\n * @fires input {InputEvent} The native `input` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/input_event)\n * --bubbles --composed\n */\nexport abstract class TextField extends textFieldBaseClass {\n  /** @nocollapse */\n  static override shadowRootOptions: ShadowRootInit = {\n    ...LitElement.shadowRootOptions,\n    delegatesFocus: true,\n  };\n\n  /**\n   * Gets or sets whether or not the text field is in a visually invalid state.\n   *\n   * This error state overrides the error state controlled by\n   * `reportValidity()`.\n   */\n  @property({type: Boolean, reflect: true}) error = false;\n\n  /**\n   * The error message that replaces supporting text when `error` is true. If\n   * `errorText` is an empty string, then the supporting text will continue to\n   * show.\n   *\n   * This error message overrides the error message displayed by\n   * `reportValidity()`.\n   */\n  @property({attribute: 'error-text'}) errorText = '';\n\n  /**\n   * The floating Material label of the textfield component. It informs the user\n   * about what information is requested for a text field. It is aligned with\n   * the input text, is always visible, and it floats when focused or when text\n   * is entered into the textfield. This label also sets accessibilty labels,\n   * but the accessible label is overriden by `aria-label`.\n   *\n   * Learn more about floating labels from the Material Design guidelines:\n   * https://m3.material.io/components/text-fields/guidelines\n   */\n  @property() label = '';\n\n  /**\n   * Disables the asterisk on the floating label, when the text field is\n   * required.\n   */\n  @property({type: Boolean, attribute: 'no-asterisk'}) noAsterisk = false;\n\n  /**\n   * Indicates that the user must specify a value for the input before the\n   * owning form can be submitted and will render an error state when\n   * `reportValidity()` is invoked when value is empty. Additionally the\n   * floating label will render an asterisk `\"*\"` when true.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/required\n   */\n  @property({type: Boolean, reflect: true}) required = false;\n\n  /**\n   * The current value of the text field. It is always a string.\n   */\n  @property() value = '';\n\n  /**\n   * An optional prefix to display before the input value.\n   */\n  @property({attribute: 'prefix-text'}) prefixText = '';\n\n  /**\n   * An optional suffix to display after the input value.\n   */\n  @property({attribute: 'suffix-text'}) suffixText = '';\n\n  /**\n   * Whether or not the text field has a leading icon. Used for SSR.\n   */\n  @property({type: Boolean, attribute: 'has-leading-icon'})\n  hasLeadingIcon = false;\n\n  /**\n   * Whether or not the text field has a trailing icon. Used for SSR.\n   */\n  @property({type: Boolean, attribute: 'has-trailing-icon'})\n  hasTrailingIcon = false;\n\n  /**\n   * Conveys additional information below the text field, such as how it should\n   * be used.\n   */\n  @property({attribute: 'supporting-text'}) supportingText = '';\n\n  /**\n   * Override the input text CSS `direction`. Useful for RTL languages that use\n   * LTR notation for fractions.\n   */\n  @property({attribute: 'text-direction'}) textDirection = '';\n\n  /**\n   * The number of rows to display for a `type=\"textarea\"` text field.\n   * Defaults to 2.\n   */\n  @property({type: Number}) rows = 2;\n\n  /**\n   * The number of cols to display for a `type=\"textarea\"` text field.\n   * Defaults to 20.\n   */\n  @property({type: Number}) cols = 20;\n\n  // <input> properties\n  @property({reflect: true}) override inputMode = '';\n\n  /**\n   * Defines the greatest value in the range of permitted values.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#max\n   */\n  @property() max = '';\n\n  /**\n   * The maximum number of characters a user can enter into the text field. Set\n   * to -1 for none.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#maxlength\n   */\n  @property({type: Number}) maxLength = -1;\n\n  /**\n   * Defines the most negative value in the range of permitted values.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#min\n   */\n  @property() min = '';\n\n  /**\n   * The minimum number of characters a user can enter into the text field. Set\n   * to -1 for none.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#minlength\n   */\n  @property({type: Number}) minLength = -1;\n\n  /**\n   * When true, hide the spinner for `type=\"number\"` text fields.\n   */\n  @property({type: Boolean, attribute: 'no-spinner'}) noSpinner = false;\n\n  /**\n   * A regular expression that the text field's value must match to pass\n   * constraint validation.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#pattern\n   */\n  @property() pattern = '';\n\n  /**\n   * Defines the text displayed in the textfield when it has no value. Provides\n   * a brief hint to the user as to the expected type of data that should be\n   * entered into the control. Unlike `label`, the placeholder is not visible\n   * and does not float when the textfield has a value.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/placeholder\n   */\n  @property({reflect: true, converter: stringConverter}) placeholder = '';\n\n  /**\n   * Indicates whether or not a user should be able to edit the text field's\n   * value.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#readonly\n   */\n  @property({type: Boolean, reflect: true}) readOnly = false;\n\n  /**\n   * Indicates that input accepts multiple email addresses.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/email#multiple\n   */\n  @property({type: Boolean, reflect: true}) multiple = false;\n\n  /**\n   * Gets or sets the direction in which selection occurred.\n   */\n  get selectionDirection() {\n    return this.getInputOrTextarea().selectionDirection;\n  }\n  set selectionDirection(value: 'forward' | 'backward' | 'none' | null) {\n    this.getInputOrTextarea().selectionDirection = value;\n  }\n\n  /**\n   * Gets or sets the end position or offset of a text selection.\n   */\n  get selectionEnd() {\n    return this.getInputOrTextarea().selectionEnd;\n  }\n  set selectionEnd(value: number | null) {\n    this.getInputOrTextarea().selectionEnd = value;\n  }\n\n  /**\n   * Gets or sets the starting position or offset of a text selection.\n   */\n  get selectionStart() {\n    return this.getInputOrTextarea().selectionStart;\n  }\n  set selectionStart(value: number | null) {\n    this.getInputOrTextarea().selectionStart = value;\n  }\n\n  /**\n   * Returns or sets the element's step attribute, which works with min and max\n   * to limit the increments at which a numeric or date-time value can be set.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#step\n   */\n  @property() step = '';\n\n  /**\n   * The `<input>` type to use, defaults to \"text\". The type greatly changes how\n   * the text field behaves.\n   *\n   * Text fields support a limited number of `<input>` types:\n   *\n   * - text\n   * - textarea\n   * - email\n   * - number\n   * - password\n   * - search\n   * - tel\n   * - url\n   *\n   * See\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#input_types\n   * for more details on each input type.\n   */\n  @property({reflect: true})\n  type: TextFieldType | UnsupportedTextFieldType = 'text';\n\n  /**\n   * Describes what, if any, type of autocomplete functionality the input\n   * should provide.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/autocomplete\n   */\n  @property({reflect: true}) autocomplete = '';\n\n  /**\n   * The text field's value as a number.\n   */\n  get valueAsNumber() {\n    const input = this.getInput();\n    if (!input) {\n      return NaN;\n    }\n\n    return input.valueAsNumber;\n  }\n  set valueAsNumber(value: number) {\n    const input = this.getInput();\n    if (!input) {\n      return;\n    }\n\n    input.valueAsNumber = value;\n    this.value = input.value;\n  }\n\n  /**\n   * The text field's value as a Date.\n   */\n  get valueAsDate() {\n    const input = this.getInput();\n    if (!input) {\n      return null;\n    }\n\n    return input.valueAsDate;\n  }\n  set valueAsDate(value: Date | null) {\n    const input = this.getInput();\n    if (!input) {\n      return;\n    }\n\n    input.valueAsDate = value;\n    this.value = input.value;\n  }\n\n  protected abstract readonly fieldTag: StaticValue;\n\n  /**\n   * Returns true when the text field has been interacted with. Native\n   * validation errors only display in response to user interactions.\n   */\n  @state() private dirty = false;\n  @state() private focused = false;\n  /**\n   * Whether or not a native error has been reported via `reportValidity()`.\n   */\n  @state() private nativeError = false;\n  /**\n   * The validation message displayed from a native error via\n   * `reportValidity()`.\n   */\n  @state() private nativeErrorText = '';\n\n  private get hasError() {\n    return this.error || this.nativeError;\n  }\n\n  @query('.input')\n  private readonly inputOrTextarea!:\n    | HTMLInputElement\n    | HTMLTextAreaElement\n    | null;\n  @query('.field') private readonly field!: Field | null;\n  @queryAssignedElements({slot: 'leading-icon'})\n  private readonly leadingIcons!: Element[];\n  @queryAssignedElements({slot: 'trailing-icon'})\n  private readonly trailingIcons!: Element[];\n\n  /**\n   * Selects all the text in the text field.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/select\n   */\n  select() {\n    this.getInputOrTextarea().select();\n  }\n\n  /**\n   * Replaces a range of text with a new string.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setRangeText\n   */\n  setRangeText(replacement: string): void;\n  setRangeText(\n    replacement: string,\n    start: number,\n    end: number,\n    selectionMode?: SelectionMode,\n  ): void;\n  setRangeText(...args: unknown[]) {\n    // Calling setRangeText with 1 vs 3-4 arguments has different behavior.\n    // Use spread syntax and type casting to ensure correct usage.\n    this.getInputOrTextarea().setRangeText(\n      ...(args as Parameters<HTMLInputElement['setRangeText']>),\n    );\n    this.value = this.getInputOrTextarea().value;\n  }\n\n  /**\n   * Sets the start and end positions of a selection in the text field.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange\n   *\n   * @param start The offset into the text field for the start of the selection.\n   * @param end The offset into the text field for the end of the selection.\n   * @param direction The direction in which the selection is performed.\n   */\n  setSelectionRange(\n    start: number | null,\n    end: number | null,\n    direction?: 'forward' | 'backward' | 'none',\n  ) {\n    this.getInputOrTextarea().setSelectionRange(start, end, direction);\n  }\n\n  /**\n   * Shows the browser picker for an input element of type \"date\", \"time\", etc.\n   *\n   * For a full list of supported types, see:\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/showPicker#browser_compatibility\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/showPicker\n   */\n  showPicker() {\n    const input = this.getInput();\n    if (!input) {\n      return;\n    }\n\n    input.showPicker();\n  }\n\n  /**\n   * Decrements the value of a numeric type text field by `step` or `n` `step`\n   * number of times.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/stepDown\n   *\n   * @param stepDecrement The number of steps to decrement, defaults to 1.\n   */\n  stepDown(stepDecrement?: number) {\n    const input = this.getInput();\n    if (!input) {\n      return;\n    }\n\n    input.stepDown(stepDecrement);\n    this.value = input.value;\n  }\n\n  /**\n   * Increments the value of a numeric type text field by `step` or `n` `step`\n   * number of times.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/stepUp\n   *\n   * @param stepIncrement The number of steps to increment, defaults to 1.\n   */\n  stepUp(stepIncrement?: number) {\n    const input = this.getInput();\n    if (!input) {\n      return;\n    }\n\n    input.stepUp(stepIncrement);\n    this.value = input.value;\n  }\n\n  /**\n   * Reset the text field to its default value.\n   */\n  reset() {\n    this.dirty = false;\n    this.value = this.getAttribute('value') ?? '';\n    this.nativeError = false;\n    this.nativeErrorText = '';\n  }\n\n  override attributeChangedCallback(\n    attribute: string,\n    newValue: string | null,\n    oldValue: string | null,\n  ) {\n    if (attribute === 'value' && this.dirty) {\n      // After user input, changing the value attribute no longer updates the\n      // text field's value (until reset). This matches native <input> behavior.\n      return;\n    }\n\n    super.attributeChangedCallback(attribute, newValue, oldValue);\n  }\n\n  protected override render() {\n    const classes = {\n      'disabled': this.disabled,\n      'error': !this.disabled && this.hasError,\n      'textarea': this.type === 'textarea',\n      'no-spinner': this.noSpinner,\n    };\n\n    return html`\n      <span class=\"text-field ${classMap(classes)}\">\n        ${this.renderField()}\n      </span>\n    `;\n  }\n\n  protected override updated(changedProperties: PropertyValues) {\n    // Keep changedProperties arg so that subclasses may call it\n\n    // If a property such as `type` changes and causes the internal <input>\n    // value to change without dispatching an event, re-sync it.\n    const value = this.getInputOrTextarea().value;\n    if (this.value !== value) {\n      // Note this is typically inefficient in updated() since it schedules\n      // another update. However, it is needed for the <input> to fully render\n      // before checking its value.\n      this.value = value;\n    }\n  }\n\n  private renderField() {\n    return staticHtml`<${this.fieldTag}\n      class=\"field\"\n      count=${this.value.length}\n      ?disabled=${this.disabled}\n      ?error=${this.hasError}\n      error-text=${this.getErrorText()}\n      ?focused=${this.focused}\n      ?has-end=${this.hasTrailingIcon}\n      ?has-start=${this.hasLeadingIcon}\n      label=${this.label}\n      ?no-asterisk=${this.noAsterisk}\n      max=${this.maxLength}\n      ?populated=${!!this.value}\n      ?required=${this.required}\n      ?resizable=${this.type === 'textarea'}\n      supporting-text=${this.supportingText}\n    >\n      ${this.renderLeadingIcon()}\n      ${this.renderInputOrTextarea()}\n      ${this.renderTrailingIcon()}\n      <div id=\"description\" slot=\"aria-describedby\"></div>\n      <slot name=\"container\" slot=\"container\"></slot>\n    </${this.fieldTag}>`;\n  }\n\n  private renderLeadingIcon() {\n    return html`\n      <span class=\"icon leading\" slot=\"start\">\n        <slot name=\"leading-icon\" @slotchange=${this.handleIconChange}></slot>\n      </span>\n    `;\n  }\n\n  private renderTrailingIcon() {\n    return html`\n      <span class=\"icon trailing\" slot=\"end\">\n        <slot name=\"trailing-icon\" @slotchange=${this.handleIconChange}></slot>\n      </span>\n    `;\n  }\n\n  private renderInputOrTextarea() {\n    const style: StyleInfo = {'direction': this.textDirection};\n    const ariaLabel =\n      (this as ARIAMixinStrict).ariaLabel || this.label || nothing;\n    // lit-anaylzer `autocomplete` types are too strict\n    // tslint:disable-next-line:no-any\n    const autocomplete = this.autocomplete as any;\n\n    // These properties may be set to null if the attribute is removed, and\n    // `null > -1` is incorrectly `true`.\n    const hasMaxLength = (this.maxLength ?? -1) > -1;\n    const hasMinLength = (this.minLength ?? -1) > -1;\n    if (this.type === 'textarea') {\n      return html`\n        <textarea\n          class=\"input\"\n          style=${styleMap(style)}\n          aria-describedby=\"description\"\n          aria-invalid=${this.hasError}\n          aria-label=${ariaLabel}\n          autocomplete=${autocomplete || nothing}\n          name=${this.name || nothing}\n          ?disabled=${this.disabled}\n          maxlength=${hasMaxLength ? this.maxLength : nothing}\n          minlength=${hasMinLength ? this.minLength : nothing}\n          placeholder=${this.placeholder || nothing}\n          ?readonly=${this.readOnly}\n          ?required=${this.required}\n          rows=${this.rows}\n          cols=${this.cols}\n          .value=${live(this.value)}\n          @change=${this.redispatchEvent}\n          @focus=${this.handleFocusChange}\n          @blur=${this.handleFocusChange}\n          @input=${this.handleInput}\n          @select=${this.redispatchEvent}></textarea>\n      `;\n    }\n\n    const prefix = this.renderPrefix();\n    const suffix = this.renderSuffix();\n\n    // TODO(b/243805848): remove `as unknown as number` and `as any` once lit\n    // analyzer is fixed\n    // tslint:disable-next-line:no-any\n    const inputMode = this.inputMode as any;\n    return html`\n      <div class=\"input-wrapper\">\n        ${prefix}\n        <input\n          class=\"input\"\n          style=${styleMap(style)}\n          aria-describedby=\"description\"\n          aria-invalid=${this.hasError}\n          aria-label=${ariaLabel}\n          autocomplete=${autocomplete || nothing}\n          name=${this.name || nothing}\n          ?disabled=${this.disabled}\n          inputmode=${inputMode || nothing}\n          max=${(this.max || nothing) as unknown as number}\n          maxlength=${hasMaxLength ? this.maxLength : nothing}\n          min=${(this.min || nothing) as unknown as number}\n          minlength=${hasMinLength ? this.minLength : nothing}\n          pattern=${this.pattern || nothing}\n          placeholder=${this.placeholder || nothing}\n          ?readonly=${this.readOnly}\n          ?required=${this.required}\n          ?multiple=${this.multiple}\n          step=${(this.step || nothing) as unknown as number}\n          type=${this.type}\n          .value=${live(this.value)}\n          @change=${this.redispatchEvent}\n          @focus=${this.handleFocusChange}\n          @blur=${this.handleFocusChange}\n          @input=${this.handleInput}\n          @select=${this.redispatchEvent} />\n        ${suffix}\n      </div>\n    `;\n  }\n\n  private renderPrefix() {\n    return this.renderAffix(this.prefixText, /* isSuffix */ false);\n  }\n\n  private renderSuffix() {\n    return this.renderAffix(this.suffixText, /* isSuffix */ true);\n  }\n\n  private renderAffix(text: string, isSuffix: boolean) {\n    if (!text) {\n      return nothing;\n    }\n\n    const classes = {\n      'suffix': isSuffix,\n      'prefix': !isSuffix,\n    };\n\n    return html`<span class=\"${classMap(classes)}\">${text}</span>`;\n  }\n\n  private getErrorText() {\n    return this.error ? this.errorText : this.nativeErrorText;\n  }\n\n  private handleFocusChange() {\n    // When calling focus() or reportValidity() during change, it's possible\n    // for blur to be called after the new focus event. Rather than set\n    // `this.focused` to true/false on focus/blur, we always set it to whether\n    // or not the input itself is focused.\n    this.focused = this.inputOrTextarea?.matches(':focus') ?? false;\n  }\n\n  private handleInput(event: InputEvent) {\n    this.dirty = true;\n    this.value = (event.target as HTMLInputElement).value;\n  }\n\n  private redispatchEvent(event: Event) {\n    redispatchEvent(this, event);\n  }\n\n  private getInputOrTextarea() {\n    if (!this.inputOrTextarea) {\n      // If the input is not yet defined, synchronously render.\n      // e.g.\n      // const textField = document.createElement('md-outlined-text-field');\n      // document.body.appendChild(textField);\n      // textField.focus(); // synchronously render\n      this.connectedCallback();\n      this.scheduleUpdate();\n    }\n\n    if (this.isUpdatePending) {\n      // If there are pending updates, synchronously perform them. This ensures\n      // that constraint validation properties (like `required`) are synced\n      // before interacting with input APIs that depend on them.\n      this.scheduleUpdate();\n    }\n\n    return this.inputOrTextarea!;\n  }\n\n  private getInput() {\n    if (this.type === 'textarea') {\n      return null;\n    }\n\n    return this.getInputOrTextarea() as HTMLInputElement;\n  }\n\n  private handleIconChange() {\n    this.hasLeadingIcon = this.leadingIcons.length > 0;\n    this.hasTrailingIcon = this.trailingIcons.length > 0;\n  }\n\n  // Writable mixin properties for lit-html binding, needed for lit-analyzer\n  declare disabled: boolean;\n  declare name: string;\n\n  override [getFormValue]() {\n    return this.value;\n  }\n\n  override formResetCallback() {\n    this.reset();\n  }\n\n  override formStateRestoreCallback(state: string) {\n    this.value = state;\n  }\n\n  override focus() {\n    // Required for the case that the user slots a focusable element into the\n    // leading icon slot such as an iconbutton due to how delegatesFocus works.\n    this.getInputOrTextarea().focus();\n  }\n\n  override [createValidator](): Validator<unknown> {\n    return new TextFieldValidator(() => ({\n      state: this,\n      renderedControl: this.inputOrTextarea,\n    }));\n  }\n\n  override [getValidityAnchor](): HTMLElement | null {\n    return this.inputOrTextarea;\n  }\n\n  override [onReportValidity](invalidEvent: Event | null) {\n    // Prevent default pop-up behavior.\n    invalidEvent?.preventDefault();\n\n    const prevMessage = this.getErrorText();\n    this.nativeError = !!invalidEvent;\n    this.nativeErrorText = this.validationMessage;\n\n    if (prevMessage === this.getErrorText()) {\n      this.field?.reannounceError();\n    }\n  }\n}\n"]}