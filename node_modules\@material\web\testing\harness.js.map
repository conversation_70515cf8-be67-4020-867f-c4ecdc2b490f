{"version": 3, "file": "harness.js", "sourceRoot": "", "sources": ["harness.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EACL,6BAA6B,EAC7B,yBAAyB,EACzB,sBAAsB,GACvB,MAAM,+BAA+B,CAAC;AAgCvC;;;;;;GAMG;AACH,MAAM,UAAU,oBAAoB,CAClC,OAAgB;IAEhB,OAAQ,OAAyC,CAAC,OAAO,YAAY,OAAO,CAAC;AAC/E,CAAC;AAED;;;;;GAKG;AACH,MAAM,OAAO,OAAO;IAkBlB;;;;OAIG;IACH,YAAY,OAAU;QAtBtB;;;WAGG;QACO,2BAAsB,GAAG,6BAA6B,CAAC;QAOjE;;;WAGG;QACc,oBAAe,GAAG,IAAI,OAAO,EAAe,CAAC;QAQ5D,IAAI,CAAC,OAAO,GAAG,OAAsC,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACtD,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE;gBACnC,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,cAAc,CAAC,OAAyB,EAAE;QAC9C,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QACrC,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,mBAAmB,CAAC,OAAyB,EAAE;QACnD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACzC,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,iBAAiB,CAAC,OAAyB,EAAE;QACjD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,kDAAkD;YAClD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,iBAAiB,CAAC,OAA0B,EAAE;QAClD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACxC,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QACtC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,sBAAsB,CAAC,OAA0B,EAAE;QACvD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,oBAAoB,CAAC,OAA0B,EAAE;QACrD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE,IAAI,CAAC,CAAC;QACnD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,MAAM,gBAAgB,GAAG,EAAC,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAC,CAAC;QACjD,MAAM,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QACjD,+DAA+D;QAC/D,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;IACtD,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,GAAG,CAAC,OAAyB,EAAE,EAAE,YAA4B,EAAE;QACnE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAClD,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;YACvC,uDAAuD;YACvD,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,QAAQ,CAAC,OAAyB,EAAE,EAAE,YAA4B,EAAE;QACxE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;IACpD,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,mBAAmB,CAAC,OAAuB,EAAE;QACjD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,MAAM,CAAC,OAAyB,EAAE,EAAE,YAA4B,EAAE;QACtE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;IACtD,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,WAAW,CAAC,OAAyB,EAAE;QAC3C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;YAC1B,WAAW,EAAE,OAAO;YACpB,GAAG,IAAI;SACR,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,SAAS,CAAC,OAAyB,EAAE,EAAE,YAA4B,EAAE;QACzE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,iBAAiB,CAAC,OAA0B,EAAE;QAClD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QACpB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,QAAQ,CAAC,GAAW,EAAE,OAA0B,EAAE;QACtD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnD,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;;OAMG;IACH,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI;QACjC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,IAAI,QAAQ,EAAE,CAAC;QACxB,CAAC;QACD,OAAO,IAAI,OAAO,CAAW,CAAC,OAAO,EAAE,EAAE;YACvC,MAAM,cAAc,GAAG,CAAC,KAAkB,EAAE,EAAE;gBAC5C,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAChC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACd,OAAO,KAAK,CAAC;YACf,CAAC,CAAC;YAEF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,cAAc,EAAE,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC;YAC9D,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACO,KAAK,CAAC,qBAAqB;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;;;;;OAOG;IACO,cAAc,CAAC,OAAoB,EAAE,WAAmB;QAChE,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACvD,OAAO;QACT,CAAC;QAED,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,EAA2B,CAAC;QAC5D,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,sBAAsB,CACpB,OAAO,CAAC,UAAU,CAAC,kBAAkB,IAAI,EAAE,EAC3C,IAAI,CAAC,sBAAsB,CAC5B,CAAC;QACJ,CAAC;QAED,sBAAsB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACtE,sBAAsB,CACpB,IAAI,CAAC,kBAAkB,IAAI,EAAE,EAC7B,IAAI,CAAC,sBAAsB,CAC5B,CAAC;QACF,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;IAED;;;;;OAKG;IACO,iBAAiB,CAAC,OAAoB,EAAE,WAAmB;QACnE,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC,CAAC;IACnE,CAAC;IAED;;;;;OAKG;IACO,aAAa,CAAC,OAAoB,EAAE,OAAuB,EAAE;QACrE,wEAAwE;QACxE,4EAA4E;QAC5E,OAAO,CAAC,aAAa,CACnB,IAAI,UAAU,CAAC,OAAO,EAAE;YACtB,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACrC,GAAG,IAAI;SACR,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACO,mBAAmB,CAC3B,OAAoB,EACpB,OAAuB,EAAE;QAEzB,OAAO,CAAC,aAAa,CACnB,IAAI,UAAU,CAAC,aAAa,EAAE;YAC5B,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACrC,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,CAAC;YACV,GAAG,IAAI;SACR,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACO,qBAAqB,CAAC,OAAoB;QAClD,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QACnD,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAC/C,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACnC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACO,oBAAoB,CAAC,OAAoB;QACjD,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACvC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE;YACnC,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,OAAO,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;QACjE,OAAO,CAAC,aAAa,CACnB,IAAI,UAAU,CAAC,SAAS,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAC3D,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACO,YAAY,CAAC,OAAoB;QACzC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAClD,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE;YACnC,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,MAAM,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;QAChE,OAAO,CAAC,aAAa,CACnB,IAAI,UAAU,CAAC,UAAU,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAC5D,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACO,kBAAkB,CAC1B,OAAoB,EACpB,OAAyB,EAAE;QAE3B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE;YACnC,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QACH,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACrD,MAAM,cAAc,GAAG;YACrB,GAAG,SAAS;YACZ,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,IAAI,CAAC,IAAI;YAClB,OAAO,EAAE,IAAI,CAAC,GAAG;YACjB,OAAO,EAAE,IAAI,CAAC,IAAI;YAClB,OAAO,EAAE,IAAI,CAAC,GAAG;SAClB,CAAC;QAEF,MAAM,WAAW,GAAG;YAClB,GAAG,SAAS;YACZ,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,OAAO;SACrB,CAAC;QAEF,MAAM,gBAAgB,GAAqB;YACzC,GAAG,WAAW;YACd,GAAG,cAAc;YACjB,GAAG,IAAI;SACR,CAAC;QAEF,OAAO,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;QACpE,OAAO,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC,CAAC;QAC1E,OAAO,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;QAC9D,OAAO,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;IACtE,CAAC;IAED;;;;;OAKG;IACO,gBAAgB,CACxB,OAAoB,EACpB,OAAyB,EAAE;QAE3B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE;YACnC,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QACH,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACrD,MAAM,cAAc,GAAG;YACrB,GAAG,SAAS;YACZ,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;YACtB,OAAO,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC;YACrB,OAAO,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;YACtB,OAAO,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC;SACtB,CAAC;QAEF,MAAM,WAAW,GAAqB;YACpC,GAAG,SAAS;YACZ,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,OAAO;YACpB,GAAG,IAAI;SACR,CAAC;QAEF,MAAM,gBAAgB,GAAqB;YACzC,GAAG,WAAW;YACd,GAAG,cAAc;SAClB,CAAC;QAEF,OAAO,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC;QACnE,OAAO,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC,CAAC;QAC1E,OAAO,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,CAAC;QAC/D,OAAO,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;IACtE,CAAC;IAED;;;;;OAKG;IACO,kBAAkB,CAC1B,OAAoB,EACpB,OAAyB,EAAE;QAE3B,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACxC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE;YACnC,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACrD,MAAM,WAAW,GAAqB;YACpC,GAAG,SAAS;YACZ,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,OAAO;YACpB,GAAG,IAAI;SACR,CAAC;QAEF,OAAO,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;QACpE,OAAO,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAED;;;;;OAKG;IACO,oBAAoB,CAC5B,OAAoB,EACpB,OAAyB,EAAE;QAE3B,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC3C,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE;YACnC,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QACH,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACrD,MAAM,WAAW,GAAqB;YACpC,GAAG,SAAS;YACZ,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,OAAO;YACpB,GAAG,IAAI;SACR,CAAC;QAEF,OAAO,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC;QAClE,OAAO,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACO,kBAAkB,CAC1B,OAAoB,EACpB,OAAyB,EAAE,EAC3B,YAA4B,EAAE;QAE9B,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACxC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE;YACnC,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACrD,MAAM,WAAW,GAAqB;YACpC,GAAG,SAAS;YACZ,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,OAAO;YACpB,GAAG,IAAI;SACR,CAAC;QAEF,OAAO,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;QACpE,kDAAkD;QAClD,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACxC,OAAO,CAAC,aAAa,CACnB,IAAI,UAAU,CAAC,YAAY,EAAE;gBAC3B,OAAO,EAAE,CAAC,KAAK,CAAC;gBAChB,aAAa,EAAE,CAAC,KAAK,CAAC;gBACtB,cAAc,EAAE,CAAC,KAAK,CAAC;gBACvB,GAAG,SAAS;aACb,CAAC,CACH,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAED;;;;;OAKG;IACO,oBAAoB,CAC5B,OAAoB,EACpB,OAAyB,EAAE,EAC3B,YAA4B,EAAE;QAE9B,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC3C,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE;YACnC,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QACH,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACrD,MAAM,WAAW,GAAqB;YACpC,GAAG,SAAS;YACZ,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,OAAO;YACpB,GAAG,IAAI;SACR,CAAC;QAEF,OAAO,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC;QAClE,kDAAkD;QAClD,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACxC,OAAO,CAAC,aAAa,CACnB,IAAI,UAAU,CAAC,UAAU,EAAE,EAAC,cAAc,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,SAAS,EAAC,CAAC,CACpE,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACO,mBAAmB,CAC3B,OAAoB,EACpB,OAAyB,EAAE,EAC3B,YAA4B,EAAE;QAE9B,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC3C,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE;YACnC,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QACH,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACrD,MAAM,WAAW,GAAqB;YACpC,GAAG,SAAS;YACZ,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,OAAO;YACpB,GAAG,IAAI;SACR,CAAC;QAEF,OAAO,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC;QACtE,kDAAkD;QAClD,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACxC,OAAO,CAAC,aAAa,CACnB,IAAI,UAAU,CAAC,aAAa,EAAE,EAAC,cAAc,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,SAAS,EAAC,CAAC,CACvE,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACO,gBAAgB,CACxB,OAAoB,EACpB,GAAW,EACX,OAA0B,EAAE;QAE5B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IACzC,CAAC;IAED;;;;;;OAMG;IACO,eAAe,CACvB,OAAoB,EACpB,GAAW,EACX,OAA0B,EAAE;QAE5B,OAAO,CAAC,aAAa,CACnB,IAAI,aAAa,CAAC,SAAS,EAAE;YAC3B,GAAG,IAAI;YACP,GAAG;YACH,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;YACd,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACO,aAAa,CACrB,OAAoB,EACpB,GAAW,EACX,OAA0B,EAAE;QAE5B,OAAO,CAAC,aAAa,CACnB,IAAI,aAAa,CAAC,OAAO,EAAE;YACzB,GAAG,IAAI;YACP,GAAG;YACH,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;YACd,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACO,oBAAoB,CAAC,OAAoB;QACjD,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAC7C,OAAO;YACL,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YACrC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACrC,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YACrC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACrC,2CAA2C;YAC3C,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,CAAC;SACX,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACO,WAAW,CAAC,OAAoB,EAAE,UAAU,GAAG,CAAC;QACxD,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAC7C,OAAO,IAAI,KAAK,CAAC;YACf,UAAU;YACV,MAAM,EAAE,OAAO;YACf,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YACrC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACrC,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YACrC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACrC,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YACnC,KAAK,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACnC,SAAS,EAAE,QAAQ;SACpB,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IACO,eAAe,CACvB,KAAkB,EAClB,QAAqC,EACrC,SAAsB,IAAI,CAAC,OAAO;QAElC,IAAI,QAAQ,GAAgB,KAAK,CAAC;QAClC,OAAO,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;YAC7C,MAAM,WAAW,GAAS,QAAQ,CAAC;YACnC,QAAQ,GAAG,WAAW,CAAC,UAAU,IAAK,WAA0B,CAAC,IAAI,CAAC;YAEtE,IAAI,CAAC,CAAC,WAAW,YAAY,WAAW,CAAC,EAAE,CAAC;gBAC1C,SAAS;YACX,CAAC;YAED,QAAQ,CAAC,WAAW,CAAC,CAAC;YAEtB,IAAI,QAAQ,YAAY,WAAW,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBAC3D,MAAM,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBAC9C,MAAM,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,aAAa,IAAI,GAAG,CAAC,CAAC,CAAC,kBAAkB,CAAC;gBACtE,MAAM,WAAW,GACf,QAAQ,CAAC,UAAU,CAAC,aAAa,CAAkB,YAAY,CAAC,CAAC;gBACnE,IAAI,WAAW,EAAE,CAAC;oBAChB,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACxD,CAAC;YACH,CAAC;QACH,CAAC;QAED,QAAQ,CAAC,MAAM,CAAC,CAAC;IACnB,CAAC;IAED;;;;;;;;OAQG;IACO,gCAAgC,CAAC,OAAoB;QAC7D,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACtC,OAAO;QACT,CAAC;QAED,8CAA8C;QAC9C,MAAM,WAAW,GAAG,CAAC,QAAgB,EAAE,EAAE;YACvC,IAAI,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACnD,OAAO,IAAI,yBAAyB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnD,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC;QAEF,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QAC1C,OAAO,CAAC,OAAO,GAAG,CAAC,QAAgB,EAAE,EAAE;YACrC,OAAO,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC;QAEF,MAAM,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;QACtD,OAAO,CAAC,aAAa,GAAG,CAAC,QAAgB,EAAE,EAAE;YAC3C,OAAO,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC;QAEF,MAAM,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC;QAC5D,OAAO,CAAC,gBAAgB,GAAG,CAAC,QAAgB,EAAE,EAAE;YAC9C,OAAO,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {\n  defaultTransformPseudoClasses,\n  getTransformedPseudoClass,\n  transformPseudoClasses,\n} from './transform-pseudo-classes.js';\n\n/**\n * Retrieves the element type from a `Harness` type.\n *\n * @template H The harness type.\n */\nexport type HarnessElement<H extends Harness> = H extends Harness<infer E>\n  ? ElementWithHarness<E, H>\n  : never;\n\n/**\n * Harnesses will attach themselves to their element for convenience.\n *\n * @template E The element type.\n * @template H The harness type.\n */\nexport type ElementWithHarness<\n  E extends HTMLElement = HTMLElement,\n  H extends Harness<E> = Harness<E>,\n> = E & {\n  /**\n   * The harness for this element.\n   */\n  harness: H;\n\n  /**\n   * Associated form element.\n   */\n  form?: HTMLFormElement | null;\n};\n\n/**\n * Checks whether or not an element has a Harness attached to it on the\n * `element.harness` property.\n *\n * @param element The element to check.\n * @return True if the element has a harness property.\n */\nexport function isElementWithHarness(\n  element: Element,\n): element is ElementWithHarness {\n  return (element as unknown as ElementWithHarness).harness instanceof Harness;\n}\n\n/**\n * A test harness class that can be used to simulate interaction with an\n * element.\n *\n * @template E The harness's element type.\n */\nexport class Harness<E extends HTMLElement = HTMLElement> {\n  /**\n   * The pseudo classes that should be transformed for simulation. Component\n   * subclasses may override this to add additional pseudo classes.\n   */\n  protected transformPseudoClasses = defaultTransformPseudoClasses;\n\n  /**\n   * The element that this harness controls.\n   */\n  readonly element: E & ElementWithHarness<E, this>;\n\n  /**\n   * A set of elements that have already been patched to support transformed\n   * pseudo classes.\n   */\n  private readonly patchedElements = new WeakSet<HTMLElement>();\n\n  /**\n   * Creates a new harness for the given element.\n   *\n   * @param element The element that this harness controls.\n   */\n  constructor(element: E) {\n    this.element = element as ElementWithHarness<E, this>;\n    this.element.harness = this;\n  }\n\n  /**\n   * Resets the element's simulated classes to the default state.\n   */\n  async reset() {\n    const element = await this.getInteractiveElement();\n    for (const pseudoClass of this.transformPseudoClasses) {\n      this.forEachNodeFrom(element, (el) => {\n        this.removePseudoClass(el, pseudoClass);\n      });\n    }\n  }\n\n  /**\n   * Hovers and clicks on an element. This will generate a `click` event.\n   *\n   * @param init Additional event options.\n   */\n  async clickWithMouse(init: PointerEventInit = {}) {\n    await this.startClickWithMouse(init);\n    await this.endClickWithMouse(init);\n  }\n\n  /**\n   * Begins a click with a mouse. Use this along with `endClickWithMouse()` to\n   * customize the length of the click.\n   *\n   * @param init Additional event options.\n   */\n  async startClickWithMouse(init: PointerEventInit = {}) {\n    const element = await this.getInteractiveElement();\n    await this.startHover();\n    this.simulateMousePress(element, init);\n  }\n\n  /**\n   * Finishes a click with a mouse. Use this along with `startClickWithMouse()`\n   * to customize the length of the click. This will generate a `click` event.\n   *\n   * @param init Additional event options.\n   */\n  async endClickWithMouse(init: PointerEventInit = {}) {\n    const element = await this.getInteractiveElement();\n    this.simulateMouseRelease(element, init);\n    if ((init?.button ?? 0) === 0) {\n      // Dispatch a click for left-click only (default).\n      this.simulateClick(element, init);\n    }\n  }\n\n  /**\n   * Clicks an element with the keyboard (defaults to spacebar). This will\n   * generate a `click` event.\n   *\n   * @param init Additional event options.\n   */\n  async clickWithKeyboard(init: KeyboardEventInit = {}) {\n    const element = await this.getInteractiveElement();\n    await this.startClickWithKeyboard(init);\n    await this.endClickWithKeyboard(init);\n    this.simulateClick(element, init);\n  }\n\n  /**\n   * Begins a click with the keyboard (defaults to spacebar). Use this along\n   * with `endClickWithKeyboard()` to customize the length of the click.\n   *\n   * @param init Additional event options.\n   */\n  async startClickWithKeyboard(init: KeyboardEventInit = {}) {\n    const element = await this.getInteractiveElement();\n    await this.focusWithKeyboard(init);\n    this.simulateKeydown(element, init.key ?? ' ', init);\n    this.simulateClick(element, init);\n  }\n\n  /**\n   * Finishes a click with the keyboard (defaults to spacebar). Use this along\n   * with `startClickWithKeyboard()` to customize the length of the click.\n   *\n   * @param init Additional event options.\n   */\n  async endClickWithKeyboard(init: KeyboardEventInit = {}) {\n    const element = await this.getInteractiveElement();\n    this.simulateKeyup(element, init.key ?? ' ', init);\n    this.simulateClick(element, init);\n  }\n\n  /**\n   * Right-clicks and opens a context menu. This will generate a `contextmenu`\n   * event.\n   */\n  async rightClickWithMouse() {\n    const element = await this.getInteractiveElement();\n    const rightMouseButton = {button: 2, buttons: 2};\n    await this.startClickWithMouse(rightMouseButton);\n    // Note: contextmenu right clicks do not generate the up events\n    this.simulateContextmenu(element, rightMouseButton);\n  }\n\n  /**\n   * Taps once on the element with a simulated touch. This will generate a\n   * `click` event.\n   *\n   * @param init Additional event options.\n   * @param touchInit Additional touch event options.\n   */\n  async tap(init: PointerEventInit = {}, touchInit: TouchEventInit = {}) {\n    const element = await this.getInteractiveElement();\n    this.simulateTouchPress(element, init, touchInit);\n    this.simulateTouchRelease(element, init, touchInit);\n    if ((init?.isPrimary ?? true) === true) {\n      // Dispatch a click for primary touches only (default).\n      await this.endTapClick(init);\n    }\n  }\n\n  /**\n   * Begins a touch tap. Use this along with `endTap()` to customize the length\n   * or number of taps.\n   *\n   * @param init Additional event options.\n   * @param touchInit Additional touch event options.\n   */\n  async startTap(init: PointerEventInit = {}, touchInit: TouchEventInit = {}) {\n    const element = await this.getInteractiveElement();\n    this.simulateTouchPress(element, init, touchInit);\n  }\n\n  /**\n   * Simulates a `contextmenu` event for touch. Use this along with `startTap()`\n   * to generate a tap-and-hold context menu interaction.\n   *\n   * @param init Additional event options.\n   */\n  async startTapContextMenu(init: MouseEventInit = {}) {\n    const element = await this.getInteractiveElement();\n    this.simulateContextmenu(element, init);\n  }\n\n  /**\n   * Finished a touch tap. Use this along with `startTap()` to customize the\n   * length or number of taps.\n   *\n   * This will NOT generate a `click` event.\n   *\n   * @param init Additional event options.\n   * @param touchInit Additional touch event options.\n   */\n  async endTap(init: PointerEventInit = {}, touchInit: TouchEventInit = {}) {\n    const element = await this.getInteractiveElement();\n    this.simulateTouchRelease(element, init, touchInit);\n  }\n\n  /**\n   * Simulates a `click` event for touch. Use this along with `endTap()` to\n   * control the timing of tap and click events.\n   *\n   * @param init Additional event options.\n   */\n  async endTapClick(init: PointerEventInit = {}) {\n    const element = await this.getInteractiveElement();\n    this.simulateClick(element, {\n      pointerType: 'touch',\n      ...init,\n    });\n  }\n\n  /**\n   * Cancels a touch tap.\n   *\n   * @param init Additional event options.\n   * @param touchInit Additional touch event options.\n   */\n  async cancelTap(init: PointerEventInit = {}, touchInit: TouchEventInit = {}) {\n    const element = await this.getInteractiveElement();\n    this.simulateTouchCancel(element, init, touchInit);\n  }\n\n  /**\n   * Hovers over the element with a simulated mouse.\n   */\n  async startHover() {\n    const element = await this.getInteractiveElement();\n    this.simulateStartHover(element);\n  }\n\n  /**\n   * Moves the simulated mouse cursor off of the element.\n   */\n  async endHover() {\n    const element = await this.getInteractiveElement();\n    this.simulateEndHover(element);\n  }\n\n  /**\n   * Simulates focusing an element with the keyboard.\n   *\n   * @param init Additional event options.\n   */\n  async focusWithKeyboard(init: KeyboardEventInit = {}) {\n    const element = await this.getInteractiveElement();\n    this.simulateKeyboardFocus(element);\n  }\n\n  /**\n   * Simulates focusing an element with a pointer.\n   */\n  async focusWithPointer() {\n    const element = await this.getInteractiveElement();\n    await this.startHover();\n    this.simulatePointerFocus(element);\n  }\n\n  /**\n   * Simulates unfocusing an element.\n   */\n  async blur() {\n    const element = await this.getInteractiveElement();\n    await this.endHover();\n    this.simulateBlur(element);\n  }\n\n  /**\n   * Simulates a keypress on an element.\n   *\n   * @param key The key to press.\n   * @param init Additional event options.\n   */\n  async keypress(key: string, init: KeyboardEventInit = {}) {\n    const element = await this.getInteractiveElement();\n    this.simulateKeypress(element, key, init);\n  }\n\n  /**\n   * Simulates submitting the element's associated form element.\n   *\n   * @param form (Optional) form to submit, defaults to the elemnt's form.\n   * @return The submitted form data or null if the element has no associated\n   * form.\n   */\n  submitForm(form = this.element.form) {\n    if (!form) {\n      return new FormData();\n    }\n    return new Promise<FormData>((resolve) => {\n      const submitListener = (event: SubmitEvent) => {\n        event.preventDefault();\n        const data = new FormData(form);\n        resolve(data);\n        return false;\n      };\n\n      form.addEventListener('submit', submitListener, {once: true});\n      form.requestSubmit();\n    });\n  }\n\n  /**\n   * Returns the element that should be used for interaction simulation.\n   * Defaults to the host element itself.\n   *\n   * Subclasses should override this if the interactive element is not the host.\n   *\n   * @return The element to use in simulation.\n   */\n  protected async getInteractiveElement(): Promise<HTMLElement> {\n    return this.element;\n  }\n\n  /**\n   * Adds a pseudo class to an element. The element's shadow root styles (or\n   * document if not in a shadow root) will be transformed to support\n   * simulated pseudo classes.\n   *\n   * @param element The element to add a pseudo class to.\n   * @param pseudoClass The pseudo class to add.\n   */\n  protected addPseudoClass(element: HTMLElement, pseudoClass: string) {\n    if (!this.transformPseudoClasses.includes(pseudoClass)) {\n      return;\n    }\n\n    const root = element.getRootNode() as Document | ShadowRoot;\n    if (element.shadowRoot) {\n      transformPseudoClasses(\n        element.shadowRoot.adoptedStyleSheets || [],\n        this.transformPseudoClasses,\n      );\n    }\n\n    transformPseudoClasses(root.styleSheets, this.transformPseudoClasses);\n    transformPseudoClasses(\n      root.adoptedStyleSheets || [],\n      this.transformPseudoClasses,\n    );\n    element.classList.add(getTransformedPseudoClass(pseudoClass));\n    this.patchForTransformedPseudoClasses(element);\n  }\n\n  /**\n   * Removes a pseudo class from an element.\n   *\n   * @param element The element to remove a pseudo class from.\n   * @param pseudoClass The pseudo class to remove.\n   */\n  protected removePseudoClass(element: HTMLElement, pseudoClass: string) {\n    element.classList.remove(getTransformedPseudoClass(pseudoClass));\n  }\n\n  /**\n   * Simulates a click event.\n   *\n   * @param element The element to click.\n   * @param init Additional event options.\n   */\n  protected simulateClick(element: HTMLElement, init: MouseEventInit = {}) {\n    // Firefox does not support some simulations with PointerEvents, such as\n    // selecting an <input type=\"checkbox\">. Use MouseEvent for browser support.\n    element.dispatchEvent(\n      new MouseEvent('click', {\n        ...this.createMouseEventInit(element),\n        ...init,\n      }),\n    );\n  }\n\n  /**\n   * Simulates a contextmenu event.\n   *\n   * @param element The element to generate an event for.\n   * @param init Additional event options.\n   */\n  protected simulateContextmenu(\n    element: HTMLElement,\n    init: MouseEventInit = {},\n  ) {\n    element.dispatchEvent(\n      new MouseEvent('contextmenu', {\n        ...this.createMouseEventInit(element),\n        button: 2,\n        buttons: 2,\n        ...init,\n      }),\n    );\n  }\n\n  /**\n   * Simulates focusing with a keyboard. The difference between this and\n   * `simulatePointerFocus` is that keyboard focus will include the\n   * `:focus-visible` pseudo class.\n   *\n   * @param element The element to focus with a keyboard.\n   */\n  protected simulateKeyboardFocus(element: HTMLElement) {\n    this.simulateKeydown(element.ownerDocument, 'Tab');\n    this.addPseudoClass(element, ':focus-visible');\n    this.simulatePointerFocus(element);\n    this.simulateKeyup(element, 'Tab');\n  }\n\n  /**\n   * Simulates focusing with a pointer.\n   *\n   * @param element The element to focus with a pointer.\n   */\n  protected simulatePointerFocus(element: HTMLElement) {\n    this.addPseudoClass(element, ':focus');\n    this.forEachNodeFrom(element, (el) => {\n      this.addPseudoClass(el, ':focus-within');\n    });\n    element.dispatchEvent(new FocusEvent('focus', {composed: true}));\n    element.dispatchEvent(\n      new FocusEvent('focusin', {bubbles: true, composed: true}),\n    );\n  }\n\n  /**\n   * Simulates unfocusing an element.\n   *\n   * @param element The element to blur.\n   */\n  protected simulateBlur(element: HTMLElement) {\n    this.removePseudoClass(element, ':focus');\n    this.removePseudoClass(element, ':focus-visible');\n    this.forEachNodeFrom(element, (el) => {\n      this.removePseudoClass(el, ':focus-within');\n    });\n    element.dispatchEvent(new FocusEvent('blur', {composed: true}));\n    element.dispatchEvent(\n      new FocusEvent('focusout', {bubbles: true, composed: true}),\n    );\n  }\n\n  /**\n   * Simulates a mouse pointer hovering over an element.\n   *\n   * @param element The element to hover over.\n   * @param init Additional event options.\n   */\n  protected simulateStartHover(\n    element: HTMLElement,\n    init: PointerEventInit = {},\n  ) {\n    this.forEachNodeFrom(element, (el) => {\n      this.addPseudoClass(el, ':hover');\n    });\n    const rect = element.getBoundingClientRect();\n    const mouseInit = this.createMouseEventInit(element);\n    const mouseEnterInit = {\n      ...mouseInit,\n      bubbles: false,\n      clientX: rect.left,\n      clientY: rect.top,\n      screenX: rect.left,\n      screenY: rect.top,\n    };\n\n    const pointerInit = {\n      ...mouseInit,\n      isPrimary: true,\n      pointerType: 'mouse',\n    };\n\n    const pointerEnterInit: PointerEventInit = {\n      ...pointerInit,\n      ...mouseEnterInit,\n      ...init,\n    };\n\n    element.dispatchEvent(new PointerEvent('pointerover', pointerInit));\n    element.dispatchEvent(new PointerEvent('pointerenter', pointerEnterInit));\n    element.dispatchEvent(new MouseEvent('mouseover', mouseInit));\n    element.dispatchEvent(new MouseEvent('mouseenter', mouseEnterInit));\n  }\n\n  /**\n   * Simulates a mouse pointer leaving the element.\n   *\n   * @param element The element to stop hovering over.\n   * @param init Additional event options.\n   */\n  protected simulateEndHover(\n    element: HTMLElement,\n    init: PointerEventInit = {},\n  ) {\n    this.forEachNodeFrom(element, (el) => {\n      this.removePseudoClass(el, ':hover');\n    });\n    const rect = element.getBoundingClientRect();\n    const mouseInit = this.createMouseEventInit(element);\n    const mouseLeaveInit = {\n      ...mouseInit,\n      bubbles: false,\n      clientX: rect.left - 1,\n      clientY: rect.top - 1,\n      screenX: rect.left - 1,\n      screenY: rect.top - 1,\n    };\n\n    const pointerInit: PointerEventInit = {\n      ...mouseInit,\n      isPrimary: true,\n      pointerType: 'mouse',\n      ...init,\n    };\n\n    const pointerLeaveInit: PointerEventInit = {\n      ...pointerInit,\n      ...mouseLeaveInit,\n    };\n\n    element.dispatchEvent(new PointerEvent('pointerout', pointerInit));\n    element.dispatchEvent(new PointerEvent('pointerleave', pointerLeaveInit));\n    element.dispatchEvent(new MouseEvent('pointerout', mouseInit));\n    element.dispatchEvent(new MouseEvent('mouseleave', mouseLeaveInit));\n  }\n\n  /**\n   * Simulates a mouse press and hold on an element.\n   *\n   * @param element The element to press with a mouse.\n   * @param init Additional event options.\n   */\n  protected simulateMousePress(\n    element: HTMLElement,\n    init: PointerEventInit = {},\n  ) {\n    this.addPseudoClass(element, ':active');\n    this.forEachNodeFrom(element, (el) => {\n      this.addPseudoClass(el, ':active');\n    });\n    const mouseInit = this.createMouseEventInit(element);\n    const pointerInit: PointerEventInit = {\n      ...mouseInit,\n      isPrimary: true,\n      pointerType: 'mouse',\n      ...init,\n    };\n\n    element.dispatchEvent(new PointerEvent('pointerdown', pointerInit));\n    element.dispatchEvent(new MouseEvent('mousedown', mouseInit));\n    this.simulatePointerFocus(element);\n  }\n\n  /**\n   * Simulates a mouse press release from an element.\n   *\n   * @param element The element to release pressing from.\n   * @param init Additional event options.\n   */\n  protected simulateMouseRelease(\n    element: HTMLElement,\n    init: PointerEventInit = {},\n  ) {\n    this.removePseudoClass(element, ':active');\n    this.forEachNodeFrom(element, (el) => {\n      this.removePseudoClass(el, ':active');\n    });\n    const mouseInit = this.createMouseEventInit(element);\n    const pointerInit: PointerEventInit = {\n      ...mouseInit,\n      isPrimary: true,\n      pointerType: 'mouse',\n      ...init,\n    };\n\n    element.dispatchEvent(new PointerEvent('pointerup', pointerInit));\n    element.dispatchEvent(new MouseEvent('mouseup', mouseInit));\n  }\n\n  /**\n   * Simulates a touch press and hold on an element.\n   *\n   * @param element The element to press with a touch pointer.\n   * @param init Additional event options.\n   */\n  protected simulateTouchPress(\n    element: HTMLElement,\n    init: PointerEventInit = {},\n    touchInit: TouchEventInit = {},\n  ) {\n    this.addPseudoClass(element, ':active');\n    this.forEachNodeFrom(element, (el) => {\n      this.addPseudoClass(el, ':active');\n    });\n    const mouseInit = this.createMouseEventInit(element);\n    const pointerInit: PointerEventInit = {\n      ...mouseInit,\n      isPrimary: true,\n      pointerType: 'touch',\n      ...init,\n    };\n\n    element.dispatchEvent(new PointerEvent('pointerdown', pointerInit));\n    // Firefox does not support TouchEvent constructor\n    if (window.TouchEvent) {\n      const touch = this.createTouch(element);\n      element.dispatchEvent(\n        new TouchEvent('touchstart', {\n          touches: [touch],\n          targetTouches: [touch],\n          changedTouches: [touch],\n          ...touchInit,\n        }),\n      );\n    }\n    this.simulatePointerFocus(element);\n  }\n\n  /**\n   * Simulates a touch press release from an element.\n   *\n   * @param element The element to release pressing from.\n   * @param init Additional event options.\n   */\n  protected simulateTouchRelease(\n    element: HTMLElement,\n    init: PointerEventInit = {},\n    touchInit: TouchEventInit = {},\n  ) {\n    this.removePseudoClass(element, ':active');\n    this.forEachNodeFrom(element, (el) => {\n      this.removePseudoClass(el, ':active');\n    });\n    const mouseInit = this.createMouseEventInit(element);\n    const pointerInit: PointerEventInit = {\n      ...mouseInit,\n      isPrimary: true,\n      pointerType: 'touch',\n      ...init,\n    };\n\n    element.dispatchEvent(new PointerEvent('pointerup', pointerInit));\n    // Firefox does not support TouchEvent constructor\n    if (window.TouchEvent) {\n      const touch = this.createTouch(element);\n      element.dispatchEvent(\n        new TouchEvent('touchend', {changedTouches: [touch], ...touchInit}),\n      );\n    }\n  }\n\n  /**\n   * Simulates a touch cancel from an element.\n   *\n   * @param element The element to cancel a touch for.\n   * @param init Additional event options.\n   */\n  protected simulateTouchCancel(\n    element: HTMLElement,\n    init: PointerEventInit = {},\n    touchInit: TouchEventInit = {},\n  ) {\n    this.removePseudoClass(element, ':active');\n    this.forEachNodeFrom(element, (el) => {\n      this.removePseudoClass(el, ':active');\n    });\n    const mouseInit = this.createMouseEventInit(element);\n    const pointerInit: PointerEventInit = {\n      ...mouseInit,\n      isPrimary: true,\n      pointerType: 'touch',\n      ...init,\n    };\n\n    element.dispatchEvent(new PointerEvent('pointercancel', pointerInit));\n    // Firefox does not support TouchEvent constructor\n    if (window.TouchEvent) {\n      const touch = this.createTouch(element);\n      element.dispatchEvent(\n        new TouchEvent('touchcancel', {changedTouches: [touch], ...touchInit}),\n      );\n    }\n  }\n\n  /**\n   * Simulates a keypress on an element.\n   *\n   * @param element The element to press a key on.\n   * @param key The key to press.\n   * @param init Additional event options.\n   */\n  protected simulateKeypress(\n    element: EventTarget,\n    key: string,\n    init: KeyboardEventInit = {},\n  ) {\n    this.simulateKeydown(element, key, init);\n    this.simulateKeyup(element, key, init);\n  }\n\n  /**\n   * Simulates a keydown press on an element.\n   *\n   * @param element The element to press a key on.\n   * @param key The key to press.\n   * @param init Additional event options.\n   */\n  protected simulateKeydown(\n    element: EventTarget,\n    key: string,\n    init: KeyboardEventInit = {},\n  ) {\n    element.dispatchEvent(\n      new KeyboardEvent('keydown', {\n        ...init,\n        key,\n        bubbles: true,\n        composed: true,\n        cancelable: true,\n      }),\n    );\n  }\n\n  /**\n   * Simulates a keyup release from an element.\n   *\n   * @param element The element to release a key from.\n   * @param key The key to release.\n   * @param init Additional keyboard options.\n   */\n  protected simulateKeyup(\n    element: EventTarget,\n    key: string,\n    init: KeyboardEventInit = {},\n  ) {\n    element.dispatchEvent(\n      new KeyboardEvent('keyup', {\n        ...init,\n        key,\n        bubbles: true,\n        composed: true,\n        cancelable: true,\n      }),\n    );\n  }\n\n  /**\n   * Creates a MouseEventInit for an element. The default x/y coordinates of the\n   * event init will be in the center of the element.\n   *\n   * @param element The element to create a `MouseEventInit` for.\n   * @return The init object for a `MouseEvent`.\n   */\n  protected createMouseEventInit(element: HTMLElement): MouseEventInit {\n    const rect = element.getBoundingClientRect();\n    return {\n      bubbles: true,\n      cancelable: true,\n      composed: true,\n      clientX: (rect.left + rect.right) / 2,\n      clientY: (rect.top + rect.bottom) / 2,\n      screenX: (rect.left + rect.right) / 2,\n      screenY: (rect.top + rect.bottom) / 2,\n      // Primary button (usually the left button)\n      button: 0,\n      buttons: 1,\n    };\n  }\n\n  /**\n   * Creates a Touch instance for an element. The default x/y coordinates of the\n   * touch will be in the center of the element. This can be used in the\n   * `TouchEvent` constructor.\n   *\n   * @param element The element to create a touch for.\n   * @param identifier Optional identifier for the touch. Defaults to 0 for\n   *     every touch instance.\n   * @return The `Touch` instance.\n   */\n  protected createTouch(element: HTMLElement, identifier = 0): Touch {\n    const rect = element.getBoundingClientRect();\n    return new Touch({\n      identifier,\n      target: element,\n      clientX: (rect.left + rect.right) / 2,\n      clientY: (rect.top + rect.bottom) / 2,\n      screenX: (rect.left + rect.right) / 2,\n      screenY: (rect.top + rect.bottom) / 2,\n      pageX: (rect.left + rect.right) / 2,\n      pageY: (rect.top + rect.bottom) / 2,\n      touchType: 'direct',\n    });\n  }\n\n  /**\n   * Visit each node up the parent tree from the given child until reaching the\n   * given parent.\n   *\n   * This is used to perform logic such as adding/removing recursive pseudo\n   * classes like `:hover`.\n   *\n   * @param child The first child element to start from.\n   * @param callback A callback that is invoked with each `HTMLElement` node\n   *     from the child to the parent.\n   * @param parent The last parent element to visit.\n   */\n  protected forEachNodeFrom(\n    child: HTMLElement,\n    callback: (node: HTMLElement) => void,\n    parent: HTMLElement = this.element,\n  ) {\n    let nextNode: Node | null = child;\n    while (nextNode && nextNode !== this.element) {\n      const currentNode: Node = nextNode;\n      nextNode = currentNode.parentNode || (currentNode as ShadowRoot).host;\n\n      if (!(currentNode instanceof HTMLElement)) {\n        continue;\n      }\n\n      callback(currentNode);\n\n      if (nextNode instanceof HTMLElement && nextNode.shadowRoot) {\n        const slot = currentNode.getAttribute('slot');\n        const slotSelector = slot ? `slot[name=${slot}]` : 'slot:not([name])';\n        const slotElement =\n          nextNode.shadowRoot.querySelector<HTMLSlotElement>(slotSelector);\n        if (slotElement) {\n          this.forEachNodeFrom(slotElement, callback, nextNode);\n        }\n      }\n    }\n\n    callback(parent);\n  }\n\n  /**\n   * Patch an element's methods, such as `querySelector` and `matches` to\n   * handle transformed pseudo classes.\n   *\n   * For example, `element.matches(':focus')` will return true when the\n   * `._focus` class is applied.\n   *\n   * @param element The element to patch.\n   */\n  protected patchForTransformedPseudoClasses(element: HTMLElement) {\n    if (this.patchedElements.has(element)) {\n      return;\n    }\n\n    // Patch functions to handle pseudo selectors.\n    const getSelector = (selector: string) => {\n      if (this.transformPseudoClasses.includes(selector)) {\n        return `.${getTransformedPseudoClass(selector)}`;\n      }\n\n      return selector;\n    };\n\n    const superMatches = this.element.matches;\n    element.matches = (selector: string) => {\n      return superMatches.call(element, getSelector(selector));\n    };\n\n    const superQuerySelector = this.element.querySelector;\n    element.querySelector = (selector: string) => {\n      return superQuerySelector.call(element, getSelector(selector));\n    };\n\n    const superQuerySelectorAll = this.element.querySelectorAll;\n    element.querySelectorAll = (selector: string) => {\n      return superQuerySelectorAll.call(element, getSelector(selector));\n    };\n\n    this.patchedElements.add(element);\n  }\n}\n"]}