//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
@use 'sass:string';
// go/keep-sorted end
// go/keep-sorted start
@use './internal/validate';
@use './md-comp-list-item';
@use './md-sys-color';
@use './md-sys-elevation';
@use './md-sys-shape';
@use './md-sys-state';
@use './md-sys-typescale';
@use './v0_192/md-comp-menu';
// go/keep-sorted end

$supported-tokens: (
  // go/keep-sorted start
  'bottom-space',
  'container-color',
  'disabled-opacity',
  'hover-state-layer-color',
  'hover-state-layer-opacity',
  'label-text-color',
  'label-text-font',
  'label-text-line-height',
  'label-text-size',
  'label-text-weight',
  'leading-icon-color',
  'leading-space',
  'one-line-container-height',
  'pressed-state-layer-color',
  'pressed-state-layer-opacity',
  'selected-container-color',
  'selected-label-text-color',
  'supporting-text-color',
  'supporting-text-font',
  'supporting-text-line-height',
  'supporting-text-size',
  'supporting-text-weight',
  'top-space',
  'trailing-icon-color',
  'trailing-space',
  'trailing-supporting-text-color',
  'trailing-supporting-text-font',
  'trailing-supporting-text-line-height',
  'trailing-supporting-text-size',
  'trailing-supporting-text-weight',
  'two-line-container-height',
  // go/keep-sorted end
);

$unsupported-tokens: (
  // go/keep-sorted start
  'container-elevation',
  'container-shadow-color',
  'container-shape',
  'selected-with-leading-icon-trailing-icon-color',
  'with-leading-icon-icon-color',
  // go/keep-sorted end
);

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $tokens: md-comp-menu.values($deps);

  // Like list items, menu items use their parent menu for their container
  // color. However, menu items can have a selected background color, so we
  // change its default unselected background color to transparent to inherit
  // from its parent menu.
  $tokens: map.set($tokens, 'container-color', transparent);

  $tokens: validate.values(
    $tokens,
    $supported-tokens: $supported-tokens,
    $unsupported-tokens: $unsupported-tokens,
    $new-tokens:
      md-comp-list-item.values($deps, $exclude-custom-properties: true),
    $renamed-tokens: _get-renamed-tokens($tokens)
  );

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      $tokens: map.set(
        $tokens,
        $token,
        var(--md-menu-item-#{$token}, #{$value})
      );
    }
  }

  @return $tokens;
}

// remove list-item prefix from tokens
@function _get-renamed-tokens($tokens) {
  $keys: map.keys($tokens);
  $renamed-tokens: ();

  @each $key in $keys {
    @each $prefix in ('list-item-', 'menu-list-item-') {
      @if string.index($key, $prefix) == 1 {
        $renamed-key: string.slice($key, string.length($prefix) + 1);
        $renamed-tokens: map.set($renamed-tokens, $key, $renamed-key);
      }
    }
  }

  @return $renamed-tokens;
}
