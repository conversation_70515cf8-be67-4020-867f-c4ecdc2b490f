//
// Copyright 2022 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:list';
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use '../../focus/focus-ring';
@use '../../ripple/ripple';
@use '../../tokens';
// go/keep-sorted end

@mixin theme($tokens) {
  $supported-tokens: ltokens.$md-comp-fab-supported-tokens;
  @each $token, $value in $tokens {
    @if list.index($supported-tokens, $token) == null {
      @error 'Token `#{$token}` is not a supported token.';
    }

    @if $value {
      --md-fab-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $tokens: tokens.md-comp-fab-values();

  :host {
    // Only use the logical properties.
    $tokens: map.remove(
      $tokens,
      'container-shape',
      'large-container-shape',
      'small-container-shape'
    );

    @each $token, $value in $tokens {
      --_#{$token}: #{$value};
    }

    cursor: pointer;
  }

  :host([size='small'][touch-target='wrapper']) {
    margin: max(0px, 48px - var(--_small-container-height));
  }

  .fab {
    @include color(
      (
        'focus-icon-color': var(--_focus-icon-color),
        'hover-icon-color': var(--_hover-icon-color),
        'icon-color': var(--_icon-color),
        'pressed-icon-color': var(--_pressed-icon-color),
      )
    );

    cursor: inherit;

    &.primary {
      @include color(
        (
          'container-color': var(--_primary-container-color),
          'focus-icon-color': var(--_primary-focus-icon-color),
          'hover-icon-color': var(--_primary-hover-icon-color),
          'hover-state-layer-color': var(--_primary-hover-state-layer-color),
          'icon-color': var(--_primary-icon-color),
          'pressed-icon-color': var(--_primary-pressed-icon-color),
          'pressed-state-layer-color': var(--_primary-pressed-state-layer-color),
          'label-text-color': var(--_primary-label-text-color),
          'hover-label-text-color': var(--_primary-hover-label-text-color),
          'focus-label-text-color': var(--_primary-focus-label-text-color),
          'pressed-label-text-color': var(--_primary-pressed-label-text-color),
        )
      );
    }

    &.secondary {
      @include color(
        (
          'container-color': var(--_secondary-container-color),
          'focus-icon-color': var(--_secondary-focus-icon-color),
          'hover-icon-color': var(--_secondary-hover-icon-color),
          'hover-state-layer-color': var(--_secondary-hover-state-layer-color),
          'icon-color': var(--_secondary-icon-color),
          'pressed-icon-color': var(--_secondary-pressed-icon-color),
          'pressed-state-layer-color':
            var(--_secondary-pressed-state-layer-color),
          'label-text-color': var(--_secondary-label-text-color),
          'hover-label-text-color': var(--_secondary-hover-label-text-color),
          'focus-label-text-color': var(--_secondary-focus-label-text-color),
          'pressed-label-text-color': var(--_secondary-pressed-label-text-color),
        )
      );
    }

    &.tertiary {
      @include color(
        (
          'container-color': var(--_tertiary-container-color),
          'focus-icon-color': var(--_tertiary-focus-icon-color),
          'hover-icon-color': var(--_tertiary-hover-icon-color),
          'hover-state-layer-color': var(--_tertiary-hover-state-layer-color),
          'icon-color': var(--_tertiary-icon-color),
          'pressed-icon-color': var(--_tertiary-pressed-icon-color),
          'pressed-state-layer-color':
            var(--_tertiary-pressed-state-layer-color),
          'label-text-color': var(--_tertiary-label-text-color),
          'hover-label-text-color': var(--_tertiary-hover-label-text-color),
          'focus-label-text-color': var(--_tertiary-focus-label-text-color),
          'pressed-label-text-color': var(--_tertiary-pressed-label-text-color),
        )
      );
    }
  }

  // The default content is needed because we do a bit of trickery. If there is
  // no slotted icon we need to have `padding-inline: 20px` but if there is, we
  // need `padding-inline: 16px 20px`.
  //
  // So what this approach does is make the margin / padding-inline of the outer
  // button `padding-inline: 16px 20px`, so if there is something slotted, it
  // renders correctly. This default content span then fills the extra `4px`
  // inline spacing if nothing is slotted which effectively makes the button's
  // inline spacing `20px/20px`.
  .fab.extended slot span {
    padding-inline-start: 4px;
  }

  .fab.small {
    width: var(--_small-container-width);
    height: var(--_small-container-height);

    .icon ::slotted(*) {
      width: var(--_small-icon-size);
      height: var(--_small-icon-size);
      font-size: var(--_small-icon-size);
    }

    &,
    .ripple {
      border-start-start-radius: var(--_small-container-shape-start-start);
      border-start-end-radius: var(--_small-container-shape-start-end);
      border-end-start-radius: var(--_small-container-shape-end-start);
      border-end-end-radius: var(--_small-container-shape-end-end);
    }

    md-focus-ring {
      @include focus-ring.theme(
        (
          'shape-start-start': var(--_small-container-shape-start-start),
          'shape-start-end': var(--_small-container-shape-start-end),
          'shape-end-end': var(--_small-container-shape-end-end),
          'shape-end-start': var(--_small-container-shape-end-start),
        )
      );
    }
  }
}

@mixin color($colors) {
  background-color: map.get($colors, 'container-color');

  @include ripple.theme(
    (
      hover-color: map.get($colors, hover-state-layer-color),
      pressed-color: map.get($colors, pressed-state-layer-color),
    )
  );

  .icon ::slotted(*) {
    color: map.get($colors, icon-color);
  }

  &:focus {
    color: map.get($colors, focus-icon-color);
  }

  &:hover {
    color: map.get($colors, hover-icon-color);
  }

  &:active {
    color: map.get($colors, pressed-icon-color);
  }

  .label {
    color: map.get($colors, label-text-color);

    .fab:hover & {
      color: map.get($colors, hover-label-text-color);
    }

    .fab:focus & {
      color: map.get($colors, focus-label-text-color);
    }

    .fab:active & {
      color: map.get($colors, pressed-label-text-color);
    }
  }
}
