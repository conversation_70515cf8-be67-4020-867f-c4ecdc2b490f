{"version": 3, "file": "select-option.js", "sourceRoot": "", "sources": ["select-option.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,MAAM,EAAC,MAAM,+CAA+C,CAAC;AAErE,OAAO,EAAC,cAAc,EAAC,MAAM,0CAA0C,CAAC;AAUxE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AAEI,IAAM,cAAc,GAApB,MAAM,cAAe,SAAQ,cAAc;;AAChC,qBAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAD5C,cAAc;IAD1B,aAAa,CAAC,kBAAkB,CAAC;GACrB,cAAc,CAE1B", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {styles} from '../menu/internal/menuitem/menu-item-styles.js';\n\nimport {SelectOptionEl} from './internal/selectoption/select-option.js';\n\nexport {type SelectOption} from './internal/selectoption/select-option.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-select-option': MdSelectOption;\n  }\n}\n\n/**\n * @summary\n * Select menus display a list of choices on temporary surfaces and display the\n * currently selected menu item above the menu.\n *\n * @description\n * The select component allows users to choose a value from a fixed list of\n * available options. Composed of an interactive anchor button and a menu, it is\n * analogous to the native HTML `<select>` element. This is the option that\n * can be placed inside of an md-select.\n *\n * This component is a subclass of `md-menu-item` and can accept the same slots,\n * properties, and events as `md-menu-item`.\n *\n * @example\n * ```html\n * <md-outlined-select label=\"fruits\">\n *   <!-- An empty selected option will give select an \"un-filled\" state -->\n *   <md-select-option selected></md-select-option>\n *   <md-select-option value=\"apple\" headline=\"Apple\"></md-select-option>\n *   <md-select-option value=\"banana\" headline=\"Banana\"></md-select-option>\n *   <md-select-option value=\"kiwi\" headline=\"Kiwi\"></md-select-option>\n *   <md-select-option value=\"orange\" headline=\"Orange\"></md-select-option>\n *   <md-select-option value=\"tomato\" headline=\"Tomato\"></md-select-option>\n * </md-outlined-select>\n * ```\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-select-option')\nexport class MdSelectOption extends SelectOptionEl {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}