{"version": 3, "file": "shared.js", "sourceRoot": "", "sources": ["shared.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AASH;;;;;;GAMG;AACH,MAAM,UAAU,gBAAgB,CAAC,KAAqB;IACpD,MAAM,mBAAmB,GAAyB,EAAE,CAAC;IAErD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,mBAAmB,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,OAAO,mBAAmB,CAAC;AAC7B,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {SelectOption} from './selectoption/select-option.js';\n\n/**\n * A type that describes a SelectOption and its index.\n */\nexport type SelectOptionRecord = [SelectOption, number];\n\n/**\n * Given a list of select options, this function will return an array of\n * SelectOptionRecords that are selected.\n *\n * @return An array of SelectOptionRecords describing the options that are\n * selected.\n */\nexport function getSelectedItems(items: SelectOption[]) {\n  const selectedItemRecords: SelectOptionRecord[] = [];\n\n  for (let i = 0; i < items.length; i++) {\n    const item = items[i];\n    if (item.selected) {\n      selectedItemRecords.push([item, i]);\n    }\n  }\n\n  return selectedItemRecords;\n}\n"]}