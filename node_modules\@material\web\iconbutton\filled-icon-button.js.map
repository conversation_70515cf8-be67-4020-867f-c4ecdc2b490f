{"version": 3, "file": "filled-icon-button.js", "sourceRoot": "", "sources": ["filled-icon-button.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,MAAM,EAAC,MAAM,6BAA6B,CAAC;AACnD,OAAO,EAAC,UAAU,EAAC,MAAM,2BAA2B,CAAC;AACrD,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAQnE;;;;;;;;;;;;;;;;;GAiBG;AAEI,IAAM,kBAAkB,GAAxB,MAAM,kBAAmB,SAAQ,UAAU;IAG7B,gBAAgB;QACjC,OAAO;YACL,GAAG,KAAK,CAAC,gBAAgB,EAAE;YAC3B,QAAQ,EAAE,IAAI;YACd,eAAe,EAAE,IAAI,CAAC,MAAM;SAC7B,CAAC;IACJ,CAAC;;AARe,yBAAM,GAAwB,CAAC,YAAY,EAAE,MAAM,CAAC,AAA9C,CAA+C;AAD1D,kBAAkB;IAD9B,aAAa,CAAC,uBAAuB,CAAC;GAC1B,kBAAkB,CAU9B", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {styles} from './internal/filled-styles.js';\nimport {IconButton} from './internal/icon-button.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-filled-icon-button': MdFilledIconButton;\n  }\n}\n\n/**\n * @summary Icon buttons help people take supplementary actions with a single\n * tap.\n *\n * @description\n * __Emphasis:__ Low emphasis – For optional or supplementary actions with the\n * least amount of prominence.\n *\n * __Rationale:__ The most compact and unobtrusive type of button, icon buttons\n * are used for optional supplementary actions such as \"Bookmark\" or \"Star.\"\n *\n * __Example usages:__\n * - Add to Favorites\n * - Print\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-filled-icon-button')\nexport class MdFilledIconButton extends IconButton {\n  static override styles: CSSResultOrNative[] = [sharedStyles, styles];\n\n  protected override getRenderClasses() {\n    return {\n      ...super.getRenderClasses(),\n      'filled': true,\n      'toggle-filled': this.toggle,\n    };\n  }\n}\n"]}