//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:list';
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use '../../tokens';
// go/keep-sorted end

@mixin theme($tokens) {
  $supported-tokens: tokens.$md-comp-filter-chip-supported-tokens;
  @each $token, $value in $tokens {
    @if list.index($supported-tokens, $token) == null {
      @error 'Token `#{$token}` is not a supported token.';
    }

    @if $value {
      --md-filter-chip-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $tokens: tokens.md-comp-filter-chip-values();

  :host {
    // Only use the logical properties.
    $tokens: map.remove($tokens, 'container-shape');
    @each $token, $value in $tokens {
      --_#{$token}: #{$value};
    }
  }

  .selected.elevated::before {
    background: var(--_elevated-selected-container-color);
  }

  .checkmark {
    height: var(--_icon-size);
    width: var(--_icon-size);
  }

  .disabled .checkmark {
    opacity: var(--_disabled-leading-icon-opacity);
  }

  @media (forced-colors: active) {
    .disabled .checkmark {
      opacity: 1;
    }
  }
}
