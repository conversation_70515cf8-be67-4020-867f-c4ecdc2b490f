//
// Copyright 2022 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:list';
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use '../../tokens';
// go/keep-sorted end

@mixin theme($tokens) {
  $supported-tokens: tokens.$md-comp-ripple-supported-tokens;
  @each $token, $value in $tokens {
    @if list.index($supported-tokens, $token) == null {
      @error 'Token `#{$token}` is not a supported token.';
    }

    @if $value {
      --md-ripple-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $tokens: tokens.md-comp-ripple-values();

  :host {
    display: flex;
    margin: auto;
    pointer-events: none;
  }

  :host([disabled]) {
    display: none;
  }

  @media (forced-colors: active) {
    :host {
      display: none;
    }
  }

  :host,
  .surface {
    border-radius: inherit;
    position: absolute;
    inset: 0;
    overflow: hidden;
  }

  .surface {
    -webkit-tap-highlight-color: transparent;

    &::before,
    &::after {
      content: '';
      opacity: 0;
      position: absolute;
    }

    &::before {
      background-color: map.get($tokens, 'hover-color');
      inset: 0;
      transition: opacity 15ms linear, background-color 15ms linear;
    }

    &::after {
      // press ripple fade-out
      background: radial-gradient(
        closest-side,
        map.get($tokens, 'pressed-color') max(calc(100% - 70px), 65%),
        transparent 100%
      );
      transform-origin: center center;
      transition: opacity 375ms linear;
    }
  }

  .hovered::before {
    background-color: map.get($tokens, 'hover-color');
    opacity: map.get($tokens, 'hover-opacity');
  }

  .pressed::after {
    // press ripple fade-in
    opacity: map.get($tokens, 'pressed-opacity');
    transition-duration: 105ms;
  }
}
