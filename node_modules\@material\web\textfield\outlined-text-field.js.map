{"version": 3, "file": "outlined-text-field.js", "sourceRoot": "", "sources": ["outlined-text-field.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,4BAA4B,CAAC;AAGpC,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAC,OAAO,EAAC,MAAM,oBAAoB,CAAC;AAE3C,OAAO,EAAC,MAAM,IAAI,cAAc,EAAC,MAAM,+BAA+B,CAAC;AACvE,OAAO,EAAC,iBAAiB,EAAC,MAAM,mCAAmC,CAAC;AACpE,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAUnE;;;;GAIG;AAEI,IAAM,mBAAmB,GAAzB,MAAM,mBAAoB,SAAQ,iBAAiB;IAAnD;;QAGuB,aAAQ,GAAG,OAAO,CAAA,mBAAmB,CAAC;IACpE,CAAC;;AAHiB,0BAAM,GAAwB,CAAC,YAAY,EAAE,cAAc,CAAC,AAAtD,CAAuD;AADlE,mBAAmB;IAD/B,aAAa,CAAC,wBAAwB,CAAC;GAC3B,mBAAmB,CAI/B", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../field/outlined-field.js';\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\nimport {literal} from 'lit/static-html.js';\n\nimport {styles as outlinedStyles} from './internal/outlined-styles.js';\nimport {OutlinedTextField} from './internal/outlined-text-field.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\nexport {type TextFieldType} from './internal/text-field.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-outlined-text-field': MdOutlinedTextField;\n  }\n}\n\n/**\n * TODO(b/228525797): Add docs\n * @final\n * @suppress {visibility}\n */\n@customElement('md-outlined-text-field')\nexport class MdOutlinedTextField extends OutlinedTextField {\n  static override styles: CSSResultOrNative[] = [sharedStyles, outlinedStyles];\n\n  protected override readonly fieldTag = literal`md-outlined-field`;\n}\n"]}