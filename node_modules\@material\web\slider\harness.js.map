{"version": 3, "file": "harness.js", "sourceRoot": "", "sources": ["harness.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,OAAO,EAAC,MAAM,uBAAuB,CAAC;AAI9C;;GAEG;AACH,MAAM,OAAO,aAAc,SAAQ,OAAe;IACvC,KAAK,CAAC,qBAAqB;QAClC,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAC1C,WAAW,CACX,CAAC;IACL,CAAC;IAED,SAAS;QACP,OAAO;YACL,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAmB,WAAW,CAAE;YACrE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAmB,aAAa,CAAE;SACxE,CAAC;IACJ,CAAC;IACD,UAAU;QACR,OAAO;YACL,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,aAAa,CAAE;YACrD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,eAAe,CAAE;SACxD,CAAC;IACJ,CAAC;IAED,SAAS;QACP,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;IACxE,CAAC;IAED,cAAc;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;YACvB,sDAAsD;YACrD,CAAiB,CAAC,KAAK,CAAC,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAC3D,MAAM,EAAC,KAAK,EAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,CAAC;YACzC,CAAiB,CAAC,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YACtD,OAAO,KAAK,GAAG,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,KAAa,EAAE,EAAqB;QACjE,IAAI,CAAC,EAAE,EAAE,CAAC;YACR,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;QACD,EAAE,CAAC,KAAK,EAAE,CAAC;QACX,EAAE,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,aAAa,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;QAC5E,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QACzB,EAAE,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;QACtE,EAAE,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,WAAW,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;QAC1E,EAAE,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;IACpC,CAAC;IAEO,qBAAqB,CAAC,IAAsB,EAAE,WAAW,GAAG,KAAK;QACvE,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,MAAM,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,MAAM,CAAC,qBAAqB,EAAE,CAAC;QAC9C,OAAO,EAAC,GAAG,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAC,CAAC;IACnE,CAAC;IAEkB,kBAAkB,CACnC,OAAoB,EACpB,OAAyB,EAAE;QAE3B,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,OAA2B,CAAC,CAAC;QAChE,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;YACvC,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QACnD,CAAC;QACD,KAAK,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;IAEkB,kBAAkB,CACnC,OAAoB,EACpB,OAAyB,EAAE;QAE3B,KAAK,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACxC,0EAA0E;QAC1E,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {Harness} from '../testing/harness.js';\n\nimport {Slider} from './internal/slider.js';\n\n/**\n * Test harness for slider.\n */\nexport class SliderHarness extends Harness<Slider> {\n  override async getInteractiveElement() {\n    await this.element.updateComplete;\n    return this.element.renderRoot.querySelector<HTMLInputElement>(\n      'input.end',\n    )!;\n  }\n\n  getInputs() {\n    return [\n      this.element.renderRoot.querySelector<HTMLInputElement>('input.end')!,\n      this.element.renderRoot.querySelector<HTMLInputElement>('input.start')!,\n    ];\n  }\n  getHandles() {\n    return [\n      this.element.renderRoot.querySelector('.handle.end')!,\n      this.element.renderRoot.querySelector('.handle.start')!,\n    ];\n  }\n\n  getLabels() {\n    return Array.from(this.element.renderRoot.querySelectorAll('.label'));\n  }\n\n  isLabelShowing() {\n    const labels = this.getLabels();\n    return labels.some((l) => {\n      // remove transition to avoid the need to wait for it.\n      (l as HTMLElement).style.setProperty('transition', 'none');\n      const {width} = l.getBoundingClientRect();\n      (l as HTMLElement).style.removeProperty('transition');\n      return width > 0;\n    });\n  }\n\n  async simulateValueInteraction(value: number, el?: HTMLInputElement) {\n    if (!el) {\n      el = this.getInputs()[0];\n    }\n    el.focus();\n    el.dispatchEvent(new Event('pointerdown', {bubbles: true, composed: true}));\n    el.value = String(value);\n    el.dispatchEvent(new Event('input', {bubbles: true, composed: true}));\n    el.dispatchEvent(new Event('pointerup', {bubbles: true, composed: true}));\n    el.dispatchEvent(new Event('change', {bubbles: true}));\n    await this.element.updateComplete;\n  }\n\n  private positionEventAtHandle(init: PointerEventInit, startHandle = false) {\n    const handle = this.getHandles()[startHandle ? 1 : 0];\n    const {x, y} = handle.getBoundingClientRect();\n    return {...init, clientX: x, clientY: y, screenX: x, screenY: y};\n  }\n\n  protected override simulateStartHover(\n    element: HTMLElement,\n    init: PointerEventInit = {},\n  ) {\n    const i = this.getInputs().indexOf(element as HTMLInputElement);\n    if (i >= 0 || element === this.element) {\n      init = this.positionEventAtHandle(init, i === 1);\n    }\n    super.simulateStartHover(element, init);\n  }\n\n  protected override simulateMousePress(\n    element: HTMLElement,\n    init: PointerEventInit = {},\n  ) {\n    super.simulateMousePress(element, init);\n    // advance beyond RAF, which is used by the element's pointerDown handler.\n    jasmine.clock().tick(1);\n  }\n}\n"]}