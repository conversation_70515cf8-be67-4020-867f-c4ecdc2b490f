{"version": 3, "file": "radio-validator.js", "sourceRoot": "", "sources": ["radio-validator.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,SAAS,EAAC,MAAM,gBAAgB,CAAC;AAsBzC;;;GAGG;AACH,MAAM,OAAO,cAAe,SAAQ,SAA0B;IAGzC,eAAe,CAAC,MAAuB;QACxD,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,kCAAkC;YAClC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACpD,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC;YACjC,2CAA2C;YAC3C,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC;QACnC,CAAC;QAED,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,KAAK,MAAM,EAAC,OAAO,EAAE,QAAQ,EAAC,IAAI,MAAM,EAAE,CAAC;YACzC,IAAI,QAAQ,EAAE,CAAC;gBACb,UAAU,GAAG,IAAI,CAAC;YACpB,CAAC;YAED,IAAI,OAAO,EAAE,CAAC;gBACZ,SAAS,GAAG,IAAI,CAAC;YACnB,CAAC;QACH,CAAC;QAED,wEAAwE;QACxE,wEAAwE;QACxE,0EAA0E;QAC1E,8DAA8D;QAC9D,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,SAAS,CAAC;QACtC,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,UAAU,CAAC;QACxC,OAAO;YACL,QAAQ,EAAE;gBACR,YAAY,EAAE,UAAU,IAAI,CAAC,SAAS;aACvC;YACD,iBAAiB,EAAE,IAAI,CAAC,YAAY,CAAC,iBAAiB;SACvD,CAAC;IACJ,CAAC;IAEkB,MAAM,CACvB,SAA0B,EAC1B,SAA0B;QAE1B,IAAI,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;YAC1C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACrE,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEkB,IAAI,CAAC,MAAuB;QAC7C,uEAAuE;QACvE,wDAAwD;QACxD,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,EAAC,OAAO,EAAE,QAAQ,EAAC,EAAE,EAAE,CAAC,CAAC;YAC1C,OAAO;YACP,QAAQ;SACT,CAAC,CAA+B,CAAC;IACpC,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {Validator} from './validator.js';\n\n/**\n * Constraint validation properties for a radio.\n */\nexport interface RadioState {\n  /**\n   * Whether the radio is checked.\n   */\n  readonly checked: boolean;\n\n  /**\n   * Whether the radio is required.\n   */\n  readonly required: boolean;\n}\n\n/**\n * Radio constraint validation properties for a single radio and its siblings.\n */\nexport type RadioGroupState = readonly [RadioState, ...RadioState[]];\n\n/**\n * A validator that provides constraint validation that emulates\n * `<input type=\"radio\">` validation.\n */\nexport class RadioValidator extends Validator<RadioGroupState> {\n  private radioElement?: HTMLInputElement;\n\n  protected override computeValidity(states: RadioGroupState) {\n    if (!this.radioElement) {\n      // Lazily create the radio element\n      this.radioElement = document.createElement('input');\n      this.radioElement.type = 'radio';\n      // A name is required for validation to run\n      this.radioElement.name = 'group';\n    }\n\n    let isRequired = false;\n    let isChecked = false;\n    for (const {checked, required} of states) {\n      if (required) {\n        isRequired = true;\n      }\n\n      if (checked) {\n        isChecked = true;\n      }\n    }\n\n    // Firefox v119 doesn't compute grouped radio validation correctly while\n    // they are detached from the DOM, which is why we don't render multiple\n    // virtual <input>s. Instead, we can check the required/checked states and\n    // grab the i18n'd validation message if the value is missing.\n    this.radioElement.checked = isChecked;\n    this.radioElement.required = isRequired;\n    return {\n      validity: {\n        valueMissing: isRequired && !isChecked,\n      },\n      validationMessage: this.radioElement.validationMessage,\n    };\n  }\n\n  protected override equals(\n    prevGroup: RadioGroupState,\n    nextGroup: RadioGroupState,\n  ) {\n    if (prevGroup.length !== nextGroup.length) {\n      return false;\n    }\n\n    for (let i = 0; i < prevGroup.length; i++) {\n      const prev = prevGroup[i];\n      const next = nextGroup[i];\n      if (prev.checked !== next.checked || prev.required !== next.required) {\n        return false;\n      }\n    }\n\n    return true;\n  }\n\n  protected override copy(states: RadioGroupState): RadioGroupState {\n    // Cast as unknown since typescript does not have enough information to\n    // infer that the array always has at least one element.\n    return states.map(({checked, required}) => ({\n      checked,\n      required,\n    })) as unknown as RadioGroupState;\n  }\n}\n"]}