{"version": 3, "file": "navigation-drawer.js", "sourceRoot": "", "sources": ["navigation-drawer.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,iCAAiC,CAAC;AAEzC,OAAO,EAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAiB,MAAM,KAAK,CAAC;AAC9D,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAGrD,OAAO,EAAC,kBAAkB,EAAC,MAAM,oCAAoC,CAAC;AAEtE,wCAAwC;AACxC,MAAM,yBAAyB,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAEjE;;;;;GAKG;AACH,MAAM,OAAO,gBAAiB,SAAQ,yBAAyB;IAA/D;;QAC6B,WAAM,GAAG,KAAK,CAAC;QAC9B,UAAK,GAAoB,KAAK,CAAC;IA6C7C,CAAC;IA3CoB,MAAM;QACvB,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;QACpD,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;QACnD,iCAAiC;QACjC,MAAM,EAAC,SAAS,EAAE,SAAS,EAAC,GAAG,IAAuB,CAAC;QACvD,OAAO,IAAI,CAAA;;yBAEU,YAAY;uBACd,UAAU;qBACZ,SAAS,IAAI,OAAO;sBACnB,SAAS,IAAI,OAAO;uCACH,IAAI,CAAC,gBAAgB,EAAE;;;;;;;KAOzD,CAAC;IACJ,CAAC;IAEO,gBAAgB;QACtB,OAAO,QAAQ,CAAC;YACd,+BAA+B,EAAE,IAAI,CAAC,MAAM;YAC5C,uCAAuC,EAAE,IAAI,CAAC,KAAK,KAAK,OAAO;SAChE,CAAC,CAAC;IACL,CAAC;IAEkB,OAAO,CACxB,iBAAmD;QAEnD,IAAI,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpC,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,2BAA2B,EAAE;oBAC3C,MAAM,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAC;oBAC7B,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,IAAI;iBACf,CAAC,CACH,CAAC;YACJ,CAAC,EAAE,GAAG,CAAC,CAAC;QACV,CAAC;IACH,CAAC;CACF;AA9C4B;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;gDAAgB;AAC9B;IAAX,QAAQ,EAAE;+CAAgC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../../elevation/elevation.js';\n\nimport {html, LitElement, nothing, PropertyValues} from 'lit';\nimport {property} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\n\n// Separate variable needed for closure.\nconst navigationDrawerBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * b/265346501 - add docs\n *\n * @fires navigation-drawer-changed {CustomEvent<{opened: boolean}>}\n * Dispatched whenever the drawer opens or closes --bubbles --composed\n */\nexport class NavigationDrawer extends navigationDrawerBaseClass {\n  @property({type: Boolean}) opened = false;\n  @property() pivot: 'start' | 'end' = 'end';\n\n  protected override render() {\n    const ariaExpanded = this.opened ? 'true' : 'false';\n    const ariaHidden = !this.opened ? 'true' : 'false';\n    // Needed for closure conformance\n    const {ariaLabel, ariaModal} = this as ARIAMixinStrict;\n    return html`\n      <div\n        aria-expanded=\"${ariaExpanded}\"\n        aria-hidden=\"${ariaHidden}\"\n        aria-label=${ariaLabel || nothing}\n        aria-modal=\"${ariaModal || nothing}\"\n        class=\"md3-navigation-drawer ${this.getRenderClasses()}\"\n        role=\"dialog\">\n        <md-elevation part=\"elevation\"></md-elevation>\n        <div class=\"md3-navigation-drawer__slot-content\">\n          <slot></slot>\n        </div>\n      </div>\n    `;\n  }\n\n  private getRenderClasses() {\n    return classMap({\n      'md3-navigation-drawer--opened': this.opened,\n      'md3-navigation-drawer--pivot-at-start': this.pivot === 'start',\n    });\n  }\n\n  protected override updated(\n    changedProperties: PropertyValues<NavigationDrawer>,\n  ) {\n    if (changedProperties.has('opened')) {\n      setTimeout(() => {\n        this.dispatchEvent(\n          new CustomEvent('navigation-drawer-changed', {\n            detail: {opened: this.opened},\n            bubbles: true,\n            composed: true,\n          }),\n        );\n      }, 250);\n    }\n  }\n}\n"]}