{"version": 3, "file": "ripple.js", "sourceRoot": "", "sources": ["ripple.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,MAAM,EAAC,MAAM,sBAAsB,CAAC;AAC5C,OAAO,EAAC,MAAM,EAAC,MAAM,6BAA6B,CAAC;AAQnD;;;;;;;;;;;;GAYG;AAEI,IAAM,QAAQ,GAAd,MAAM,QAAS,SAAQ,MAAM;;AAClB,eAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAD5C,QAAQ;IADpB,aAAa,CAAC,WAAW,CAAC;GACd,QAAQ,CAEpB", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Ripple} from './internal/ripple.js';\nimport {styles} from './internal/ripple-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-ripple': MdRipple;\n  }\n}\n\n/**\n * @summary Ripples, also known as state layers, are visual indicators used to\n * communicate the status of a component or interactive element.\n *\n * @description A state layer is a semi-transparent covering on an element that\n * indicates its state. State layers provide a systematic approach to\n * visualizing states by using opacity. A layer can be applied to an entire\n * element or in a circular shape and only one state layer can be applied at a\n * given time.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-ripple')\nexport class MdRipple extends Ripple {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}