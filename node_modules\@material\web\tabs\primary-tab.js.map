{"version": 3, "file": "primary-tab.js", "sourceRoot": "", "sources": ["primary-tab.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,UAAU,EAAC,MAAM,2BAA2B,CAAC;AACrD,OAAO,EAAC,MAAM,IAAI,aAAa,EAAC,MAAM,kCAAkC,CAAC;AACzE,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,0BAA0B,CAAC;AAQhE,8BAA8B;AAC9B;;;;;GAKG;AAEI,IAAM,YAAY,GAAlB,MAAM,YAAa,SAAQ,UAAU;;AAC1B,mBAAM,GAAwB,CAAC,YAAY,EAAE,aAAa,CAAC,AAArD,CAAsD;AADjE,YAAY;IADxB,aAAa,CAAC,gBAAgB,CAAC;GACnB,YAAY,CAExB", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {PrimaryTab} from './internal/primary-tab.js';\nimport {styles as primaryStyles} from './internal/primary-tab-styles.js';\nimport {styles as sharedStyles} from './internal/tab-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-primary-tab': MdPrimaryTab;\n  }\n}\n\n// TODO(b/*********): add docs\n/**\n * @summary Tab allow users to display a tab within a Tabs.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-primary-tab')\nexport class MdPrimaryTab extends PrimaryTab {\n  static override styles: CSSResultOrNative[] = [sharedStyles, primaryStyles];\n}\n"]}