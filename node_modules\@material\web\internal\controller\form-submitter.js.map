{"version": 3, "file": "form-submitter.js", "sourceRoot": "", "sources": ["form-submitter.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,QAAQ,EAAkB,MAAM,KAAK,CAAC;AAE9C,OAAO,EACL,SAAS,GAEV,MAAM,2CAA2C,CAAC;AAgDnD;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,UAAU,kBAAkB,CAAC,IAA8B;IAC/D,IAAI,QAAQ,EAAE,CAAC;QACb,OAAO;IACT,CAAC;IAEA,IAA0C,CAAC,cAAc,CAAC,CAAC,QAAQ,EAAE,EAAE;QACtE,MAAM,SAAS,GAAG,QAAyB,CAAC;QAC5C,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YAClD,MAAM,EAAC,IAAI,EAAE,CAAC,SAAS,CAAC,EAAE,gBAAgB,EAAC,GAAG,SAAS,CAAC;YACxD,MAAM,EAAC,IAAI,EAAC,GAAG,gBAAgB,CAAC;YAChC,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC/B,OAAO;YACT,CAAC;YAED,mDAAmD;YACnD,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;gBAClC,UAAU,CAAC,OAAO,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAC;gBAC3B,OAAO;YACT,CAAC;YAED,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;gBACrB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,OAAO;YACT,CAAC;YAED,0EAA0E;YAC1E,wEAAwE;YACxE,eAAe;YACf,uDAAuD;YACvD,IAAI,CAAC,gBAAgB,CACnB,QAAQ,EACR,CAAC,WAAW,EAAE,EAAE;gBACd,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW,EAAE;oBAC9C,YAAY,EAAE,IAAI;oBAClB,UAAU,EAAE,IAAI;oBAChB,GAAG,EAAE,GAAG,EAAE,CAAC,SAAS;iBACrB,CAAC,CAAC;YACL,CAAC,EACD,EAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAC5B,CAAC;YAEF,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC/C,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {isServer, ReactiveElement} from 'lit';\n\nimport {\n  internals,\n  WithElementInternals,\n} from '../../labs/behaviors/element-internals.js';\n\n/**\n * A string indicating the form submission behavior of the element.\n *\n * - submit: The element submits the form. This is the default value if the\n * attribute is not specified, or if it is dynamically changed to an empty or\n * invalid value.\n * - reset: The element resets the form.\n * - button: The element does nothing.\n */\nexport type FormSubmitterType = 'button' | 'submit' | 'reset';\n\n/**\n * An element that can submit or reset a `<form>`, similar to\n * `<button type=\"submit\">`.\n */\nexport interface FormSubmitter extends ReactiveElement, WithElementInternals {\n  /**\n   * A string indicating the form submission behavior of the element.\n   *\n   * - submit: The element submits the form. This is the default value if the\n   * attribute is not specified, or if it is dynamically changed to an empty or\n   * invalid value.\n   * - reset: The element resets the form.\n   * - button: The element does nothing.\n   */\n  type: FormSubmitterType;\n\n  /**\n   * The HTML name to use in form submission. When combined with a `value`, the\n   * submitting button's name/value will be added to the form.\n   *\n   * Names must reflect to a `name` attribute for form integration.\n   */\n  name: string;\n\n  /**\n   * The value of the button. When combined with a `name`, the submitting\n   * button's name/value will be added to the form.\n   */\n  value: string;\n}\n\ntype FormSubmitterConstructor =\n  | (new () => FormSubmitter)\n  | (abstract new () => FormSubmitter);\n\n/**\n * Sets up an element's constructor to enable form submission. The element\n * instance should be form associated and have a `type` property.\n *\n * A click listener is added to each element instance. If the click is not\n * default prevented, it will submit the element's form, if any.\n *\n * @example\n * ```ts\n * class MyElement extends mixinElementInternals(LitElement) {\n *   static {\n *     setupFormSubmitter(MyElement);\n *   }\n *\n *   static formAssociated = true;\n *\n *   type: FormSubmitterType = 'submit';\n * }\n * ```\n *\n * @param ctor The form submitter element's constructor.\n */\nexport function setupFormSubmitter(ctor: FormSubmitterConstructor) {\n  if (isServer) {\n    return;\n  }\n\n  (ctor as unknown as typeof ReactiveElement).addInitializer((instance) => {\n    const submitter = instance as FormSubmitter;\n    submitter.addEventListener('click', async (event) => {\n      const {type, [internals]: elementInternals} = submitter;\n      const {form} = elementInternals;\n      if (!form || type === 'button') {\n        return;\n      }\n\n      // Wait a full task for event bubbling to complete.\n      await new Promise<void>((resolve) => {\n        setTimeout(resolve);\n      });\n\n      if (event.defaultPrevented) {\n        return;\n      }\n\n      if (type === 'reset') {\n        form.reset();\n        return;\n      }\n\n      // form.requestSubmit(submitter) does not work with form associated custom\n      // elements. This patches the dispatched submit event to add the correct\n      // `submitter`.\n      // See https://github.com/WICG/webcomponents/issues/814\n      form.addEventListener(\n        'submit',\n        (submitEvent) => {\n          Object.defineProperty(submitEvent, 'submitter', {\n            configurable: true,\n            enumerable: true,\n            get: () => submitter,\n          });\n        },\n        {capture: true, once: true},\n      );\n\n      elementInternals.setFormValue(submitter.value);\n      form.requestSubmit();\n    });\n  });\n}\n"]}