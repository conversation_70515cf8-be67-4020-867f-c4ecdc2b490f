{"version": 3, "file": "navigation-bar.js", "sourceRoot": "", "sources": ["navigation-bar.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,iCAAiC,CAAC;AAEzC,OAAO,EAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAiB,MAAM,KAAK,CAAC;AAC9D,OAAO,EAAC,QAAQ,EAAE,qBAAqB,EAAC,MAAM,mBAAmB,CAAC;AAGlE,OAAO,EAAC,kBAAkB,EAAC,MAAM,oCAAoC,CAAC;AACtE,OAAO,EAAC,KAAK,EAAC,MAAM,wCAAwC,CAAC;AAM7D,wCAAwC;AACxC,MAAM,sBAAsB,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAE9D;;;;;GAKG;AACH,MAAM,OAAO,aACX,SAAQ,sBAAsB;IADhC;;QAIuD,gBAAW,GAAG,CAAC,CAAC;QAGrE,uBAAkB,GAAG,KAAK,CAAC;QAE3B,SAAI,GAAoB,EAAE,CAAC;IAkI7B,CAAC;IA7HoB,MAAM;QACvB,iCAAiC;QACjC,MAAM,EAAC,SAAS,EAAC,GAAG,IAAuB,CAAC;QAC5C,OAAO,IAAI,CAAA;;;mBAGI,SAAS,IAAI,OAAO;kBACrB,IAAI,CAAC,aAAa;qCACC,IAAI,CAAC,8BAA8B;iCACvC,IAAI,CAAC,4BAA4B;;;YAGtD,CAAC;IACX,CAAC;IAEkB,OAAO,CAAC,iBAAgD;QACzE,IAAI,iBAAiB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC3C,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,0BAA0B,EAAE;gBAC1C,MAAM,EAAE;oBACN,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;oBAChC,WAAW,EAAE,IAAI,CAAC,WAAW;iBAC9B;gBACD,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,IAAI;aACf,CAAC,CACH,CAAC;QACJ,CAAC;QAED,IAAI,iBAAiB,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,CAAC;YAChD,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACzD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAEQ,YAAY,CAAC,iBAAiC;QACrD,KAAK,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QACtC,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAO;QAC9B,MAAM,OAAO,GAAoB,EAAE,CAAC;QACpC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;QACD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;IACtB,CAAC;IAEO,4BAA4B,CAAC,KAAkB;QACrD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAuB,CAAC;QAC7C,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC;IACH,CAAC;IAEO,8BAA8B,CAAC,KAAoC;QACzE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,KAAsB,CAAC,CAAC;IAC5E,CAAC;IAEO,aAAa,CAAC,KAAoB;QACxC,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;QACtB,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,EAAE;YAClD,OAAO,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QACH,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAEtC,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG,KAAK,GAAG,EAAE,CAAC;YACnC,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC;YACnC,OAAO;QACT,CAAC;QAED,IAAI,GAAG,KAAK,MAAM,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QAED,IAAI,GAAG,KAAK,KAAK,EAAE,CAAC;YAClB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;YAC5B,OAAO;QACT,CAAC;QAED,MAAM,SAAS,GACb,CAAC,GAAG,KAAK,YAAY,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,WAAW,IAAI,KAAK,CAAC,CAAC;QACrE,IAAI,SAAS,IAAI,eAAe,KAAK,QAAQ,EAAE,CAAC;YAC9C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QACD,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YACvC,OAAO;QACT,CAAC;QAED,MAAM,aAAa,GACjB,CAAC,GAAG,KAAK,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,YAAY,IAAI,KAAK,CAAC,CAAC;QACrE,IAAI,aAAa,IAAI,eAAe,KAAK,CAAC,EAAE,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;YAC5B,OAAO;QACT,CAAC;QACD,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YACvC,OAAO;QACT,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,KAAa;QACvC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,KAAK,KAAK,CAAC;QACpC,CAAC;IACH,CAAC;IAEO,0BAA0B,CAAC,KAAc;QAC/C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAC5B,GAAG,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAChC,CAAC;IACH,CAAC;CACF;AAvIsD;IAApD,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAC,CAAC;kDAAiB;AAGrE;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,sBAAsB,EAAC,CAAC;yDAClC;AAKV;IADhB,qBAAqB,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;kDACQ", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../../elevation/elevation.js';\n\nimport {html, LitElement, nothing, PropertyValues} from 'lit';\nimport {property, queryAssignedElements} from 'lit/decorators.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\nimport {isRtl} from '../../../internal/controller/is-rtl.js';\nimport {NavigationTab} from '../../navigationtab/internal/navigation-tab.js';\n\nimport {NavigationTabInteractionEvent} from './constants.js';\nimport {NavigationBarState} from './state.js';\n\n// Separate variable needed for closure.\nconst navigationBarBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * b/265346501 - add docs\n *\n * @fires navigation-bar-activated {CustomEvent<tab: NavigationTab, activeIndex: number>}\n * Dispatched whenever the `activeIndex` changes. --bubbles --composed\n */\nexport class NavigationBar\n  extends navigationBarBaseClass\n  implements NavigationBarState\n{\n  @property({type: Number, attribute: 'active-index'}) activeIndex = 0;\n\n  @property({type: Boolean, attribute: 'hide-inactive-labels'})\n  hideInactiveLabels = false;\n\n  tabs: NavigationTab[] = [];\n\n  @queryAssignedElements({flatten: true})\n  private readonly tabsElement!: NavigationTab[];\n\n  protected override render() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`<div\n      class=\"md3-navigation-bar\"\n      role=\"tablist\"\n      aria-label=${ariaLabel || nothing}\n      @keydown=\"${this.handleKeydown}\"\n      @navigation-tab-interaction=\"${this.handleNavigationTabInteraction}\"\n      @navigation-tab-rendered=${this.handleNavigationTabConnected}\n      ><md-elevation part=\"elevation\"></md-elevation\n      ><div class=\"md3-navigation-bar__tabs-slot-container\"><slot></slot></div\n    ></div>`;\n  }\n\n  protected override updated(changedProperties: PropertyValues<NavigationBar>) {\n    if (changedProperties.has('activeIndex')) {\n      this.onActiveIndexChange(this.activeIndex);\n      this.dispatchEvent(\n        new CustomEvent('navigation-bar-activated', {\n          detail: {\n            tab: this.tabs[this.activeIndex],\n            activeIndex: this.activeIndex,\n          },\n          bubbles: true,\n          composed: true,\n        }),\n      );\n    }\n\n    if (changedProperties.has('hideInactiveLabels')) {\n      this.onHideInactiveLabelsChange(this.hideInactiveLabels);\n    }\n\n    if (changedProperties.has('tabs')) {\n      this.onHideInactiveLabelsChange(this.hideInactiveLabels);\n      this.onActiveIndexChange(this.activeIndex);\n    }\n  }\n\n  override firstUpdated(changedProperties: PropertyValues) {\n    super.firstUpdated(changedProperties);\n    this.layout();\n  }\n\n  layout() {\n    if (!this.tabsElement) return;\n    const navTabs: NavigationTab[] = [];\n    for (const node of this.tabsElement) {\n      navTabs.push(node);\n    }\n    this.tabs = navTabs;\n  }\n\n  private handleNavigationTabConnected(event: CustomEvent) {\n    const target = event.target as NavigationTab;\n    if (this.tabs.indexOf(target) === -1) {\n      this.layout();\n    }\n  }\n\n  private handleNavigationTabInteraction(event: NavigationTabInteractionEvent) {\n    this.activeIndex = this.tabs.indexOf(event.detail.state as NavigationTab);\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    const key = event.key;\n    const focusedTabIndex = this.tabs.findIndex((tab) => {\n      return tab.matches(':focus-within');\n    });\n    const isRTL = isRtl(this);\n    const maxIndex = this.tabs.length - 1;\n\n    if (key === 'Enter' || key === ' ') {\n      this.activeIndex = focusedTabIndex;\n      return;\n    }\n\n    if (key === 'Home') {\n      this.tabs[0].focus();\n      return;\n    }\n\n    if (key === 'End') {\n      this.tabs[maxIndex].focus();\n      return;\n    }\n\n    const toNextTab =\n      (key === 'ArrowRight' && !isRTL) || (key === 'ArrowLeft' && isRTL);\n    if (toNextTab && focusedTabIndex === maxIndex) {\n      this.tabs[0].focus();\n      return;\n    }\n    if (toNextTab) {\n      this.tabs[focusedTabIndex + 1].focus();\n      return;\n    }\n\n    const toPreviousTab =\n      (key === 'ArrowLeft' && !isRTL) || (key === 'ArrowRight' && isRTL);\n    if (toPreviousTab && focusedTabIndex === 0) {\n      this.tabs[maxIndex].focus();\n      return;\n    }\n    if (toPreviousTab) {\n      this.tabs[focusedTabIndex - 1].focus();\n      return;\n    }\n  }\n\n  private onActiveIndexChange(value: number) {\n    if (!this.tabs[value]) {\n      throw new Error('NavigationBar: activeIndex is out of bounds.');\n    }\n    for (let i = 0; i < this.tabs.length; i++) {\n      this.tabs[i].active = i === value;\n    }\n  }\n\n  private onHideInactiveLabelsChange(value: boolean) {\n    for (const tab of this.tabs) {\n      tab.hideInactiveLabel = value;\n    }\n  }\n}\n"]}