{"version": 3, "file": "slider.js", "sourceRoot": "", "sources": ["slider.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,8BAA8B,CAAC;AACtC,OAAO,8BAA8B,CAAC;AACtC,OAAO,wBAAwB,CAAC;AAEhC,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAiB,MAAM,KAAK,CAAC;AACxE,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAC,MAAM,mBAAmB,CAAC;AACrE,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AACrD,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AACrD,OAAO,EAAC,IAAI,EAAC,MAAM,wBAAwB,CAAC;AAG5C,OAAO,EAAC,kBAAkB,EAAC,MAAM,iCAAiC,CAAC;AACnE,OAAO,EACL,uBAAuB,EACvB,iBAAiB,GAClB,MAAM,gDAAgD,CAAC;AACxD,OAAO,EAAC,eAAe,EAAC,MAAM,2CAA2C,CAAC;AAC1E,OAAO,EAAC,qBAAqB,EAAC,MAAM,2CAA2C,CAAC;AAChF,OAAO,EACL,YAAY,EACZ,mBAAmB,GACpB,MAAM,yCAAyC,CAAC;AAGjD,kDAAkD;AAClD,mDAAmD;AAEnD,wCAAwC;AACxC,MAAM,eAAe,GAAG,kBAAkB,CACxC,mBAAmB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,CACvD,CAAC;AAEF;;;;;;;;;;GAUG;AACH,MAAM,OAAO,MAAO,SAAQ,eAAe;IAgGzC;;;;OAIG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC;IACtD,CAAC;IACD,IAAI,SAAS,CAAC,IAAY;QACxB,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC;IACzD,CAAC;IACD,IAAI,OAAO,CAAC,IAAY;QACtB,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC;IAwBD,2EAA2E;IAC3E,6CAA6C;IAC7C,IAAY,oBAAoB;QAC9B,iCAAiC;QACjC,MAAM,EAAC,SAAS,EAAC,GAAG,IAAuB,CAAC;QAC5C,OAAO,CACL,IAAI,CAAC,cAAc;YACnB,CAAC,SAAS,IAAI,GAAG,SAAS,QAAQ,CAAC;YACnC,IAAI,CAAC,eAAe;YACpB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CACxB,CAAC;IACJ,CAAC;IAED,IAAY,wBAAwB;QAClC,OAAO,CACL,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAC3E,CAAC;IACJ,CAAC;IAED,8EAA8E;IAC9E,2EAA2E;IAC3E,QAAQ;IACR,IAAY,kBAAkB;QAC5B,iCAAiC;QACjC,MAAM,EAAC,SAAS,EAAC,GAAG,IAAuB,CAAC;QAC5C,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,OAAO,CACL,IAAI,CAAC,YAAY;gBACjB,CAAC,SAAS,IAAI,GAAG,SAAS,MAAM,CAAC;gBACjC,IAAI,CAAC,aAAa;gBAClB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CACtB,CAAC;QACJ,CAAC;QAED,OAAO,SAAS,IAAI,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED,IAAY,sBAAsB;QAChC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,OAAO,CACL,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,aAAa,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CACrE,CAAC;QACJ,CAAC;QAED,yBAAyB;QACzB,MAAM,EAAC,aAAa,EAAC,GAAG,IAAuB,CAAC;QAChD,OAAO,aAAa,IAAI,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChE,CAAC;IAUD;QACE,KAAK,EAAE,CAAC;QAjMV;;WAEG;QACuB,QAAG,GAAG,CAAC,CAAC;QAElC;;WAEG;QACuB,QAAG,GAAG,GAAG,CAAC;QAiBpC;;;WAGG;QACmC,eAAU,GAAG,EAAE,CAAC;QAEtD;;;WAGG;QACyC,oBAAe,GAAG,EAAE,CAAC;QAEjE;;;WAGG;QACuC,kBAAa,GAAG,EAAE,CAAC;QAE7D;;;WAGG;QACwC,mBAAc,GAAG,EAAE,CAAC;QAE/D;;;WAGG;QAC4C,uBAAkB,GAAG,EAAE,CAAC;QAEvE;;;WAGG;QACsC,iBAAY,GAAG,EAAE,CAAC;QAE3D;;;WAGG;QAC0C,qBAAgB,GAAG,EAAE,CAAC;QAEnE;;WAEG;QACuB,SAAI,GAAG,CAAC,CAAC;QAEnC;;WAEG;QACwB,UAAK,GAAG,KAAK,CAAC;QAEzC;;WAEG;QACwB,YAAO,GAAG,KAAK,CAAC;QAE3C;;;;WAIG;QACwB,UAAK,GAAG,KAAK,CAAC;QAoCzC,gEAAgE;QAChE,gEAAgE;QAChE,uBAAuB;QACN,qBAAgB,GAAG,KAAK,CAAC;QACzB,mBAAc,GAAG,KAAK,CAAC;QAEvB,eAAU,GAAG,KAAK,CAAC;QACnB,uBAAkB,GAAG,KAAK,CAAC;QAsD5C,oEAAoE;QAC5D,oBAAe,GAAG,CAAC,CAAC;QAE5B,2DAA2D;QACnD,yBAAoB,GAAG,KAAK,CAAC;QAMnC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,KAAiB,EAAE,EAAE;gBACnD,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAChD,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEQ,KAAK;QACZ,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;IACzB,CAAC;IAEkB,UAAU,CAAC,OAAuB;QACnD,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;YAC/C,CAAC,CAAC,IAAI,CAAC,UAAU;YACjB,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC;QACnC,MAAM,eAAe,GACnB,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,CAAC,cAAc,GAAG,eAAe;YACnC,CAAC,CAAC,IAAI,CAAC,KAAK;gBACV,CAAC,CAAC,IAAI,CAAC,QAAQ;gBACf,CAAC,CAAC,IAAI,CAAC,KAAK;YACd,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC;QACjC,wEAAwE;QACxE,QAAQ;QACR,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,SAAS,EAAE,CAAC;YAClD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAClE,CAAC;aAAM,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,KAAK,SAAS,EAAE,CAAC;YACvD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEkB,OAAO,CAAC,OAAuB;QAChD,yEAAyE;QACzE,0EAA0E;QAC1E,sEAAsE;QACtE,uDAAuD;QACvD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAW,CAAC,aAAa,CAAC;QACzD,CAAC;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAS,CAAC,aAAa,CAAC;QACnD,kCAAkC;QAClC,sDAAsD;QACtD,oCAAoC;QACpC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC1C,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;gBAClC,IAAI,CAAC,UAAW,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC;gBACpD,+BAA+B;gBAC/B,MAAM,CAAC,GAAG,IAAI,CAAC,UAAW,CAAC,aAAa,CAAC;gBACzC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;YAC9C,CAAC;YACD,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAChC,IAAI,CAAC,QAAS,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;gBACtD,+BAA+B;gBAC/B,MAAM,CAAC,GAAG,IAAI,CAAC,QAAS,CAAC,aAAa,CAAC;gBACvC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,cAAc,CAAC;QACrC,CAAC;QACD,IACE,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;YAC7B,IAAI,CAAC,eAAe,EACpB,CAAC;YACD,qEAAqE;YACrE,qEAAqE;YACrE,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;YAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;YAC3D,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC5D,CAAC;QACD,0CAA0C;QAC1C,sDAAsD;QACtD,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAEkB,MAAM;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAClD,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK;YAC9B,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK;YAC1D,CAAC,CAAC,CAAC,CAAC;QACN,MAAM,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAC3E,MAAM,eAAe,GAAG;YACtB,wCAAwC;YACxC,mBAAmB,EAAE,MAAM,CAAC,aAAa,CAAC;YAC1C,iBAAiB,EAAE,MAAM,CAAC,WAAW,CAAC;YACtC,4BAA4B;YAC5B,eAAe,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;SACtC,CAAC;QACF,MAAM,gBAAgB,GAAG,EAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAC,CAAC;QAE9C,uDAAuD;QACvD,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACzE,MAAM,QAAQ,GACZ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE9B,MAAM,eAAe,GAAG;YACtB,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,IAAI,CAAC,gBAAgB;YAC5B,SAAS,EAAE,IAAI,CAAC,oBAAoB;YACpC,aAAa,EAAE,IAAI,CAAC,wBAAwB;YAC5C,OAAO,EAAE,IAAI,CAAC,GAAG;YACjB,OAAO,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG;SACnC,CAAC;QAEF,MAAM,aAAa,GAAG;YACpB,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,IAAI,CAAC,cAAc;YAC1B,SAAS,EAAE,IAAI,CAAC,kBAAkB;YAClC,aAAa,EAAE,IAAI,CAAC,sBAAsB;YAC1C,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG;YAC5D,OAAO,EAAE,IAAI,CAAC,GAAG;SAClB,CAAC;QAEF,MAAM,gBAAgB,GAAG;YACvB,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,IAAI,CAAC,gBAAgB;YAC5B,KAAK,EAAE,UAAU;SAClB,CAAC;QAEF,MAAM,cAAc,GAAG;YACrB,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,IAAI,CAAC,cAAc;YAC1B,KAAK,EAAE,QAAQ;SAChB,CAAC;QAEF,MAAM,sBAAsB,GAAG;YAC7B,KAAK,EAAE,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,cAAc;SACpD,CAAC;QAEF,OAAO,IAAI,CAAA;yBACU,QAAQ,CAAC,gBAAgB,CAAC;cACrC,QAAQ,CAAC,eAAe,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QACzD,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;;;wCAGrB,QAAQ,CAAC,sBAAsB,CAAC;cAC1D,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;cAC3D,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;;;;WAIpC,CAAC;IACV,CAAC;IAEO,WAAW;QACjB,OAAO,IAAI,CAAA;;QAEP,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA,+BAA+B,CAAC,CAAC,CAAC,OAAO;KAC7D,CAAC;IACJ,CAAC;IAEO,WAAW,CAAC,KAAa;QAC/B,OAAO,IAAI,CAAA;gDACiC,KAAK;WAC1C,CAAC;IACV,CAAC;IAEO,YAAY,CAAC,EACnB,KAAK,EACL,KAAK,EACL,KAAK,GAKN;QACC,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC;QAC1D,MAAM,aAAa,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,kBAAkB,CAAC;QAChE,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QACrC,OAAO,IAAI,CAAA;sBACO,QAAQ,CAAC;YACvB,CAAC,IAAI,CAAC,EAAE,IAAI;YACZ,KAAK;YACL,KAAK;YACL,aAAa;SACd,CAAC;6CACqC,IAAI;;cAEnC,IAAI;gBACF,IAAI;oBACA,IAAI,CAAC,QAAQ;;;;QAIzB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;WAC9C,CAAC;IACV,CAAC;IAEO,WAAW,CAAC,EAClB,KAAK,EACL,KAAK,EACL,SAAS,EACT,aAAa,EACb,OAAO,EACP,OAAO,GAQR;QACC,sEAAsE;QACtE,yEAAyE;QACzE,qBAAqB;QACrB,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;QACrC,OAAO,IAAI,CAAA;;eAEA,QAAQ,CAAC;YAChB,KAAK;YACL,GAAG,EAAE,CAAC,KAAK;SACZ,CAAC;eACO,IAAI,CAAC,WAAW;qBACV,IAAI,CAAC,UAAU;mBACjB,IAAI,CAAC,QAAQ;sBACV,IAAI,CAAC,WAAW;qBACjB,IAAI,CAAC,UAAU;sBACd,IAAI,CAAC,WAAW;iBACrB,IAAI,CAAC,aAAa;eACpB,IAAI,CAAC,WAAW;eAChB,IAAI,CAAC,WAAW;gBACf,IAAI,CAAC,YAAY;WACtB,IAAI;kBACG,IAAI,CAAC,QAAQ;aAClB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;sBACP,OAAO;aAChB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;sBACP,OAAO;cACf,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;eAChB,MAAM,CAAC,KAAK,CAAC;kBACV,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACZ,SAAS,IAAI,OAAO;uBAChB,aAAa,KAAK,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,MAAgC,EAChC,QAAiB;QAEjB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC;QAC9B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;QACT,CAAC;QACD,sDAAsD;QACtD,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,kBAAkB,CACzB,IAAI,YAAY,CAAC,cAAc,EAAE;gBAC/B,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI,CAAC,eAAe;aAChC,CAAC,CACH,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,kBAAkB,CACzB,IAAI,YAAY,CAAC,cAAc,EAAE;gBAC/B,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI,CAAC,eAAe;aAChC,CAAC,CACH,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,KAAY;QAC9B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,MAA0B,CAAC,CAAC;IACrD,CAAC;IAEO,WAAW,CAAC,KAAY;QAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,MAA0B,CAAC;QAChD,MAAM,KAAK,GACT,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,QAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAW,CAAC;QACjE,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO,EAAE,KAAK,CAAC,IAAI,KAAK,aAAa;YACrC,OAAO,EAAE,KAAK;YACd,MAAM;YACN,KAAK;YACL,MAAM,EAAE,IAAI,GAAG,CAAC;gBACd,CAAC,MAAM,EAAE,MAAM,CAAC,aAAa,CAAC;gBAC9B,CAAC,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC;aAC9B,CAAC;SACH,CAAC;IACJ,CAAC;IAEO,YAAY,CAAC,KAAY;QAC/B,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IAC1B,CAAC;IAEO,aAAa,CAAC,KAAoB;QACxC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAEO,WAAW,CAAC,KAAoB;QACtC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAEO,UAAU,CAAC,KAAmB;QACpC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACxB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,SAAS,CAAC;QACvC,MAAM,OAAO,GAAI,KAAK,CAAC,MAA2B,KAAK,IAAI,CAAC,UAAU,CAAC;QACvE,qEAAqE;QACrE,sCAAsC;QACtC,IAAI,CAAC,gBAAgB;YACnB,CAAC,IAAI,CAAC,QAAQ,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzD,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC9E,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,KAAmB;QACxC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QAED,MAAM,EAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QAC9C,+DAA+D;QAC/D,0BAA0B;QAC1B,MAAM,IAAI,OAAO,CAAC,qBAAqB,CAAC,CAAC;QACzC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,gDAAgD;YAChD,iEAAiE;YACjE,MAAM,CAAC,KAAK,EAAE,CAAC;YACf,kEAAkE;YAClE,oCAAoC;YACpC,IAAI,OAAO,IAAI,MAAM,CAAC,aAAa,KAAK,MAAM,CAAC,GAAG,CAAC,MAAM,CAAE,EAAE,CAAC;gBAC5D,MAAM,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED;;;;;;;;;;;;OAYG;IACK,UAAU,CAAC,KAAmB;QACpC,IAAI,CAAC,gBAAgB,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC5E,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAC1E,CAAC;IAEO,WAAW,CAAC,KAAmB;QACrC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAC9B,CAAC;IAEO,WAAW,CAAC,KAAuB;QACzC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACtD,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,EAAC,MAAM,EAAE,KAAK,EAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QACpC,MAAM,OAAO,GAAG,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC;QAC3C,OAAO,OAAO;YACZ,CAAC,CAAC,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa;YAC5C,CAAC,CAAC,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;IACjD,CAAC;IAED,yEAAyE;IACzE,gEAAgE;IAChE,wBAAwB;IAChB,eAAe;QACrB,MAAM,EAAC,MAAM,EAAC,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,EAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAC,GAAG,MAAM,CAAC;QACvC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC5D,IAAI,UAAU,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;gBACvC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;gBACvB,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;gBACtB,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC;gBACtB,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC;YACxB,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,qEAAqE;IACrE,qBAAqB;IACb,UAAU;QAChB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,EAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QAC5C,MAAM,OAAO,GAAG,MAAM,CAAC,aAAa,KAAK,KAAK,CAAC,aAAa,CAAC;QAC7D,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;QAC3C,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC;QACzC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,iEAAiE;IACzD,WAAW;QACjB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAC1C,OAAO,KAAK,CAAC;QACf,CAAC;QACD,MAAM,EAAC,MAAM,EAAE,KAAK,EAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QACpC,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,WAAW,CAAC,KAAiB;QACnC,yCAAyC;QACzC,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,OAAO;QACT,CAAC;QACD,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;gBAC3B,eAAe,GAAG,IAAI,CAAC;gBACvB,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACjC,CAAC;YACD,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;gBACvB,eAAe,GAAG,IAAI,CAAC;gBACvB,UAAU,GAAG,KAAK,CAAC;YACrB,CAAC;QACH,CAAC;QACD,MAAM,MAAM,GAAG,KAAK,CAAC,MAA0B,CAAC;QAChD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACzB,mCAAmC;QACnC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAW,CAAC,aAAa,CAAC;YACjD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAS,CAAC,aAAa,CAAC;QAC/C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAS,CAAC,aAAa,CAAC;QAC5C,CAAC;QACD,6CAA6C;QAC7C,IAAI,eAAe,EAAE,CAAC;YACpB,KAAK,CAAC,eAAe,EAAE,CAAC;QAC1B,CAAC;QACD,6CAA6C;QAC7C,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACjC,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAC/B,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QACpC,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,KAAY;QAC/B,0DAA0D;QAC1D,sDAAsD;QACtD,MAAM,YAAY,GAAG,KAAK,CAAC,MAA0B,CAAC;QACtD,MAAM,EAAC,MAAM,EAAE,MAAM,EAAC,GAAG,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;QAC3C,MAAM,OAAO,GACX,MAAM,IAAI,MAAM,CAAC,aAAa,KAAK,MAAO,CAAC,GAAG,CAAC,YAAY,CAAE,CAAC;QAChE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC/B,CAAC;QACD,kDAAkD;QAClD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAMQ,CAAC,YAAY,CAAC;QACrB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAEQ,iBAAiB;QACxB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACpD,IAAI,CAAC,UAAU,GAAG,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAChD,IAAI,CAAC,QAAQ,GAAG,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACjE,OAAO;QACT,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACzC,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC1D,CAAC;IAEQ,wBAAwB,CAC/B,KAA8C;QAE9C,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;YAC7C,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;YACrC,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;YACjC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;;AA7sBD,kBAAkB;AACF,wBAAiB,GAAmB;IAClD,GAAG,UAAU,CAAC,iBAAiB;IAC/B,cAAc,EAAE,IAAI;CACrB,AAHgC,CAG/B;AAKwB;IAAzB,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;mCAAS;AAKR;IAAzB,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;mCAAW;AAKV;IAAzB,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;qCAAgB;AAKW;IAAnD,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,EAAC,CAAC;0CAAqB;AAKtB;IAAjD,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAC,CAAC;wCAAmB;AAM9B;IAArC,QAAQ,CAAC,EAAC,SAAS,EAAE,aAAa,EAAC,CAAC;0CAAiB;AAMV;IAA3C,QAAQ,CAAC,EAAC,SAAS,EAAE,mBAAmB,EAAC,CAAC;+CAAsB;AAMvB;IAAzC,QAAQ,CAAC,EAAC,SAAS,EAAE,iBAAiB,EAAC,CAAC;6CAAoB;AAMlB;IAA1C,QAAQ,CAAC,EAAC,SAAS,EAAE,kBAAkB,EAAC,CAAC;8CAAqB;AAMhB;IAA9C,QAAQ,CAAC,EAAC,SAAS,EAAE,sBAAsB,EAAC,CAAC;kDAAyB;AAM9B;IAAxC,QAAQ,CAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAC;4CAAmB;AAMd;IAA5C,QAAQ,CAAC,EAAC,SAAS,EAAE,oBAAoB,EAAC,CAAC;gDAAuB;AAKzC;IAAzB,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;oCAAU;AAKR;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;qCAAe;AAKd;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;uCAAiB;AAOhB;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;qCAAe;AA0BF;IAAtC,KAAK,CAAC,aAAa,CAAC;0CAAuD;AACnC;IAAxC,KAAK,CAAC,eAAe,CAAC;2CAAsD;AAE5D;IADhB,UAAU,CAAC,iBAAiB,CAAC;2CAC0B;AAEnB;IAApC,KAAK,CAAC,WAAW,CAAC;wCAAqD;AACjC;IAAtC,KAAK,CAAC,aAAa,CAAC;yCAAoD;AAExD;IADhB,UAAU,CAAC,eAAe,CAAC;yCAC0B;AAKrC;IAAhB,KAAK,EAAE;gDAAkC;AACzB;IAAhB,KAAK,EAAE;8CAAgC;AAEvB;IAAhB,KAAK,EAAE;0CAA4B;AACnB;IAAhB,KAAK,EAAE;kDAAoC;AAE3B;IAAhB,KAAK,EAAE;gDAAmC;AAC1B;IAAhB,KAAK,EAAE;8CAAiC;AAqkB3C,SAAS,QAAQ,CAAC,EAAC,CAAC,EAAE,CAAC,EAAe,EAAE,OAA4B;IAClE,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,EAAC,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAC,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;IACnE,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,MAAM,CAAC;AAC5D,CAAC;AAED,SAAS,aAAa,CACpB,GAA+B,EAC/B,GAA+B;IAE/B,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC;QAClB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,CAAC,GAAG,GAAG,CAAC,qBAAqB,EAAE,CAAC;IACtC,MAAM,CAAC,GAAG,GAAG,CAAC,qBAAqB,EAAE,CAAC;IACtC,OAAO,CAAC,CACN,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM;QAChB,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI;QAChB,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG;QAChB,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CACjB,CAAC;AACJ,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../elevation/elevation.js';\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, isServer, LitElement, nothing, PropertyValues} from 'lit';\nimport {property, query, queryAsync, state} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\nimport {styleMap} from 'lit/directives/style-map.js';\nimport {when} from 'lit/directives/when.js';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\nimport {\n  dispatchActivationClick,\n  isActivationClick,\n} from '../../internal/events/form-label-activation.js';\nimport {redispatchEvent} from '../../internal/events/redispatch-event.js';\nimport {mixinElementInternals} from '../../labs/behaviors/element-internals.js';\nimport {\n  getFormValue,\n  mixinFormAssociated,\n} from '../../labs/behaviors/form-associated.js';\nimport {MdRipple} from '../../ripple/ripple.js';\n\n// Disable warning for classMap with destructuring\n// tslint:disable:no-implicit-dictionary-conversion\n\n// Separate variable needed for closure.\nconst sliderBaseClass = mixinDelegatesAria(\n  mixinFormAssociated(mixinElementInternals(LitElement)),\n);\n\n/**\n * Slider component.\n *\n *\n * @fires change {Event} The native `change` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/change_event)\n * --bubbles\n * @fires input {InputEvent} The native `input` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/input_event)\n * --bubbles --composed\n */\nexport class Slider extends sliderBaseClass {\n  /** @nocollapse */\n  static override shadowRootOptions: ShadowRootInit = {\n    ...LitElement.shadowRootOptions,\n    delegatesFocus: true,\n  };\n\n  /**\n   * The slider minimum value\n   */\n  @property({type: Number}) min = 0;\n\n  /**\n   * The slider maximum value\n   */\n  @property({type: Number}) max = 100;\n\n  /**\n   * The slider value displayed when range is false.\n   */\n  @property({type: Number}) value?: number;\n\n  /**\n   * The slider start value displayed when range is true.\n   */\n  @property({type: Number, attribute: 'value-start'}) valueStart?: number;\n\n  /**\n   * The slider end value displayed when range is true.\n   */\n  @property({type: Number, attribute: 'value-end'}) valueEnd?: number;\n\n  /**\n   * An optional label for the slider's value displayed when range is\n   * false; if not set, the label is the value itself.\n   */\n  @property({attribute: 'value-label'}) valueLabel = '';\n\n  /**\n   * An optional label for the slider's start value displayed when\n   * range is true; if not set, the label is the valueStart itself.\n   */\n  @property({attribute: 'value-label-start'}) valueLabelStart = '';\n\n  /**\n   * An optional label for the slider's end value displayed when\n   * range is true; if not set, the label is the valueEnd itself.\n   */\n  @property({attribute: 'value-label-end'}) valueLabelEnd = '';\n\n  /**\n   * Aria label for the slider's start handle displayed when\n   * range is true.\n   */\n  @property({attribute: 'aria-label-start'}) ariaLabelStart = '';\n\n  /**\n   * Aria value text for the slider's start value displayed when\n   * range is true.\n   */\n  @property({attribute: 'aria-valuetext-start'}) ariaValueTextStart = '';\n\n  /**\n   * Aria label for the slider's end handle displayed when\n   * range is true.\n   */\n  @property({attribute: 'aria-label-end'}) ariaLabelEnd = '';\n\n  /**\n   * Aria value text for the slider's end value displayed when\n   * range is true.\n   */\n  @property({attribute: 'aria-valuetext-end'}) ariaValueTextEnd = '';\n\n  /**\n   * The step between values.\n   */\n  @property({type: Number}) step = 1;\n\n  /**\n   * Whether or not to show tick marks.\n   */\n  @property({type: Boolean}) ticks = false;\n\n  /**\n   * Whether or not to show a value label when activated.\n   */\n  @property({type: Boolean}) labeled = false;\n\n  /**\n   * Whether or not to show a value range. When false, the slider displays\n   * a slideable handle for the value property; when true, it displays\n   * slideable handles for the valueStart and valueEnd properties.\n   */\n  @property({type: Boolean}) range = false;\n\n  /**\n   * The HTML name to use in form submission for a range slider's starting\n   * value. Use `name` instead if both the start and end values should use the\n   * same name.\n   */\n  get nameStart() {\n    return this.getAttribute('name-start') ?? this.name;\n  }\n  set nameStart(name: string) {\n    this.setAttribute('name-start', name);\n  }\n\n  /**\n   * The HTML name to use in form submission for a range slider's ending value.\n   * Use `name` instead if both the start and end values should use the same\n   * name.\n   */\n  get nameEnd() {\n    return this.getAttribute('name-end') ?? this.nameStart;\n  }\n  set nameEnd(name: string) {\n    this.setAttribute('name-end', name);\n  }\n\n  @query('input.start') private readonly inputStart!: HTMLInputElement | null;\n  @query('.handle.start') private readonly handleStart!: HTMLDivElement | null;\n  @queryAsync('md-ripple.start')\n  private readonly rippleStart!: Promise<MdRipple | null>;\n\n  @query('input.end') private readonly inputEnd!: HTMLInputElement | null;\n  @query('.handle.end') private readonly handleEnd!: HTMLDivElement | null;\n  @queryAsync('md-ripple.end')\n  private readonly rippleEnd!: Promise<MdRipple | null>;\n\n  // handle hover/pressed states are set manually since the handle\n  // does not receive pointer events so that the native inputs are\n  // interaction targets.\n  @state() private handleStartHover = false;\n  @state() private handleEndHover = false;\n\n  @state() private startOnTop = false;\n  @state() private handlesOverlapping = false;\n\n  @state() private renderValueStart?: number;\n  @state() private renderValueEnd?: number;\n\n  // Note: start aria-* properties are only applied when range=true, which is\n  // why they do not need to handle both cases.\n  private get renderAriaLabelStart() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return (\n      this.ariaLabelStart ||\n      (ariaLabel && `${ariaLabel} start`) ||\n      this.valueLabelStart ||\n      String(this.valueStart)\n    );\n  }\n\n  private get renderAriaValueTextStart() {\n    return (\n      this.ariaValueTextStart || this.valueLabelStart || String(this.valueStart)\n    );\n  }\n\n  // Note: end aria-* properties are applied for single and range sliders, which\n  // is why it needs to handle `this.range` (while start aria-* properties do\n  // not).\n  private get renderAriaLabelEnd() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    if (this.range) {\n      return (\n        this.ariaLabelEnd ||\n        (ariaLabel && `${ariaLabel} end`) ||\n        this.valueLabelEnd ||\n        String(this.valueEnd)\n      );\n    }\n\n    return ariaLabel || this.valueLabel || String(this.value);\n  }\n\n  private get renderAriaValueTextEnd() {\n    if (this.range) {\n      return (\n        this.ariaValueTextEnd || this.valueLabelEnd || String(this.valueEnd)\n      );\n    }\n\n    // Needed for conformance\n    const {ariaValueText} = this as ARIAMixinStrict;\n    return ariaValueText || this.valueLabel || String(this.value);\n  }\n\n  // used in synthetic events generated to control ripple hover state.\n  private ripplePointerId = 1;\n\n  // flag to prevent processing of re-dispatched input event.\n  private isRedispatchingEvent = false;\n\n  private action?: Action;\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.addEventListener('click', (event: MouseEvent) => {\n        if (!isActivationClick(event) || !this.inputEnd) {\n          return;\n        }\n        this.focus();\n        dispatchActivationClick(this.inputEnd);\n      });\n    }\n  }\n\n  override focus() {\n    this.inputEnd?.focus();\n  }\n\n  protected override willUpdate(changed: PropertyValues) {\n    this.renderValueStart = changed.has('valueStart')\n      ? this.valueStart\n      : this.inputStart?.valueAsNumber;\n    const endValueChanged =\n      (changed.has('valueEnd') && this.range) || changed.has('value');\n    this.renderValueEnd = endValueChanged\n      ? this.range\n        ? this.valueEnd\n        : this.value\n      : this.inputEnd?.valueAsNumber;\n    // manually handle ripple hover state since the handle is pointer events\n    // none.\n    if (changed.get('handleStartHover') !== undefined) {\n      this.toggleRippleHover(this.rippleStart, this.handleStartHover);\n    } else if (changed.get('handleEndHover') !== undefined) {\n      this.toggleRippleHover(this.rippleEnd, this.handleEndHover);\n    }\n  }\n\n  protected override updated(changed: PropertyValues) {\n    // Validate input rendered value and re-render if necessary. This ensures\n    // the rendred handle stays in sync with the input thumb which is used for\n    // interaction. These can get out of sync if a supplied value does not\n    // map to an exactly stepped value between min and max.\n    if (this.range) {\n      this.renderValueStart = this.inputStart!.valueAsNumber;\n    }\n    this.renderValueEnd = this.inputEnd!.valueAsNumber;\n    // update values if they are unset\n    // when using a range, default to equi-distant between\n    // min - valueStart - valueEnd - max\n    if (this.range) {\n      const segment = (this.max - this.min) / 3;\n      if (this.valueStart === undefined) {\n        this.inputStart!.valueAsNumber = this.min + segment;\n        // read actual value from input\n        const v = this.inputStart!.valueAsNumber;\n        this.valueStart = this.renderValueStart = v;\n      }\n      if (this.valueEnd === undefined) {\n        this.inputEnd!.valueAsNumber = this.min + 2 * segment;\n        // read actual value from input\n        const v = this.inputEnd!.valueAsNumber;\n        this.valueEnd = this.renderValueEnd = v;\n      }\n    } else {\n      this.value ??= this.renderValueEnd;\n    }\n    if (\n      changed.has('range') ||\n      changed.has('renderValueStart') ||\n      changed.has('renderValueEnd') ||\n      this.isUpdatePending\n    ) {\n      // Only check if the handle nubs are overlapping, as the ripple touch\n      // target extends subtantially beyond the boundary of the handle nub.\n      const startNub = this.handleStart?.querySelector('.handleNub');\n      const endNub = this.handleEnd?.querySelector('.handleNub');\n      this.handlesOverlapping = isOverlapping(startNub, endNub);\n    }\n    // called to finish the update imediately;\n    // note, this is a no-op unless an update is scheduled\n    this.performUpdate();\n  }\n\n  protected override render() {\n    const step = this.step === 0 ? 1 : this.step;\n    const range = Math.max(this.max - this.min, step);\n    const startFraction = this.range\n      ? ((this.renderValueStart ?? this.min) - this.min) / range\n      : 0;\n    const endFraction = ((this.renderValueEnd ?? this.min) - this.min) / range;\n    const containerStyles = {\n      // for clipping inputs and active track.\n      '--_start-fraction': String(startFraction),\n      '--_end-fraction': String(endFraction),\n      // for generating tick marks\n      '--_tick-count': String(range / step),\n    };\n    const containerClasses = {ranged: this.range};\n\n    // optional label values to show in place of the value.\n    const labelStart = this.valueLabelStart || String(this.renderValueStart);\n    const labelEnd =\n      (this.range ? this.valueLabelEnd : this.valueLabel) ||\n      String(this.renderValueEnd);\n\n    const inputStartProps = {\n      start: true,\n      value: this.renderValueStart,\n      ariaLabel: this.renderAriaLabelStart,\n      ariaValueText: this.renderAriaValueTextStart,\n      ariaMin: this.min,\n      ariaMax: this.valueEnd ?? this.max,\n    };\n\n    const inputEndProps = {\n      start: false,\n      value: this.renderValueEnd,\n      ariaLabel: this.renderAriaLabelEnd,\n      ariaValueText: this.renderAriaValueTextEnd,\n      ariaMin: this.range ? this.valueStart ?? this.min : this.min,\n      ariaMax: this.max,\n    };\n\n    const handleStartProps = {\n      start: true,\n      hover: this.handleStartHover,\n      label: labelStart,\n    };\n\n    const handleEndProps = {\n      start: false,\n      hover: this.handleEndHover,\n      label: labelEnd,\n    };\n\n    const handleContainerClasses = {\n      hover: this.handleStartHover || this.handleEndHover,\n    };\n\n    return html` <div\n      class=\"container ${classMap(containerClasses)}\"\n      style=${styleMap(containerStyles)}>\n      ${when(this.range, () => this.renderInput(inputStartProps))}\n      ${this.renderInput(inputEndProps)} ${this.renderTrack()}\n      <div class=\"handleContainerPadded\">\n        <div class=\"handleContainerBlock\">\n          <div class=\"handleContainer ${classMap(handleContainerClasses)}\">\n            ${when(this.range, () => this.renderHandle(handleStartProps))}\n            ${this.renderHandle(handleEndProps)}\n          </div>\n        </div>\n      </div>\n    </div>`;\n  }\n\n  private renderTrack() {\n    return html`\n      <div class=\"track\"></div>\n      ${this.ticks ? html`<div class=\"tickmarks\"></div>` : nothing}\n    `;\n  }\n\n  private renderLabel(value: string) {\n    return html`<div class=\"label\" aria-hidden=\"true\">\n      <span class=\"labelContent\" part=\"label\">${value}</span>\n    </div>`;\n  }\n\n  private renderHandle({\n    start,\n    hover,\n    label,\n  }: {\n    start: boolean;\n    hover: boolean;\n    label: string;\n  }) {\n    const onTop = !this.disabled && start === this.startOnTop;\n    const isOverlapping = !this.disabled && this.handlesOverlapping;\n    const name = start ? 'start' : 'end';\n    return html`<div\n      class=\"handle ${classMap({\n        [name]: true,\n        hover,\n        onTop,\n        isOverlapping,\n      })}\">\n      <md-focus-ring part=\"focus-ring\" for=${name}></md-focus-ring>\n      <md-ripple\n        for=${name}\n        class=${name}\n        ?disabled=${this.disabled}></md-ripple>\n      <div class=\"handleNub\">\n        <md-elevation part=\"elevation\"></md-elevation>\n      </div>\n      ${when(this.labeled, () => this.renderLabel(label))}\n    </div>`;\n  }\n\n  private renderInput({\n    start,\n    value,\n    ariaLabel,\n    ariaValueText,\n    ariaMin,\n    ariaMax,\n  }: {\n    start: boolean;\n    value?: number;\n    ariaLabel: string;\n    ariaValueText: string;\n    ariaMin: number;\n    ariaMax: number;\n  }) {\n    // Slider requires min/max set to the overall min/max for both inputs.\n    // This is reported to screen readers, which is why we need aria-valuemin\n    // and aria-valuemax.\n    const name = start ? `start` : `end`;\n    return html`<input\n      type=\"range\"\n      class=\"${classMap({\n        start,\n        end: !start,\n      })}\"\n      @focus=${this.handleFocus}\n      @pointerdown=${this.handleDown}\n      @pointerup=${this.handleUp}\n      @pointerenter=${this.handleEnter}\n      @pointermove=${this.handleMove}\n      @pointerleave=${this.handleLeave}\n      @keydown=${this.handleKeydown}\n      @keyup=${this.handleKeyup}\n      @input=${this.handleInput}\n      @change=${this.handleChange}\n      id=${name}\n      .disabled=${this.disabled}\n      .min=${String(this.min)}\n      aria-valuemin=${ariaMin}\n      .max=${String(this.max)}\n      aria-valuemax=${ariaMax}\n      .step=${String(this.step)}\n      .value=${String(value)}\n      .tabIndex=${start ? 1 : 0}\n      aria-label=${ariaLabel || nothing}\n      aria-valuetext=${ariaValueText} />`;\n  }\n\n  private async toggleRippleHover(\n    ripple: Promise<MdRipple | null>,\n    hovering: boolean,\n  ) {\n    const rippleEl = await ripple;\n    if (!rippleEl) {\n      return;\n    }\n    // TODO(b/269799771): improve slider ripple connection\n    if (hovering) {\n      rippleEl.handlePointerenter(\n        new PointerEvent('pointerenter', {\n          isPrimary: true,\n          pointerId: this.ripplePointerId,\n        }),\n      );\n    } else {\n      rippleEl.handlePointerleave(\n        new PointerEvent('pointerleave', {\n          isPrimary: true,\n          pointerId: this.ripplePointerId,\n        }),\n      );\n    }\n  }\n\n  private handleFocus(event: Event) {\n    this.updateOnTop(event.target as HTMLInputElement);\n  }\n\n  private startAction(event: Event) {\n    const target = event.target as HTMLInputElement;\n    const fixed =\n      target === this.inputStart ? this.inputEnd! : this.inputStart!;\n    this.action = {\n      canFlip: event.type === 'pointerdown',\n      flipped: false,\n      target,\n      fixed,\n      values: new Map([\n        [target, target.valueAsNumber],\n        [fixed, fixed?.valueAsNumber],\n      ]),\n    };\n  }\n\n  private finishAction(event: Event) {\n    this.action = undefined;\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    this.startAction(event);\n  }\n\n  private handleKeyup(event: KeyboardEvent) {\n    this.finishAction(event);\n  }\n\n  private handleDown(event: PointerEvent) {\n    this.startAction(event);\n    this.ripplePointerId = event.pointerId;\n    const isStart = (event.target as HTMLInputElement) === this.inputStart;\n    // Since handle moves to pointer on down and there may not be a move,\n    // it needs to be considered hovered..\n    this.handleStartHover =\n      !this.disabled && isStart && Boolean(this.handleStart);\n    this.handleEndHover = !this.disabled && !isStart && Boolean(this.handleEnd);\n  }\n\n  private async handleUp(event: PointerEvent) {\n    if (!this.action) {\n      return;\n    }\n\n    const {target, values, flipped} = this.action;\n    //  Async here for Firefox because input can be after pointerup\n    //  when value is calmped.\n    await new Promise(requestAnimationFrame);\n    if (target !== undefined) {\n      // Ensure Safari focuses input so label renders.\n      // Ensure any flipped input is focused so the tab order is right.\n      target.focus();\n      // When action is flipped, change must be fired manually since the\n      // real event target did not change.\n      if (flipped && target.valueAsNumber !== values.get(target)!) {\n        target.dispatchEvent(new Event('change', {bubbles: true}));\n      }\n    }\n    this.finishAction(event);\n  }\n\n  /**\n   * The move handler tracks handle hovering to facilitate proper ripple\n   * behavior on the slider handle. This is needed because user interaction with\n   * the native input is leveraged to position the handle. Because the separate\n   * displayed handle element has pointer events disabled (to allow interaction\n   * with the input) and the input's handle is a pseudo-element, neither can be\n   * the ripple's interactive element. Therefore the input is the ripple's\n   * interactive element and has a `ripple` directive; however the ripple\n   * is gated on the handle being hovered. In addition, because the ripple\n   * hover state is being specially handled, it must be triggered independent\n   * of the directive. This is done based on the hover state when the\n   * slider is updated.\n   */\n  private handleMove(event: PointerEvent) {\n    this.handleStartHover = !this.disabled && inBounds(event, this.handleStart);\n    this.handleEndHover = !this.disabled && inBounds(event, this.handleEnd);\n  }\n\n  private handleEnter(event: PointerEvent) {\n    this.handleMove(event);\n  }\n\n  private handleLeave() {\n    this.handleStartHover = false;\n    this.handleEndHover = false;\n  }\n\n  private updateOnTop(input: HTMLInputElement) {\n    this.startOnTop = input.classList.contains('start');\n  }\n\n  private needsClamping() {\n    if (!this.action) {\n      return false;\n    }\n\n    const {target, fixed} = this.action;\n    const isStart = target === this.inputStart;\n    return isStart\n      ? target.valueAsNumber > fixed.valueAsNumber\n      : target.valueAsNumber < fixed.valueAsNumber;\n  }\n\n  // if start/end start coincident and the first drag input would e.g. move\n  // start > end, avoid clamping and \"flip\" to use the other input\n  // as the action target.\n  private isActionFlipped() {\n    const {action} = this;\n    if (!action) {\n      return false;\n    }\n\n    const {target, fixed, values} = action;\n    if (action.canFlip) {\n      const coincident = values.get(target) === values.get(fixed);\n      if (coincident && this.needsClamping()) {\n        action.canFlip = false;\n        action.flipped = true;\n        action.target = fixed;\n        action.fixed = target;\n      }\n    }\n    return action.flipped;\n  }\n\n  // when flipped, apply the drag input to the flipped target and reset\n  // the actual target.\n  private flipAction() {\n    if (!this.action) {\n      return false;\n    }\n\n    const {target, fixed, values} = this.action;\n    const changed = target.valueAsNumber !== fixed.valueAsNumber;\n    target.valueAsNumber = fixed.valueAsNumber;\n    fixed.valueAsNumber = values.get(fixed)!;\n    return changed;\n  }\n\n  // clamp such that start does not move beyond end and visa versa.\n  private clampAction() {\n    if (!this.needsClamping() || !this.action) {\n      return false;\n    }\n    const {target, fixed} = this.action;\n    target.valueAsNumber = fixed.valueAsNumber;\n    return true;\n  }\n\n  private handleInput(event: InputEvent) {\n    // avoid processing a re-dispatched event\n    if (this.isRedispatchingEvent) {\n      return;\n    }\n    let stopPropagation = false;\n    let redispatch = false;\n    if (this.range) {\n      if (this.isActionFlipped()) {\n        stopPropagation = true;\n        redispatch = this.flipAction();\n      }\n      if (this.clampAction()) {\n        stopPropagation = true;\n        redispatch = false;\n      }\n    }\n    const target = event.target as HTMLInputElement;\n    this.updateOnTop(target);\n    // update value only on interaction\n    if (this.range) {\n      this.valueStart = this.inputStart!.valueAsNumber;\n      this.valueEnd = this.inputEnd!.valueAsNumber;\n    } else {\n      this.value = this.inputEnd!.valueAsNumber;\n    }\n    // control external visibility of input event\n    if (stopPropagation) {\n      event.stopPropagation();\n    }\n    // ensure event path is correct when flipped.\n    if (redispatch) {\n      this.isRedispatchingEvent = true;\n      redispatchEvent(target, event);\n      this.isRedispatchingEvent = false;\n    }\n  }\n\n  private handleChange(event: Event) {\n    // prevent keyboard triggered changes from dispatching for\n    // clamped values; note, this only occurs for keyboard\n    const changeTarget = event.target as HTMLInputElement;\n    const {target, values} = this.action ?? {};\n    const squelch =\n      target && target.valueAsNumber === values!.get(changeTarget)!;\n    if (!squelch) {\n      redispatchEvent(this, event);\n    }\n    // ensure keyboard triggered change clears action.\n    this.finishAction(event);\n  }\n\n  // Writable mixin properties for lit-html binding, needed for lit-analyzer\n  declare disabled: boolean;\n  declare name: string;\n\n  override [getFormValue]() {\n    if (this.range) {\n      const data = new FormData();\n      data.append(this.nameStart, String(this.valueStart));\n      data.append(this.nameEnd, String(this.valueEnd));\n      return data;\n    }\n\n    return String(this.value);\n  }\n\n  override formResetCallback() {\n    if (this.range) {\n      const valueStart = this.getAttribute('value-start');\n      this.valueStart = valueStart !== null ? Number(valueStart) : undefined;\n      const valueEnd = this.getAttribute('value-end');\n      this.valueEnd = valueEnd !== null ? Number(valueEnd) : undefined;\n      return;\n    }\n    const value = this.getAttribute('value');\n    this.value = value !== null ? Number(value) : undefined;\n  }\n\n  override formStateRestoreCallback(\n    state: string | Array<[string, string]> | null,\n  ) {\n    if (Array.isArray(state)) {\n      const [[, valueStart], [, valueEnd]] = state;\n      this.valueStart = Number(valueStart);\n      this.valueEnd = Number(valueEnd);\n      this.range = true;\n      return;\n    }\n\n    this.value = Number(state);\n    this.range = false;\n  }\n}\n\nfunction inBounds({x, y}: PointerEvent, element?: HTMLElement | null) {\n  if (!element) {\n    return false;\n  }\n  const {top, left, bottom, right} = element.getBoundingClientRect();\n  return x >= left && x <= right && y >= top && y <= bottom;\n}\n\nfunction isOverlapping(\n  elA: Element | null | undefined,\n  elB: Element | null | undefined,\n) {\n  if (!(elA && elB)) {\n    return false;\n  }\n  const a = elA.getBoundingClientRect();\n  const b = elB.getBoundingClientRect();\n  return !(\n    a.top > b.bottom ||\n    a.right < b.left ||\n    a.bottom < b.top ||\n    a.left > b.right\n  );\n}\n\ninterface Action {\n  canFlip: boolean;\n  flipped: boolean;\n  target: HTMLInputElement;\n  fixed: HTMLInputElement;\n  values: Map<HTMLInputElement | undefined, number | undefined>;\n}\n"]}