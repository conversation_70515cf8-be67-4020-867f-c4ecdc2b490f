{"version": 3, "file": "validator.js", "sourceRoot": "", "sources": ["validator.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;;;;;;;;;GAUG;AACH,MAAM,OAAgB,SAAS;IAgB7B;;;;;OAKG;IACH,YAA6B,eAA4B;QAA5B,oBAAe,GAAf,eAAe,CAAa;QAfzD;;;WAGG;QACK,oBAAe,GAAuB;YAC5C,QAAQ,EAAE,EAAE;YACZ,iBAAiB,EAAE,EAAE;SACtB,CAAC;IAQ0D,CAAC;IAE7D;;;;;;;;;OASG;IACH,WAAW;QACT,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,eAAe,GACnB,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACzD,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;QAED,MAAM,EAAC,QAAQ,EAAE,iBAAiB,EAAC,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAClE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,eAAe,GAAG;YACrB,iBAAiB;YACjB,QAAQ,EAAE;gBACR,uEAAuE;gBACvE,kDAAkD;gBAClD,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,eAAe,EAAE,QAAQ,CAAC,eAAe;gBACzC,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,cAAc,EAAE,QAAQ,CAAC,cAAc;gBACvC,YAAY,EAAE,QAAQ,CAAC,YAAY;gBACnC,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,YAAY,EAAE,QAAQ,CAAC,YAAY;gBACnC,YAAY,EAAE,QAAQ,CAAC,YAAY;aACpC;SACF,CAAC;QAEF,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;CAsCF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * A class that computes and caches `ValidityStateFlags` for a component with\n * a given `State` interface.\n *\n * Cached performance before computing validity is important since constraint\n * validation must be checked frequently and synchronously when properties\n * change.\n *\n * @template State The expected interface of properties relevant to constraint\n *     validation.\n */\nexport abstract class Validator<State> {\n  /**\n   * The previous state, used to determine if state changed and validation needs\n   * to be re-computed.\n   */\n  private prevState?: State;\n\n  /**\n   * The current validity state and message. This is cached and returns if\n   * constraint validation state does not change.\n   */\n  private currentValidity: ValidityAndMessage = {\n    validity: {},\n    validationMessage: '',\n  };\n\n  /**\n   * Creates a new validator.\n   *\n   * @param getCurrentState A callback that returns the current state of\n   *     constraint validation-related properties.\n   */\n  constructor(private readonly getCurrentState: () => State) {}\n\n  /**\n   * Returns the current `ValidityStateFlags` and validation message for the\n   * validator.\n   *\n   * If the constraint validation state has not changed, this will return a\n   * cached result. This is important since `getValidity()` can be called\n   * frequently in response to synchronous property changes.\n   *\n   * @return The current validity and validation message.\n   */\n  getValidity(): ValidityAndMessage {\n    const state = this.getCurrentState();\n    const hasStateChanged =\n      !this.prevState || !this.equals(this.prevState, state);\n    if (!hasStateChanged) {\n      return this.currentValidity;\n    }\n\n    const {validity, validationMessage} = this.computeValidity(state);\n    this.prevState = this.copy(state);\n    this.currentValidity = {\n      validationMessage,\n      validity: {\n        // Change any `ValidityState` instances into `ValidityStateFlags` since\n        // `ValidityState` cannot be easily `{...spread}`.\n        badInput: validity.badInput,\n        customError: validity.customError,\n        patternMismatch: validity.patternMismatch,\n        rangeOverflow: validity.rangeOverflow,\n        rangeUnderflow: validity.rangeUnderflow,\n        stepMismatch: validity.stepMismatch,\n        tooLong: validity.tooLong,\n        tooShort: validity.tooShort,\n        typeMismatch: validity.typeMismatch,\n        valueMissing: validity.valueMissing,\n      },\n    };\n\n    return this.currentValidity;\n  }\n\n  /**\n   * Computes the `ValidityStateFlags` and validation message for a given set\n   * of constraint validation properties.\n   *\n   * Implementations can use platform elements like `<input>` and `<select>` to\n   * sync state and compute validation along with i18n'd messages. This function\n   * may be expensive, and is only called when state changes.\n   *\n   * @param state The new state of constraint validation properties.\n   * @return An object containing a `validity` property with\n   *     `ValidityStateFlags` and a `validationMessage` property.\n   */\n  protected abstract computeValidity(state: State): ValidityAndMessage;\n\n  /**\n   * Checks if two states are equal. This is used to check against cached state\n   * to see if validity needs to be re-computed.\n   *\n   * @param prev The previous state.\n   * @param next The next state.\n   * @return True if the states are equal, or false if not.\n   */\n  protected abstract equals(prev: State, next: State): boolean;\n\n  /**\n   * Creates a copy of a state. This is used to cache state and check if it\n   * changes.\n   *\n   * Note: do NOT spread the {...state} to copy it. The actual state object is\n   * a web component, and trying to spread its getter/setter properties won't\n   * work.\n   *\n   * @param state The state to copy.\n   * @return A copy of the state.\n   */\n  protected abstract copy(state: State): State;\n}\n\n/**\n * An object containing `ValidityStateFlags` and a corresponding validation\n * message.\n */\nexport interface ValidityAndMessage {\n  /**\n   * Validity flags.\n   */\n  validity: ValidityStateFlags;\n\n  /**\n   * The validation message for the associated flags. It may not be an empty\n   * string if any of the validity flags are `true`.\n   */\n  validationMessage: string;\n}\n"]}