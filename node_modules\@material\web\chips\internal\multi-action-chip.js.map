{"version": 3, "file": "multi-action-chip.js", "sourceRoot": "", "sources": ["multi-action-chip.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAC,MAAM,KAAK,CAAC;AAInC,OAAO,EAAC,IAAI,EAAC,MAAM,WAAW,CAAC;AAE/B,MAAM,iBAAiB,GAAG,mBAAmB,CAAC;AAE9C;;GAEG;AACH,MAAM,OAAgB,eAAgB,SAAQ,IAAI;IAChD,IAAI,eAAe;QACjB,IAAI,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAE,CAAC;QAC/C,CAAC;QAED,MAAM,EAAC,SAAS,EAAC,GAAG,IAAuB,CAAC;QAE5C,yEAAyE;QACzE,IAAI,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC5B,OAAO,UAAU,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;QAC7C,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,eAAe,CAAC,SAAwB;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC;QAClC,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAKD;QACE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3E,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAEQ,KAAK,CAAC,OAA6C;QAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC3D,IAAI,WAAW,IAAI,OAAO,EAAE,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5D,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACnC,OAAO;QACT,CAAC;QAED,KAAK,CAAC,KAAK,CAAC,OAAuB,CAAC,CAAC;IACvC,CAAC;IAEkB,sBAAsB;QACvC,OAAO,IAAI,CAAA;QACP,KAAK,CAAC,sBAAsB,EAAE;QAC9B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,yBAAyB,CAAC;KAC5D,CAAC;IACJ,CAAC;IAMO,aAAa,CAAC,KAAoB;QACxC,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,KAAK,WAAW,CAAC;QACzC,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,KAAK,YAAY,CAAC;QAC3C,8BAA8B;QAC9B,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YAChD,kCAAkC;YAClC,OAAO;QACT,CAAC;QAED,wCAAwC;QACxC,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC;QACzD,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;QAC1C,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;QACtE,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;QAExE,IAAI,CAAC,QAAQ,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC,QAAQ,IAAI,gBAAgB,CAAC,EAAE,CAAC;YACvE,kEAAkE;YAClE,OAAO;QACT,CAAC;QAED,mDAAmD;QACnD,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,uDAAuD;QACvD,KAAK,CAAC,eAAe,EAAE,CAAC;QACxB,MAAM,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;QAC1E,aAAa,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAEO,yBAAyB;QAC/B,MAAM,EAAC,aAAa,EAAE,cAAc,EAAC,GAAG,IAAI,CAAC;QAC7C,IAAI,CAAC,aAAa,IAAI,CAAC,cAAc,EAAE,CAAC;YACtC,OAAO;QACT,CAAC;QAED,sEAAsE;QACtE,yEAAyE;QACzE,4CAA4C;QAC5C,aAAa,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QAC5B,cAAc,CAAC,gBAAgB,CAC7B,UAAU,EACV,GAAG,EAAE;YACH,aAAa,CAAC,QAAQ,GAAG,CAAC,CAAC;QAC7B,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,EAAC,CACb,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, isServer} from 'lit';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\n\nimport {Chip} from './chip.js';\n\nconst ARIA_LABEL_REMOVE = 'aria-label-remove';\n\n/**\n * A chip component with multiple actions.\n */\nexport abstract class MultiActionChip extends Chip {\n  get ariaLabelRemove(): string | null {\n    if (this.hasAttribute(ARIA_LABEL_REMOVE)) {\n      return this.getAttribute(ARIA_LABEL_REMOVE)!;\n    }\n\n    const {ariaLabel} = this as ARIAMixinStrict;\n\n    // TODO(b/350810013): remove `this.label` when label property is removed.\n    if (ariaLabel || this.label) {\n      return `Remove ${ariaLabel || this.label}`;\n    }\n\n    return null;\n  }\n\n  set ariaLabelRemove(ariaLabel: string | null) {\n    const prev = this.ariaLabelRemove;\n    if (ariaLabel === prev) {\n      return;\n    }\n\n    if (ariaLabel === null) {\n      this.removeAttribute(ARIA_LABEL_REMOVE);\n    } else {\n      this.setAttribute(ARIA_LABEL_REMOVE, ariaLabel);\n    }\n\n    this.requestUpdate();\n  }\n\n  protected abstract readonly primaryAction: HTMLElement | null;\n  protected abstract readonly trailingAction: HTMLElement | null;\n\n  constructor() {\n    super();\n    this.handleTrailingActionFocus = this.handleTrailingActionFocus.bind(this);\n    if (!isServer) {\n      this.addEventListener('keydown', this.handleKeyDown.bind(this));\n    }\n  }\n\n  override focus(options?: FocusOptions & {trailing?: boolean}) {\n    const isFocusable = this.alwaysFocusable || !this.disabled;\n    if (isFocusable && options?.trailing && this.trailingAction) {\n      this.trailingAction.focus(options);\n      return;\n    }\n\n    super.focus(options as FocusOptions);\n  }\n\n  protected override renderContainerContent() {\n    return html`\n      ${super.renderContainerContent()}\n      ${this.renderTrailingAction(this.handleTrailingActionFocus)}\n    `;\n  }\n\n  protected abstract renderTrailingAction(\n    focusListener: EventListener,\n  ): unknown;\n\n  private handleKeyDown(event: KeyboardEvent) {\n    const isLeft = event.key === 'ArrowLeft';\n    const isRight = event.key === 'ArrowRight';\n    // Ignore non-navigation keys.\n    if (!isLeft && !isRight) {\n      return;\n    }\n\n    if (!this.primaryAction || !this.trailingAction) {\n      // Does not have multiple actions.\n      return;\n    }\n\n    // Check if moving forwards or backwards\n    const isRtl = getComputedStyle(this).direction === 'rtl';\n    const forwards = isRtl ? isLeft : isRight;\n    const isPrimaryFocused = this.primaryAction?.matches(':focus-within');\n    const isTrailingFocused = this.trailingAction?.matches(':focus-within');\n\n    if ((forwards && isTrailingFocused) || (!forwards && isPrimaryFocused)) {\n      // Moving outside of the chip, it will be handled by the chip set.\n      return;\n    }\n\n    // Prevent default interactions, such as scrolling.\n    event.preventDefault();\n    // Don't let the chip set handle this navigation event.\n    event.stopPropagation();\n    const actionToFocus = forwards ? this.trailingAction : this.primaryAction;\n    actionToFocus.focus();\n  }\n\n  private handleTrailingActionFocus() {\n    const {primaryAction, trailingAction} = this;\n    if (!primaryAction || !trailingAction) {\n      return;\n    }\n\n    // Temporarily turn off the primary action's focusability. This allows\n    // shift+tab from the trailing action to move to the previous chip rather\n    // than the primary action in the same chip.\n    primaryAction.tabIndex = -1;\n    trailingAction.addEventListener(\n      'focusout',\n      () => {\n        primaryAction.tabIndex = 0;\n      },\n      {once: true},\n    );\n  }\n}\n"]}