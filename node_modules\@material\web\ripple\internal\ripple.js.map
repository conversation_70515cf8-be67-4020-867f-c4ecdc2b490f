{"version": 3, "file": "ripple.js", "sourceRoot": "", "sources": ["ripple.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAiB,MAAM,KAAK,CAAC;AAC/D,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAC,MAAM,mBAAmB,CAAC;AACzD,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAErD,OAAO,EAEL,oBAAoB,GACrB,MAAM,oDAAoD,CAAC;AAC5D,OAAO,EAAC,MAAM,EAAC,MAAM,oCAAoC,CAAC;AAE1D,MAAM,aAAa,GAAG,GAAG,CAAC;AAC1B,MAAM,gBAAgB,GAAG,GAAG,CAAC;AAC7B,MAAM,oBAAoB,GAAG,GAAG,CAAC;AACjC,MAAM,OAAO,GAAG,EAAE,CAAC;AACnB,MAAM,sBAAsB,GAAG,EAAE,CAAC;AAClC,MAAM,yBAAyB,GAAG,IAAI,CAAC;AACvC,MAAM,YAAY,GAAG,SAAS,CAAC;AAC/B,MAAM,cAAc,GAAG,UAAU,CAAC;AAElC;;;;;;;;;GASG;AACH,IAAK,KAiCJ;AAjCD,WAAK,KAAK;IACR;;;;;;OAMG;IACH,yCAAQ,CAAA;IACR;;;;;;;;OAQG;IACH,+CAAW,CAAA;IACX;;;;;OAKG;IACH,uCAAO,CAAA;IACP;;;;;OAKG;IACH,2DAAiB,CAAA;AACnB,CAAC,EAjCI,KAAK,KAAL,KAAK,QAiCT;AAED;;GAEG;AACH,MAAM,MAAM,GAAG;IACb,OAAO;IACP,aAAa;IACb,eAAe;IACf,aAAa;IACb,cAAc;IACd,cAAc;IACd,WAAW;CACZ,CAAC;AAEF;;;GAGG;AACH,MAAM,cAAc,GAAG,GAAG,CAAC;AAE3B;;;GAGG;AACH,MAAM,aAAa,GAAG,QAAQ;IAC5B,CAAC,CAAC,IAAI;IACN,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,yBAAyB,CAAC,CAAC;AAEjD;;GAEG;AACH,MAAM,OAAO,MAAO,SAAQ,UAAU;IAAtC;;QACE;;WAEG;QACuC,aAAQ,GAAG,KAAK,CAAC;QAiB1C,YAAO,GAAG,KAAK,CAAC;QAChB,YAAO,GAAG,KAAK,CAAC;QAGzB,eAAU,GAAG,EAAE,CAAC;QAChB,gBAAW,GAAG,EAAE,CAAC;QACjB,gBAAW,GAAG,CAAC,CAAC;QAEhB,UAAK,GAAG,KAAK,CAAC,QAAQ,CAAC;QAEvB,gCAA2B,GAAG,KAAK,CAAC;QAC3B,yBAAoB,GAAG,IAAI,oBAAoB,CAC9D,IAAI,EACJ,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC,CAAC;IAkWJ,CAAC;IA/XC,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;IAC3C,CAAC;IAED,IAAI,OAAO,CAAC,OAAsB;QAChC,IAAI,CAAC,oBAAoB,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9C,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;IAC3C,CAAC;IACD,IAAI,OAAO,CAAC,OAA2B;QACrC,IAAI,CAAC,oBAAoB,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9C,CAAC;IAkBD,MAAM,CAAC,OAAoB;QACzB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC;IACrC,CAAC;IAEQ,iBAAiB;QACxB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,wEAAwE;QACxE,4BAA4B;QAC5B,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAEkB,MAAM;QACvB,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,IAAI,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI,CAAC,OAAO;SACxB,CAAC;QAEF,OAAO,IAAI,CAAA,uBAAuB,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC;IAChE,CAAC;IAEkB,MAAM,CAAC,YAAoC;QAC5D,IAAI,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACvB,CAAC;QACD,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACH,kBAAkB,CAAC,KAAmB;QACpC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IAED;;;OAGG;IACH,kBAAkB,CAAC,KAAmB;QACpC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QAErB,mEAAmE;QACnE,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,QAAQ,EAAE,CAAC;YAClC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,KAAmB;QACzC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,iBAAiB,CAAC;YACrC,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,iBAAiB,CAAC;YACrC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAChD,OAAO;QACT,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,KAAmB;QACjD,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,iBAAiB,CAAC;YACrC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAChC,OAAO;QACT,CAAC;QAED,qEAAqE;QACrE,mEAAmE;QACnE,sCAAsC;QACtC,IAAI,IAAI,CAAC,2BAA2B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9D,OAAO;QACT,CAAC;QAED,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;QAEzC,oCAAoC;QACpC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC;QAC/B,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC5B,UAAU,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,WAAW,EAAE,CAAC;YACrC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC;QAC3B,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAEO,WAAW;QACjB,gEAAgE;QAChE,uBAAuB;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC3C,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,QAAQ,EAAE,CAAC;YAClC,mCAAmC;YACnC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,KAAmB;QAC7C,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAEO,iBAAiB;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;QACxC,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAEO,mBAAmB;QACzB,MAAM,EAAC,MAAM,EAAE,KAAK,EAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACrD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACvC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAC3B,yBAAyB,GAAG,MAAM,EAClC,sBAAsB,CACvB,CAAC;QAEF,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,oBAAoB,CAAC,CAAC;QAC9D,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC;QACvD,MAAM,SAAS,GAAG,UAAU,GAAG,OAAO,CAAC;QAEvC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,SAAS,GAAG,YAAY,CAAC,GAAG,WAAW,EAAE,CAAC;QACjE,IAAI,CAAC,UAAU,GAAG,GAAG,WAAW,IAAI,CAAC;IACvC,CAAC;IAEO,+BAA+B,CAAC,YAA0B;QAIhE,MAAM,EAAC,OAAO,EAAE,OAAO,EAAC,GAAG,MAAM,CAAC;QAClC,MAAM,EAAC,IAAI,EAAE,GAAG,EAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACjD,MAAM,SAAS,GAAG,OAAO,GAAG,IAAI,CAAC;QACjC,MAAM,SAAS,GAAG,OAAO,GAAG,GAAG,CAAC;QAChC,MAAM,EAAC,KAAK,EAAE,KAAK,EAAC,GAAG,YAAY,CAAC;QACpC,OAAO,EAAC,CAAC,EAAE,KAAK,GAAG,SAAS,EAAE,CAAC,EAAE,KAAK,GAAG,SAAS,EAAC,CAAC;IACtD,CAAC;IAEO,yBAAyB,CAAC,aAAqB;QACrD,MAAM,EAAC,MAAM,EAAE,KAAK,EAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACrD,oBAAoB;QACpB,MAAM,QAAQ,GAAG;YACf,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;YACjC,CAAC,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;SACnC,CAAC;QAEF,IAAI,UAAU,CAAC;QACf,IAAI,aAAa,YAAY,YAAY,EAAE,CAAC;YAC1C,UAAU,GAAG,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,CAAC;QACnE,CAAC;aAAM,CAAC;YACN,UAAU,GAAG;gBACX,CAAC,EAAE,KAAK,GAAG,CAAC;gBACZ,CAAC,EAAE,MAAM,GAAG,CAAC;aACd,CAAC;QACJ,CAAC;QAED,4BAA4B;QAC5B,UAAU,GAAG;YACX,CAAC,EAAE,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC;YACtC,CAAC,EAAE,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC;SACvC,CAAC;QAEF,OAAO,EAAC,UAAU,EAAE,QAAQ,EAAC,CAAC;IAChC,CAAC;IAEO,mBAAmB,CAAC,aAAqB;QAC/C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,CAAC;QAC7B,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,MAAM,EAAC,UAAU,EAAE,QAAQ,EAAC,GAC1B,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAC;QAChD,MAAM,cAAc,GAAG,GAAG,UAAU,CAAC,CAAC,OAAO,UAAU,CAAC,CAAC,IAAI,CAAC;QAC9D,MAAM,YAAY,GAAG,GAAG,QAAQ,CAAC,CAAC,OAAO,QAAQ,CAAC,CAAC,IAAI,CAAC;QAExD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CACtC;YACE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YACX,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YACZ,MAAM,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC;YAC1C,KAAK,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC;YACzC,SAAS,EAAE;gBACT,aAAa,cAAc,YAAY;gBACvC,aAAa,YAAY,WAAW,IAAI,CAAC,WAAW,GAAG;aACxD;SACF,EACD;YACE,aAAa,EAAE,YAAY;YAC3B,QAAQ,EAAE,aAAa;YACvB,MAAM,EAAE,MAAM,CAAC,QAAQ;YACvB,IAAI,EAAE,cAAc;SACrB,CACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC;QAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC;QACrC,IAAI,uBAAuB,GAAG,QAAQ,CAAC;QACvC,IAAI,OAAO,SAAS,EAAE,WAAW,KAAK,QAAQ,EAAE,CAAC;YAC/C,uBAAuB,GAAG,SAAS,CAAC,WAAW,CAAC;QAClD,CAAC;aAAM,IAAI,SAAS,EAAE,WAAW,EAAE,CAAC;YAClC,uBAAuB,GAAG,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;QACjE,CAAC;QAED,IAAI,uBAAuB,IAAI,gBAAgB,EAAE,CAAC;YAChD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,OAAO;QACT,CAAC;QAED,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC5B,UAAU,CAAC,OAAO,EAAE,gBAAgB,GAAG,uBAAuB,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACrC,wEAAwE;YACxE,uCAAuC;YACvC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;;;;;;;OAQG;IACK,kBAAkB,CAAC,KAAmB;QAC5C,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACtC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IACE,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,gBAAgB,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS,EACnD,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YACnE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,KAAK,CAAC,CAAC;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,eAAe,CAAC;IAChD,CAAC;IAED;;;;OAIG;IACK,QAAQ,CAAC,EAAC,CAAC,EAAE,CAAC,EAAe;QACnC,MAAM,EAAC,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAChE,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,MAAM,CAAC;IAC5D,CAAC;IAEO,OAAO,CAAC,EAAC,WAAW,EAAe;QACzC,OAAO,WAAW,KAAK,OAAO,CAAC;IACjC,CAAC;IAED,eAAe;IACf,KAAK,CAAC,WAAW,CAAC,KAAY;QAC5B,IAAI,aAAa,EAAE,OAAO,EAAE,CAAC;YAC3B,wDAAwD;YACxD,OAAO;QACT,CAAC;QAED,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,OAAO;gBACV,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,MAAM;YACR,KAAK,aAAa;gBAChB,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,MAAM;YACR,KAAK,eAAe;gBAClB,IAAI,CAAC,mBAAmB,CAAC,KAAqB,CAAC,CAAC;gBAChD,MAAM;YACR,KAAK,aAAa;gBAChB,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAqB,CAAC,CAAC;gBACpD,MAAM;YACR,KAAK,cAAc;gBACjB,IAAI,CAAC,kBAAkB,CAAC,KAAqB,CAAC,CAAC;gBAC/C,MAAM;YACR,KAAK,cAAc;gBACjB,IAAI,CAAC,kBAAkB,CAAC,KAAqB,CAAC,CAAC;gBAC/C,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,CAAC,eAAe,CAAC,KAAqB,CAAC,CAAC;gBAC5C,MAAM;YACR;gBACE,MAAM;QACV,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,IAAwB,EAAE,IAAwB;QACxE,IAAI,QAAQ;YAAE,OAAO;QAErB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,EAAE,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACvC,IAAI,EAAE,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;CACF;AAjY2C;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;wCAAkB;AAiB1C;IAAhB,KAAK,EAAE;uCAAyB;AAChB;IAAhB,KAAK,EAAE;uCAAyB;AAEG;IAAnC,KAAK,CAAC,UAAU,CAAC;sCAA8C", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, isServer, LitElement, PropertyValues} from 'lit';\nimport {property, query, state} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {\n  Attachable,\n  AttachableController,\n} from '../../internal/controller/attachable-controller.js';\nimport {EASING} from '../../internal/motion/animation.js';\n\nconst PRESS_GROW_MS = 450;\nconst MINIMUM_PRESS_MS = 225;\nconst INITIAL_ORIGIN_SCALE = 0.2;\nconst PADDING = 10;\nconst SOFT_EDGE_MINIMUM_SIZE = 75;\nconst SOFT_EDGE_CONTAINER_RATIO = 0.35;\nconst PRESS_PSEUDO = '::after';\nconst ANIMATION_FILL = 'forwards';\n\n/**\n * Interaction states for the ripple.\n *\n * On Touch:\n *  - `INACTIVE -> TOUCH_DELAY -> WAITING_FOR_CLICK -> INACTIVE`\n *  - `INACTIVE -> TOUCH_DELAY -> HOLDING -> WAITING_FOR_CLICK -> INACTIVE`\n *\n * On Mouse or Pen:\n *   - `INACTIVE -> WAITING_FOR_CLICK -> INACTIVE`\n */\nenum State {\n  /**\n   * Initial state of the control, no touch in progress.\n   *\n   * Transitions:\n   *   - on touch down: transition to `TOUCH_DELAY`.\n   *   - on mouse down: transition to `WAITING_FOR_CLICK`.\n   */\n  INACTIVE,\n  /**\n   * Touch down has been received, waiting to determine if it's a swipe or\n   * scroll.\n   *\n   * Transitions:\n   *   - on touch up: begin press; transition to `WAITING_FOR_CLICK`.\n   *   - on cancel: transition to `INACTIVE`.\n   *   - after `TOUCH_DELAY_MS`: begin press; transition to `HOLDING`.\n   */\n  TOUCH_DELAY,\n  /**\n   * A touch has been deemed to be a press\n   *\n   * Transitions:\n   *  - on up: transition to `WAITING_FOR_CLICK`.\n   */\n  HOLDING,\n  /**\n   * The user touch has finished, transition into rest state.\n   *\n   * Transitions:\n   *   - on click end press; transition to `INACTIVE`.\n   */\n  WAITING_FOR_CLICK,\n}\n\n/**\n * Events that the ripple listens to.\n */\nconst EVENTS = [\n  'click',\n  'contextmenu',\n  'pointercancel',\n  'pointerdown',\n  'pointerenter',\n  'pointerleave',\n  'pointerup',\n];\n\n/**\n * Delay reacting to touch so that we do not show the ripple for a swipe or\n * scroll interaction.\n */\nconst TOUCH_DELAY_MS = 150;\n\n/**\n * Used to detect if HCM is active. Events do not process during HCM when the\n * ripple is not displayed.\n */\nconst FORCED_COLORS = isServer\n  ? null\n  : window.matchMedia('(forced-colors: active)');\n\n/**\n * A ripple component.\n */\nexport class Ripple extends LitElement implements Attachable {\n  /**\n   * Disables the ripple.\n   */\n  @property({type: Boolean, reflect: true}) disabled = false;\n\n  get htmlFor() {\n    return this.attachableController.htmlFor;\n  }\n\n  set htmlFor(htmlFor: string | null) {\n    this.attachableController.htmlFor = htmlFor;\n  }\n\n  get control() {\n    return this.attachableController.control;\n  }\n  set control(control: HTMLElement | null) {\n    this.attachableController.control = control;\n  }\n\n  @state() private hovered = false;\n  @state() private pressed = false;\n\n  @query('.surface') private readonly mdRoot!: HTMLElement | null;\n  private rippleSize = '';\n  private rippleScale = '';\n  private initialSize = 0;\n  private growAnimation?: Animation;\n  private state = State.INACTIVE;\n  private rippleStartEvent?: PointerEvent;\n  private checkBoundsAfterContextMenu = false;\n  private readonly attachableController = new AttachableController(\n    this,\n    this.onControlChange.bind(this),\n  );\n\n  attach(control: HTMLElement) {\n    this.attachableController.attach(control);\n  }\n\n  detach() {\n    this.attachableController.detach();\n  }\n\n  override connectedCallback() {\n    super.connectedCallback();\n    // Needed for VoiceOver, which will create a \"group\" if the element is a\n    // sibling to other content.\n    this.setAttribute('aria-hidden', 'true');\n  }\n\n  protected override render() {\n    const classes = {\n      'hovered': this.hovered,\n      'pressed': this.pressed,\n    };\n\n    return html`<div class=\"surface ${classMap(classes)}\"></div>`;\n  }\n\n  protected override update(changedProps: PropertyValues<Ripple>) {\n    if (changedProps.has('disabled') && this.disabled) {\n      this.hovered = false;\n      this.pressed = false;\n    }\n    super.update(changedProps);\n  }\n\n  /**\n   * TODO(b/269799771): make private\n   * @private only public for slider\n   */\n  handlePointerenter(event: PointerEvent) {\n    if (!this.shouldReactToEvent(event)) {\n      return;\n    }\n\n    this.hovered = true;\n  }\n\n  /**\n   * TODO(b/269799771): make private\n   * @private only public for slider\n   */\n  handlePointerleave(event: PointerEvent) {\n    if (!this.shouldReactToEvent(event)) {\n      return;\n    }\n\n    this.hovered = false;\n\n    // release a held mouse or pen press that moves outside the element\n    if (this.state !== State.INACTIVE) {\n      this.endPressAnimation();\n    }\n  }\n\n  private handlePointerup(event: PointerEvent) {\n    if (!this.shouldReactToEvent(event)) {\n      return;\n    }\n\n    if (this.state === State.HOLDING) {\n      this.state = State.WAITING_FOR_CLICK;\n      return;\n    }\n\n    if (this.state === State.TOUCH_DELAY) {\n      this.state = State.WAITING_FOR_CLICK;\n      this.startPressAnimation(this.rippleStartEvent);\n      return;\n    }\n  }\n\n  private async handlePointerdown(event: PointerEvent) {\n    if (!this.shouldReactToEvent(event)) {\n      return;\n    }\n\n    this.rippleStartEvent = event;\n    if (!this.isTouch(event)) {\n      this.state = State.WAITING_FOR_CLICK;\n      this.startPressAnimation(event);\n      return;\n    }\n\n    // after a longpress contextmenu event, an extra `pointerdown` can be\n    // dispatched to the pressed element. Check that the down is within\n    // bounds of the element in this case.\n    if (this.checkBoundsAfterContextMenu && !this.inBounds(event)) {\n      return;\n    }\n\n    this.checkBoundsAfterContextMenu = false;\n\n    // Wait for a hold after touch delay\n    this.state = State.TOUCH_DELAY;\n    await new Promise((resolve) => {\n      setTimeout(resolve, TOUCH_DELAY_MS);\n    });\n\n    if (this.state !== State.TOUCH_DELAY) {\n      return;\n    }\n\n    this.state = State.HOLDING;\n    this.startPressAnimation(event);\n  }\n\n  private handleClick() {\n    // Click is a MouseEvent in Firefox and Safari, so we cannot use\n    // `shouldReactToEvent`\n    if (this.disabled) {\n      return;\n    }\n\n    if (this.state === State.WAITING_FOR_CLICK) {\n      this.endPressAnimation();\n      return;\n    }\n\n    if (this.state === State.INACTIVE) {\n      // keyboard synthesized click event\n      this.startPressAnimation();\n      this.endPressAnimation();\n    }\n  }\n\n  private handlePointercancel(event: PointerEvent) {\n    if (!this.shouldReactToEvent(event)) {\n      return;\n    }\n\n    this.endPressAnimation();\n  }\n\n  private handleContextmenu() {\n    if (this.disabled) {\n      return;\n    }\n\n    this.checkBoundsAfterContextMenu = true;\n    this.endPressAnimation();\n  }\n\n  private determineRippleSize() {\n    const {height, width} = this.getBoundingClientRect();\n    const maxDim = Math.max(height, width);\n    const softEdgeSize = Math.max(\n      SOFT_EDGE_CONTAINER_RATIO * maxDim,\n      SOFT_EDGE_MINIMUM_SIZE,\n    );\n\n    const initialSize = Math.floor(maxDim * INITIAL_ORIGIN_SCALE);\n    const hypotenuse = Math.sqrt(width ** 2 + height ** 2);\n    const maxRadius = hypotenuse + PADDING;\n\n    this.initialSize = initialSize;\n    this.rippleScale = `${(maxRadius + softEdgeSize) / initialSize}`;\n    this.rippleSize = `${initialSize}px`;\n  }\n\n  private getNormalizedPointerEventCoords(pointerEvent: PointerEvent): {\n    x: number;\n    y: number;\n  } {\n    const {scrollX, scrollY} = window;\n    const {left, top} = this.getBoundingClientRect();\n    const documentX = scrollX + left;\n    const documentY = scrollY + top;\n    const {pageX, pageY} = pointerEvent;\n    return {x: pageX - documentX, y: pageY - documentY};\n  }\n\n  private getTranslationCoordinates(positionEvent?: Event) {\n    const {height, width} = this.getBoundingClientRect();\n    // end in the center\n    const endPoint = {\n      x: (width - this.initialSize) / 2,\n      y: (height - this.initialSize) / 2,\n    };\n\n    let startPoint;\n    if (positionEvent instanceof PointerEvent) {\n      startPoint = this.getNormalizedPointerEventCoords(positionEvent);\n    } else {\n      startPoint = {\n        x: width / 2,\n        y: height / 2,\n      };\n    }\n\n    // center around start point\n    startPoint = {\n      x: startPoint.x - this.initialSize / 2,\n      y: startPoint.y - this.initialSize / 2,\n    };\n\n    return {startPoint, endPoint};\n  }\n\n  private startPressAnimation(positionEvent?: Event) {\n    if (!this.mdRoot) {\n      return;\n    }\n\n    this.pressed = true;\n    this.growAnimation?.cancel();\n    this.determineRippleSize();\n    const {startPoint, endPoint} =\n      this.getTranslationCoordinates(positionEvent);\n    const translateStart = `${startPoint.x}px, ${startPoint.y}px`;\n    const translateEnd = `${endPoint.x}px, ${endPoint.y}px`;\n\n    this.growAnimation = this.mdRoot.animate(\n      {\n        top: [0, 0],\n        left: [0, 0],\n        height: [this.rippleSize, this.rippleSize],\n        width: [this.rippleSize, this.rippleSize],\n        transform: [\n          `translate(${translateStart}) scale(1)`,\n          `translate(${translateEnd}) scale(${this.rippleScale})`,\n        ],\n      },\n      {\n        pseudoElement: PRESS_PSEUDO,\n        duration: PRESS_GROW_MS,\n        easing: EASING.STANDARD,\n        fill: ANIMATION_FILL,\n      },\n    );\n  }\n\n  private async endPressAnimation() {\n    this.rippleStartEvent = undefined;\n    this.state = State.INACTIVE;\n    const animation = this.growAnimation;\n    let pressAnimationPlayState = Infinity;\n    if (typeof animation?.currentTime === 'number') {\n      pressAnimationPlayState = animation.currentTime;\n    } else if (animation?.currentTime) {\n      pressAnimationPlayState = animation.currentTime.to('ms').value;\n    }\n\n    if (pressAnimationPlayState >= MINIMUM_PRESS_MS) {\n      this.pressed = false;\n      return;\n    }\n\n    await new Promise((resolve) => {\n      setTimeout(resolve, MINIMUM_PRESS_MS - pressAnimationPlayState);\n    });\n\n    if (this.growAnimation !== animation) {\n      // A new press animation was started. The old animation was canceled and\n      // should not finish the pressed state.\n      return;\n    }\n\n    this.pressed = false;\n  }\n\n  /**\n   * Returns `true` if\n   *  - the ripple element is enabled\n   *  - the pointer is primary for the input type\n   *  - the pointer is the pointer that started the interaction, or will start\n   * the interaction\n   *  - the pointer is a touch, or the pointer state has the primary button\n   * held, or the pointer is hovering\n   */\n  private shouldReactToEvent(event: PointerEvent) {\n    if (this.disabled || !event.isPrimary) {\n      return false;\n    }\n\n    if (\n      this.rippleStartEvent &&\n      this.rippleStartEvent.pointerId !== event.pointerId\n    ) {\n      return false;\n    }\n\n    if (event.type === 'pointerenter' || event.type === 'pointerleave') {\n      return !this.isTouch(event);\n    }\n\n    const isPrimaryButton = event.buttons === 1;\n    return this.isTouch(event) || isPrimaryButton;\n  }\n\n  /**\n   * Check if the event is within the bounds of the element.\n   *\n   * This is only needed for the \"stuck\" contextmenu longpress on Chrome.\n   */\n  private inBounds({x, y}: PointerEvent) {\n    const {top, left, bottom, right} = this.getBoundingClientRect();\n    return x >= left && x <= right && y >= top && y <= bottom;\n  }\n\n  private isTouch({pointerType}: PointerEvent) {\n    return pointerType === 'touch';\n  }\n\n  /** @private */\n  async handleEvent(event: Event) {\n    if (FORCED_COLORS?.matches) {\n      // Skip event logic since the ripple is `display: none`.\n      return;\n    }\n\n    switch (event.type) {\n      case 'click':\n        this.handleClick();\n        break;\n      case 'contextmenu':\n        this.handleContextmenu();\n        break;\n      case 'pointercancel':\n        this.handlePointercancel(event as PointerEvent);\n        break;\n      case 'pointerdown':\n        await this.handlePointerdown(event as PointerEvent);\n        break;\n      case 'pointerenter':\n        this.handlePointerenter(event as PointerEvent);\n        break;\n      case 'pointerleave':\n        this.handlePointerleave(event as PointerEvent);\n        break;\n      case 'pointerup':\n        this.handlePointerup(event as PointerEvent);\n        break;\n      default:\n        break;\n    }\n  }\n\n  private onControlChange(prev: HTMLElement | null, next: HTMLElement | null) {\n    if (isServer) return;\n\n    for (const event of EVENTS) {\n      prev?.removeEventListener(event, this);\n      next?.addEventListener(event, this);\n    }\n  }\n}\n"]}