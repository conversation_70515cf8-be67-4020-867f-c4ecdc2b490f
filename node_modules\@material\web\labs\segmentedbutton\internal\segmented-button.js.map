{"version": 3, "file": "segmented-button.js", "sourceRoot": "", "sources": ["segmented-button.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,iCAAiC,CAAC;AACzC,OAAO,2BAA2B,CAAC;AAEnC,OAAO,EAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAiC,MAAM,KAAK,CAAC;AAC9E,OAAO,EAAC,QAAQ,EAAE,qBAAqB,EAAE,KAAK,EAAC,MAAM,mBAAmB,CAAC;AACzE,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAGrD,OAAO,EAAC,kBAAkB,EAAC,MAAM,oCAAoC,CAAC;AAEtE,wCAAwC;AACxC,MAAM,wBAAwB,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAEhE;;;;;;;;GAQG;AACH,MAAM,OAAO,eAAgB,SAAQ,wBAAwB;IAA7D;;QAC6B,aAAQ,GAAG,KAAK,CAAC;QACjB,aAAQ,GAAG,KAAK,CAAC;QAChC,UAAK,GAAG,EAAE,CAAC;QAC+B,gBAAW,GAAG,KAAK,CAAC;QACxB,YAAO,GAAG,KAAK,CAAC;QAEjD,cAAS,GAAG,EAAE,CAAC;IAkIlC,CAAC;IA9HoB,MAAM,CAAC,KAAsC;QAC9D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAChD,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACpB,0EAA0E;QAC1E,uCAAuC;QACvC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;IAC7C,CAAC;IAEO,kBAAkB,CACxB,YAA6C;QAE7C,MAAM,YAAY,GAAG,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAClD,+BAA+B;QAC/B,IAAI,YAAY,KAAK,SAAS;YAAE,OAAO,EAAE,CAAC;QAE1C,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;QACnC,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;QAC3C,IAAI,CAAC,YAAY,IAAI,YAAY,IAAI,gBAAgB,EAAE,CAAC;YACtD,OAAO,WAAW,CAAC;QACrB,CAAC;QACD,IAAI,YAAY,IAAI,CAAC,YAAY,IAAI,gBAAgB,EAAE,CAAC;YACtD,OAAO,aAAa,CAAC;QACvB,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,WAAW;QACjB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,8BAA8B,EAAE;YACtD,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAEkB,MAAM;QACvB,iCAAiC;QACjC,MAAM,EAAC,SAAS,EAAC,GAAG,IAAuB,CAAC;QAC5C,OAAO,IAAI,CAAA;;oBAEK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG;qBACzB,SAAS,IAAI,OAAO;uBAClB,IAAI,CAAC,QAAQ;oBAChB,IAAI,CAAC,QAAQ;kBACf,IAAI,CAAC,WAAW;sCACI,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;;;;;uBAKhD,IAAI,CAAC,QAAQ;;UAE1B,IAAI,CAAC,aAAa,EAAE,IAAI,IAAI,CAAC,aAAa,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE;UAClE,IAAI,CAAC,iBAAiB,EAAE;;KAE7B,CAAC;IACJ,CAAC;IAES,gBAAgB;QACxB,OAAO;YACL,gCAAgC,EAAE,IAAI,CAAC,QAAQ;YAC/C,kCAAkC,EAAE,CAAC,IAAI,CAAC,QAAQ;YAClD,kCAAkC,EAAE,IAAI,CAAC,KAAK,KAAK,EAAE;YACrD,qCAAqC,EAAE,IAAI,CAAC,KAAK,KAAK,EAAE;YACxD,iCAAiC,EAAE,IAAI,CAAC,OAAO;YAC/C,sCAAsC,EAAE,CAAC,IAAI,CAAC,WAAW;YACzD,yCAAyC,EAAE,IAAI,CAAC,WAAW;YAC3D,iCAAiC,EAAE,IAAI,CAAC,SAAS,KAAK,WAAW;YACjE,mCAAmC,EAAE,IAAI,CAAC,SAAS,KAAK,aAAa;SACtE,CAAC;IACJ,CAAC;IAES,aAAa;QACrB,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,aAAa;QACnB,OAAO,IAAI,CAAC,KAAK,KAAK,EAAE;YACtB,CAAC,CAAC,IAAI,CAAC,yBAAyB,EAAE;YAClC,CAAC,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IACpC,CAAC;IAEO,yBAAyB;QAC/B,OAAO,IAAI,CAAA;;;;;;;;;;;;;;KAcV,CAAC;IACJ,CAAC;IAEO,sBAAsB;QAC5B,OAAO,IAAI,CAAA;;;;;;;;;;;;;;KAcV,CAAC;IACJ,CAAC;IAEO,WAAW;QACjB,OAAO,IAAI,CAAA;uDACwC,IAAI,CAAC,KAAK;KAC5D,CAAC;IACJ,CAAC;IAEO,iBAAiB;QACvB,OAAO,IAAI,CAAA,mDAAmD,CAAC;IACjE,CAAC;CACF;AAxI4B;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;iDAAkB;AACjB;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;iDAAkB;AAChC;IAAX,QAAQ,EAAE;8CAAY;AAC+B;IAArD,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,cAAc,EAAC,CAAC;oDAAqB;AACxB;IAAjD,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAC,CAAC;gDAAiB;AAEjD;IAAhB,KAAK,EAAE;kDAAwB;AAEf;IADhB,qBAAqB,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;oDACR", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../../focus/md-focus-ring.js';\nimport '../../../ripple/ripple.js';\n\nimport {html, LitElement, nothing, PropertyValues, TemplateResult} from 'lit';\nimport {property, queryAssignedElements, state} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\n\n// Separate variable needed for closure.\nconst segmentedButtonBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * SegmentedButton is a web component implementation of the Material Design\n * segmented button component. It is intended **only** for use as a child of a\n * `SementedButtonSet` component. It is **not** intended for use in any other\n * context.\n *\n * @fires segmented-button-interaction {Event} Dispatched whenever a button is\n * clicked. --bubbles --composed\n */\nexport class SegmentedButton extends segmentedButtonBaseClass {\n  @property({type: Boolean}) disabled = false;\n  @property({type: Boolean}) selected = false;\n  @property() label = '';\n  @property({type: Boolean, attribute: 'no-checkmark'}) noCheckmark = false;\n  @property({type: Boolean, attribute: 'has-icon'}) hasIcon = false;\n\n  @state() private animState = '';\n  @queryAssignedElements({slot: 'icon', flatten: true})\n  private readonly iconElement!: HTMLElement[];\n\n  protected override update(props: PropertyValues<SegmentedButton>) {\n    this.animState = this.nextAnimationState(props);\n    super.update(props);\n    // NOTE: This needs to be set *after* calling super.update() to ensure the\n    // appropriate CSS classes are applied.\n    this.hasIcon = this.iconElement.length > 0;\n  }\n\n  private nextAnimationState(\n    changedProps: PropertyValues<SegmentedButton>,\n  ): string {\n    const prevSelected = changedProps.get('selected');\n    // Early exit for first update.\n    if (prevSelected === undefined) return '';\n\n    const nextSelected = this.selected;\n    const nextHasCheckmark = !this.noCheckmark;\n    if (!prevSelected && nextSelected && nextHasCheckmark) {\n      return 'selecting';\n    }\n    if (prevSelected && !nextSelected && nextHasCheckmark) {\n      return 'deselecting';\n    }\n    return '';\n  }\n\n  private handleClick() {\n    const event = new Event('segmented-button-interaction', {\n      bubbles: true,\n      composed: true,\n    });\n    this.dispatchEvent(event);\n  }\n\n  protected override render() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`\n      <button\n        tabindex=\"${this.disabled ? '-1' : '0'}\"\n        aria-label=${ariaLabel || nothing}\n        aria-pressed=${this.selected}\n        ?disabled=${this.disabled}\n        @click=\"${this.handleClick}\"\n        class=\"md3-segmented-button ${classMap(this.getRenderClasses())}\">\n        <md-focus-ring\n          class=\"md3-segmented-button__focus-ring\"\n          part=\"focus-ring\"></md-focus-ring>\n        <md-ripple\n          ?disabled=\"${this.disabled}\"\n          class=\"md3-segmented-button__ripple\"></md-ripple>\n        ${this.renderOutline()} ${this.renderLeading()} ${this.renderLabel()}\n        ${this.renderTouchTarget()}\n      </button>\n    `;\n  }\n\n  protected getRenderClasses() {\n    return {\n      'md3-segmented-button--selected': this.selected,\n      'md3-segmented-button--unselected': !this.selected,\n      'md3-segmented-button--with-label': this.label !== '',\n      'md3-segmented-button--without-label': this.label === '',\n      'md3-segmented-button--with-icon': this.hasIcon,\n      'md3-segmented-button--with-checkmark': !this.noCheckmark,\n      'md3-segmented-button--without-checkmark': this.noCheckmark,\n      'md3-segmented-button--selecting': this.animState === 'selecting',\n      'md3-segmented-button--deselecting': this.animState === 'deselecting',\n    };\n  }\n\n  protected renderOutline(): TemplateResult | typeof nothing {\n    return nothing;\n  }\n\n  private renderLeading() {\n    return this.label === ''\n      ? this.renderLeadingWithoutLabel()\n      : this.renderLeadingWithLabel();\n  }\n\n  private renderLeadingWithoutLabel() {\n    return html`\n      <span class=\"md3-segmented-button__leading\" aria-hidden=\"true\">\n        <span class=\"md3-segmented-button__graphic\">\n          <svg class=\"md3-segmented-button__checkmark\" viewBox=\"0 0 24 24\">\n            <path\n              class=\"md3-segmented-button__checkmark-path\"\n              fill=\"none\"\n              d=\"M1.73,12.91 8.1,19.28 22.79,4.59\"></path>\n          </svg>\n        </span>\n        <span class=\"md3-segmented-button__icon\" aria-hidden=\"true\">\n          <slot name=\"icon\"></slot>\n        </span>\n      </span>\n    `;\n  }\n\n  private renderLeadingWithLabel() {\n    return html`\n      <span class=\"md3-segmented-button__leading\" aria-hidden=\"true\">\n        <span class=\"md3-segmented-button__graphic\">\n          <svg class=\"md3-segmented-button__checkmark\" viewBox=\"0 0 24 24\">\n            <path\n              class=\"md3-segmented-button__checkmark-path\"\n              fill=\"none\"\n              d=\"M1.73,12.91 8.1,19.28 22.79,4.59\"></path>\n          </svg>\n          <span class=\"md3-segmented-button__icon\" aria-hidden=\"true\">\n            <slot name=\"icon\"></slot>\n          </span>\n        </span>\n      </span>\n    `;\n  }\n\n  private renderLabel() {\n    return html`\n      <span class=\"md3-segmented-button__label-text\">${this.label}</span>\n    `;\n  }\n\n  private renderTouchTarget() {\n    return html`<span class=\"md3-segmented-button__touch\"></span>`;\n  }\n}\n"]}