//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
@use 'sass:string';
// go/keep-sorted end
// go/keep-sorted start
@use './internal/shape';
@use './internal/validate';
@use './md-sys-color';
@use './md-sys-shape';
@use './md-sys-state';
@use './v0_192/md-comp-filled-tonal-icon-button';
// go/keep-sorted end

$supported-tokens: (
  // go/keep-sorted start
  'container-color',
  'container-height',
  'container-shape',
  'container-shape-end-end',
  'container-shape-end-start',
  'container-shape-start-end',
  'container-shape-start-start',
  'container-width',
  'disabled-container-color',
  'disabled-container-opacity',
  'disabled-icon-color',
  'disabled-icon-opacity',
  'focus-icon-color',
  'hover-icon-color',
  'hover-state-layer-color',
  'hover-state-layer-opacity',
  'icon-color',
  'icon-size',
  'pressed-icon-color',
  'pressed-state-layer-color',
  'pressed-state-layer-opacity',
  'selected-container-color',
  'toggle-focus-icon-color',
  'toggle-hover-icon-color',
  'toggle-hover-state-layer-color',
  'toggle-icon-color',
  'toggle-pressed-icon-color',
  'toggle-pressed-state-layer-color',
  'toggle-selected-focus-icon-color',
  'toggle-selected-hover-icon-color',
  'toggle-selected-hover-state-layer-color',
  'toggle-selected-icon-color',
  'toggle-selected-pressed-icon-color',
  'toggle-selected-pressed-state-layer-color',
  // Note: filled icon buttons have three container colors,
  // "container-color" for regular, then selected/unselected for toggle.
  'unselected-container-color',
  // go/keep-sorted end
);

$unsupported-tokens: (
  // go/keep-sorted start
  'focus-state-layer-color',
  'focus-state-layer-opacity',
  'toggle-focus-state-layer-color',
  'toggle-selected-focus-state-layer-color',
  // go/keep-sorted end
);

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
);

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $tokens: md-comp-filled-tonal-icon-button.values(
    $deps,
    $exclude-hardcoded-values
  );

  $new-tokens: shape.get-new-logical-shape-tokens($tokens, 'container-shape');
  $tokens: validate.values(
    $tokens,
    $supported-tokens: $supported-tokens,
    $unsupported-tokens: $unsupported-tokens,
    $new-tokens: $new-tokens,
    $renamed-tokens: (
      // Remove default "unselected" prefix (b/292244480)
      'toggle-unselected-focus-icon-color': 'toggle-focus-icon-color',
      'toggle-unselected-hover-icon-color': 'toggle-hover-icon-color',
      'toggle-unselected-hover-state-layer-color':
        'toggle-hover-state-layer-color',
      'toggle-unselected-icon-color': 'toggle-icon-color',
      'toggle-unselected-pressed-icon-color': 'toggle-pressed-icon-color',
      'toggle-unselected-pressed-state-layer-color':
        'toggle-pressed-state-layer-color',
      'toggle-unselected-focus-state-layer-color':
        'toggle-focus-state-layer-color'
    )
  );

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      @if string.index($token, 'container-shape-') == 1 {
        // Add fallback to shorthand for logical shape properties.
        $value: var(--md-filled-tonal-icon-button-container-shape, #{$value});
      }

      $tokens: map.set(
        $tokens,
        $token,
        var(--md-filled-tonal-icon-button-#{$token}, #{$value})
      );
    }
  }

  @return $tokens;
}
