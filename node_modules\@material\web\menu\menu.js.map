{"version": 3, "file": "menu.js", "sourceRoot": "", "sources": ["menu.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,IAAI,EAAC,MAAM,oBAAoB,CAAC;AACxC,OAAO,EAAC,MAAM,EAAC,MAAM,2BAA2B,CAAC;AAIjD,OAAO,EACL,WAAW,EACX,UAAU,GAGX,MAAM,kCAAkC,CAAC;AAC1C,OAAO,EAAC,MAAM,EAAC,MAAM,oBAAoB,CAAC;AAQ1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgDG;AAEI,IAAM,MAAM,GAAZ,MAAM,MAAO,SAAQ,IAAI;;AACd,aAAM,GAAwB,CAAC,MAAM,CAAC,AAAhC,CAAiC;AAD5C,MAAM;IADlB,aAAa,CAAC,SAAS,CAAC;GACZ,MAAM,CAElB", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Menu} from './internal/menu.js';\nimport {styles} from './internal/menu-styles.js';\n\nexport {type ListItem} from '../list/internal/list-navigation-helpers.js';\nexport {type MenuItem} from './internal/controllers/menuItemController.js';\nexport {\n  CloseReason,\n  FocusState,\n  type CloseMenuEvent,\n  type Menu,\n} from './internal/controllers/shared.js';\nexport {Corner} from './internal/menu.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-menu': MdMenu;\n  }\n}\n\n/**\n * @summary Menus display a list of choices on a temporary surface.\n *\n * @description\n * Menus appear when users interact with a button, action, or other control.\n *\n * They can be opened from a variety of elements, most commonly icon buttons,\n * buttons, and text fields.\n *\n * md-menu listens for the `close-menu` and `deselect-items` events.\n *\n * - `close-menu` closes the menu when dispatched from a child element.\n * - `deselect-items` deselects all of its immediate menu-item children.\n *\n * @example\n * ```html\n * <div style=\"position:relative;\">\n *   <button\n *       id=\"anchor\"\n *       @click=${() => this.menuRef.value.show()}>\n *     Click to open menu\n *   </button>\n *   <!--\n *     `has-overflow` is required when using a submenu which overflows the\n *     menu's contents.\n *\n *     Additionally, `anchor` ingests an idref which do not pass through shadow\n *     roots. You can also set `.anchorElement` to an element reference if\n *     necessary.\n *   -->\n *   <md-menu anchor=\"anchor\" has-overflow ${ref(menuRef)}>\n *     <md-menu-item headline=\"This is a headline\"></md-menu-item>\n *     <md-sub-menu>\n *       <md-menu-item\n *           slot=\"item\"\n *           headline=\"this is a submenu item\">\n *       </md-menu-item>\n *       <md-menu slot=\"menu\">\n *         <md-menu-item headline=\"This is an item inside a submenu\">\n *         </md-menu-item>\n *       </md-menu>\n *     </md-sub-menu>\n *   </md-menu>\n * </div>\n * ```\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-menu')\nexport class MdMenu extends Menu {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"]}