//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:map';
@use 'sass:string';
// go/keep-sorted end
// go/keep-sorted start
@use './internal/shape';
@use './internal/validate';
@use './md-sys-color';
@use './md-sys-elevation';
@use './md-sys-shape';
@use './md-sys-state';
@use './md-sys-typescale';
@use './v0_192/md-comp-input-chip';
// go/keep-sorted end

$supported-tokens: (
  // go/keep-sorted start
  'avatar-shape',
  'avatar-size',
  'container-height',
  'container-shape',
  'container-shape-end-end',
  'container-shape-end-start',
  'container-shape-start-end',
  'container-shape-start-start',
  'disabled-avatar-opacity',
  'disabled-label-text-color',
  'disabled-label-text-opacity',
  'disabled-leading-icon-color',
  'disabled-leading-icon-opacity',
  'disabled-outline-color',
  'disabled-outline-opacity',
  'disabled-selected-container-color',
  'disabled-selected-container-opacity',
  'disabled-trailing-icon-color',
  'disabled-trailing-icon-opacity',
  'focus-label-text-color',
  'focus-leading-icon-color',
  'focus-outline-color',
  'focus-trailing-icon-color',
  'hover-label-text-color',
  'hover-leading-icon-color',
  'hover-state-layer-color',
  'hover-state-layer-opacity',
  'hover-trailing-icon-color',
  'icon-label-space',
  'icon-size',
  'label-text-color',
  'label-text-font',
  'label-text-line-height',
  'label-text-size',
  'label-text-weight',
  'leading-icon-color',
  'leading-space',
  'outline-color',
  'outline-width',
  'pressed-label-text-color',
  'pressed-leading-icon-color',
  'pressed-state-layer-color',
  'pressed-state-layer-opacity',
  'pressed-trailing-icon-color',
  'selected-container-color',
  'selected-focus-label-text-color',
  'selected-focus-leading-icon-color',
  'selected-focus-trailing-icon-color',
  'selected-hover-label-text-color',
  'selected-hover-leading-icon-color',
  'selected-hover-state-layer-color',
  'selected-hover-state-layer-opacity',
  'selected-hover-trailing-icon-color',
  'selected-label-text-color',
  'selected-leading-icon-color',
  'selected-outline-width',
  'selected-pressed-label-text-color',
  'selected-pressed-leading-icon-color',
  'selected-pressed-state-layer-color',
  'selected-pressed-state-layer-opacity',
  'selected-pressed-trailing-icon-color',
  'selected-trailing-icon-color',
  'trailing-icon-color',
  'trailing-space',
  'with-leading-icon-leading-space',
  'with-trailing-icon-trailing-space',
  // go/keep-sorted end
);

$unsupported-tokens: (
  // go/keep-sorted start
  'container-elevation',
  'dragged-container-elevation',
  'dragged-label-text-color',
  'dragged-leading-icon-color',
  'dragged-state-layer-color',
  'dragged-state-layer-opacity',
  'dragged-trailing-icon-color',
  'focus-state-layer-color',
  'focus-state-layer-opacity',
  'label-text-tracking',
  'label-text-type',
  'selected-dragged-label-text-color',
  'selected-dragged-leading-icon-color',
  'selected-dragged-state-layer-color',
  'selected-dragged-state-layer-opacity',
  'selected-dragged-trailing-icon-color',
  'selected-focus-state-layer-color',
  'selected-focus-state-layer-opacity',
  'trailing-icon-size',
  // go/keep-sorted end
);

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values(
  $deps: $_default,
  $exclude-hardcoded-values: false,
  $exclude-custom-properties: false
) {
  $tokens: md-comp-input-chip.values($deps, $exclude-hardcoded-values);
  $new-tokens: map.merge(
    shape.get-new-logical-shape-tokens($tokens, 'container-shape'),
    (
      'leading-space': if($exclude-hardcoded-values, null, 16px),
      'trailing-space': if($exclude-hardcoded-values, null, 16px),
      'icon-label-space': if($exclude-hardcoded-values, null, 8px),
      'with-leading-icon-leading-space':
        if($exclude-hardcoded-values, null, 8px),
      'with-trailing-icon-trailing-space':
        if($exclude-hardcoded-values, null, 8px),
    )
  );

  $tokens: validate.values(
    $tokens,
    $supported-tokens: $supported-tokens,
    $unsupported-tokens: $unsupported-tokens,
    $new-tokens: $new-tokens,
    $renamed-tokens: (
      // Remove "unselected-*" prefix (b/275577569)
      'disabled-unselected-outline-color': 'disabled-outline-color',
      'disabled-unselected-outline-opacity': 'disabled-outline-opacity',
      'unselected-dragged-label-text-color': 'dragged-label-text-color',
      'unselected-dragged-state-layer-color': 'dragged-state-layer-color',
      'unselected-dragged-state-layer-opacity': 'dragged-state-layer-opacity',
      'unselected-focus-label-text-color': 'focus-label-text-color',
      'unselected-focus-outline-color': 'focus-outline-color',
      'unselected-focus-state-layer-color': 'focus-state-layer-color',
      'unselected-focus-state-layer-opacity': 'focus-state-layer-opacity',
      'unselected-hover-label-text-color': 'hover-label-text-color',
      'unselected-hover-state-layer-color': 'hover-state-layer-color',
      'unselected-hover-state-layer-opacity': 'hover-state-layer-opacity',
      'unselected-label-text-color': 'label-text-color',
      'unselected-outline-color': 'outline-color',
      'unselected-outline-width': 'outline-width',
      'unselected-pressed-label-text-color': 'pressed-label-text-color',
      'unselected-pressed-state-layer-color': 'pressed-state-layer-color',
      'unselected-pressed-state-layer-opacity': 'pressed-state-layer-opacity',
      // Remove "with-*" prefixes (b/273534858)
      'with-avatar-avatar-shape': 'avatar-shape',
      'with-avatar-avatar-size': 'avatar-size',
      'with-avatar-disabled-avatar-opacity': 'disabled-avatar-opacity',
      'with-leading-icon-disabled-leading-icon-color':
        'disabled-leading-icon-color',
      'with-leading-icon-disabled-leading-icon-opacity':
        'disabled-leading-icon-opacity',
      // Rename "leading-icon-size" to "icon-size" (b/275577569)
      'with-leading-icon-leading-icon-size': 'icon-size',
      'with-leading-icon-selected-dragged-leading-icon-color':
        'selected-dragged-leading-icon-color',
      'with-leading-icon-selected-focus-leading-icon-color':
        'selected-focus-leading-icon-color',
      'with-leading-icon-selected-hover-leading-icon-color':
        'selected-hover-leading-icon-color',
      'with-leading-icon-selected-leading-icon-color':
        'selected-leading-icon-color',
      'with-leading-icon-selected-pressed-leading-icon-color':
        'selected-pressed-leading-icon-color',
      'with-leading-icon-unselected-dragged-leading-icon-color':
        'dragged-leading-icon-color',
      'with-leading-icon-unselected-focus-leading-icon-color':
        'focus-leading-icon-color',
      'with-leading-icon-unselected-hover-leading-icon-color':
        'hover-leading-icon-color',
      'with-leading-icon-unselected-leading-icon-color': 'leading-icon-color',
      'with-leading-icon-unselected-pressed-leading-icon-color':
        'pressed-leading-icon-color',
      'with-trailing-icon-disabled-trailing-icon-color':
        'disabled-trailing-icon-color',
      'with-trailing-icon-disabled-trailing-icon-opacity':
        'disabled-trailing-icon-opacity',
      'with-trailing-icon-selected-dragged-trailing-icon-color':
        'selected-dragged-trailing-icon-color',
      'with-trailing-icon-selected-focus-trailing-icon-color':
        'selected-focus-trailing-icon-color',
      'with-trailing-icon-selected-hover-trailing-icon-color':
        'selected-hover-trailing-icon-color',
      'with-trailing-icon-selected-pressed-trailing-icon-color':
        'selected-pressed-trailing-icon-color',
      'with-trailing-icon-selected-trailing-icon-color':
        'selected-trailing-icon-color',
      'with-trailing-icon-trailing-icon-size': 'trailing-icon-size',
      'with-trailing-icon-unselected-dragged-trailing-icon-color':
        'dragged-trailing-icon-color',
      'with-trailing-icon-unselected-focus-trailing-icon-color':
        'focus-trailing-icon-color',
      'with-trailing-icon-unselected-hover-trailing-icon-color':
        'hover-trailing-icon-color',
      'with-trailing-icon-unselected-pressed-trailing-icon-color':
        'pressed-trailing-icon-color',
      'with-trailing-icon-unselected-trailing-icon-color': 'trailing-icon-color'
    )
  );

  // TODO(b/288199264): remove once input chip leading icon color is fixed
  $tokens: map.set(
    $tokens,
    'leading-icon-color',
    map.get($deps, 'md-sys-color', 'primary')
  );

  @if not $exclude-custom-properties {
    @each $token, $value in $tokens {
      @if string.index($token, 'container-shape-') == 1 {
        // Add fallback to shorthand for logical shape properties.
        $value: var(--md-input-chip-container-shape, #{$value});
      }

      $tokens: map.set(
        $tokens,
        $token,
        var(--md-input-chip-#{$token}, #{$value})
      );
    }
  }

  @return $tokens;
}
