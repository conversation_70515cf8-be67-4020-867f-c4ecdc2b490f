{"version": 3, "file": "icon-button.js", "sourceRoot": "", "sources": ["icon-button.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,8BAA8B,CAAC;AACtC,OAAO,wBAAwB,CAAC;AAEhC,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAC,MAAM,KAAK,CAAC;AACxD,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAC,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AACrD,OAAO,EAAC,OAAO,EAAE,IAAI,IAAI,UAAU,EAAC,MAAM,oBAAoB,CAAC;AAG/D,OAAO,EAAC,kBAAkB,EAAC,MAAM,iCAAiC,CAAC;AACnE,OAAO,EAEL,kBAAkB,GAEnB,MAAM,6CAA6C,CAAC;AACrD,OAAO,EAAC,KAAK,EAAC,MAAM,qCAAqC,CAAC;AAC1D,OAAO,EACL,SAAS,EACT,qBAAqB,GACtB,MAAM,2CAA2C,CAAC;AAInD,wCAAwC;AACxC,MAAM,mBAAmB,GAAG,kBAAkB,CAC5C,qBAAqB,CAAC,UAAU,CAAC,CAClC,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,OAAO,UAAW,SAAQ,mBAAmB;IAkFjD,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IACzC,CAAC;IACD,IAAI,IAAI,CAAC,IAAY;QACnB,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;IAChC,CAAC;IAID;QACE,KAAK,EAAE,CAAC;QA5FV;;WAEG;QACuC,aAAQ,GAAG,KAAK,CAAC;QAE3D;;;;;;WAMG;QAEH,iBAAY,GAAG,KAAK,CAAC;QAErB;;WAEG;QAEH,kBAAa,GAAG,KAAK,CAAC;QAEtB;;WAEG;QACS,SAAI,GAAG,EAAE,CAAC;QAEtB;;;;WAIG;QACS,aAAQ,GAAG,EAAE,CAAC;QAE1B;;WAEG;QACS,WAAM,GAAoB,EAAE,CAAC;QAEzC;;WAEG;QAC2C,sBAAiB,GAAG,EAAE,CAAC;QAErE;;;WAGG;QACwB,WAAM,GAAG,KAAK,CAAC;QAE1C;;;;WAIG;QACuC,aAAQ,GAAG,KAAK,CAAC;QAE3D;;;WAGG;QACS,SAAI,GAAsB,QAAQ,CAAC;QAE/C;;;WAGG;QACwB,UAAK,GAAG,EAAE,CAAC;QAuBrB,aAAQ,GAAG,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAI1D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEkB,UAAU;QAC3B,oDAAoD;QACpD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC5B,CAAC;IACH,CAAC;IAEkB,MAAM;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAA,KAAK,CAAC,CAAC,CAAC,OAAO,CAAA,QAAQ,CAAC;QACvD,iCAAiC;QACjC,MAAM,EAAC,SAAS,EAAE,YAAY,EAAE,YAAY,EAAC,GAAG,IAAuB,CAAC;QACxE,MAAM,mBAAmB,GAAG,SAAS,IAAI,IAAI,CAAC,iBAAiB,CAAC;QAChE,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;QAChE,IAAI,cAAc,GAAmC,OAAO,CAAC;QAC7D,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,cAAc;gBACZ,mBAAmB,IAAI,IAAI,CAAC,QAAQ;oBAClC,CAAC,CAAC,IAAI,CAAC,iBAAiB;oBACxB,CAAC,CAAC,SAAS,CAAC;QAClB,CAAC;QACD,OAAO,UAAU,CAAA,IAAI,GAAG;6BACC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;;sBAExC,cAAc,IAAI,OAAO;yBACtB,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,YAAY,CAAC,IAAI,OAAO;yBACvC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,YAAY,CAAC,IAAI,OAAO;wBACxC,gBAAgB;wBAChB,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,OAAO;qBAC/C,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ;kBAC9B,IAAI,CAAC,kBAAkB;UAC/B,IAAI,CAAC,eAAe,EAAE;UACtB,IAAI,CAAC,YAAY,EAAE;UACnB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,OAAO;UAC5C,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,OAAO;UACnD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,EAAE;MAC5D,GAAG,GAAG,CAAC;IACX,CAAC;IAEO,UAAU;QAChB,iCAAiC;QACjC,MAAM,EAAC,SAAS,EAAC,GAAG,IAAuB,CAAC;QAC5C,OAAO,IAAI,CAAA;;;;gBAIC,IAAI,CAAC,IAAI;oBACL,IAAI,CAAC,QAAQ,IAAI,OAAO;kBAC1B,IAAI,CAAC,MAAM,IAAI,OAAO;sBAClB,SAAS,IAAI,OAAO;UAChC,IAAI,CAAC,iBAAiB,EAAE;;KAE7B,CAAC;IACJ,CAAC;IAES,gBAAgB;QACxB,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,QAAQ;YAC1B,UAAU,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ;SACzC,CAAC;IACJ,CAAC;IAEO,UAAU;QAChB,OAAO,IAAI,CAAA,yCAAyC,CAAC;IACvD,CAAC;IAEO,kBAAkB;QACxB,wEAAwE;QACxE,OAAO,IAAI,CAAA;;aAEF,CAAC;IACZ,CAAC;IAEO,iBAAiB;QACvB,OAAO,IAAI,CAAA,6BAA6B,CAAC;IAC3C,CAAC;IAEO,eAAe;QACrB,uDAAuD;QACvD,OAAO,IAAI,CAAA;;YAEH,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,mBAAmB,CAAC;IAC3D,CAAC;IAEO,YAAY;QAClB,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5E,uDAAuD;QACvD,OAAO,IAAI,CAAA;YACH,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;mBACrB,gBAAgB,gBAAgB,CAAC;IAClD,CAAC;IAEQ,iBAAiB;QACxB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAChD,KAAK,CAAC,iBAAiB,EAAE,CAAC;IAC5B,CAAC;IAED,uCAAuC;IAC/B,WAAW,CAAC,KAAiB;QACnC,yEAAyE;QACzE,yEAAyE;QACzE,kBAAkB;QAClB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpC,KAAK,CAAC,wBAAwB,EAAE,CAAC;YACjC,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;IACH,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,kBAAkB,CAAC,KAAY;QAC3C,+BAA+B;QAC/B,MAAM,CAAC,CAAC;QACR,IACE,CAAC,IAAI,CAAC,MAAM;YACZ,IAAI,CAAC,QAAQ;YACb,IAAI,CAAC,YAAY;YACjB,KAAK,CAAC,gBAAgB,EACtB,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAI,CAAC,aAAa,CAChB,IAAI,UAAU,CAAC,OAAO,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CACzD,CAAC;QACF,0EAA0E;QAC1E,0DAA0D;QAC1D,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;IAC3D,CAAC;;AAnPD;IACE,kBAAkB,CAAC,UAAU,CAAC,CAAC;AACjC,CAAC,GAAA,CAAA;AAED,kBAAkB;AACF,yBAAc,GAAG,IAAI,AAAP,CAAQ;AAEtC,kBAAkB;AACF,4BAAiB,GAAmB;IAClD,IAAI,EAAE,MAAM;IACZ,cAAc,EAAE,IAAI;CACrB,AAHgC,CAG/B;AAKwC;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;4CAAkB;AAU3D;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;gDAChD;AAMrB;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAC,CAAC;iDACnC;AAKV;IAAX,QAAQ,EAAE;wCAAW;AAOV;IAAX,QAAQ,EAAE;4CAAe;AAKd;IAAX,QAAQ,EAAE;0CAA8B;AAKK;IAA7C,QAAQ,CAAC,EAAC,SAAS,EAAE,qBAAqB,EAAC,CAAC;qDAAwB;AAM1C;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;0CAAgB;AAOA;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;4CAAkB;AAM/C;IAAX,QAAQ,EAAE;wCAAoC;AAMpB;IAA1B,QAAQ,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;yCAAY;AAuBrB;IAAhB,KAAK,EAAE;4CAAoD", "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, isServer, LitElement, nothing} from 'lit';\nimport {property, state} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\nimport {literal, html as staticHtml} from 'lit/static-html.js';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\nimport {\n  FormSubmitter,\n  setupFormSubmitter,\n  type FormSubmitterType,\n} from '../../internal/controller/form-submitter.js';\nimport {isRtl} from '../../internal/controller/is-rtl.js';\nimport {\n  internals,\n  mixinElementInternals,\n} from '../../labs/behaviors/element-internals.js';\n\ntype LinkTarget = '_blank' | '_parent' | '_self' | '_top';\n\n// Separate variable needed for closure.\nconst iconButtonBaseClass = mixinDelegatesAria(\n  mixinElementInternals(LitElement),\n);\n\n/**\n * A button for rendering icons.\n *\n * @fires input {InputEvent} Dispatched when a toggle button toggles --bubbles\n * --composed\n * @fires change {Event} Dispatched when a toggle button toggles --bubbles\n */\nexport class IconButton extends iconButtonBaseClass implements FormSubmitter {\n  static {\n    setupFormSubmitter(IconButton);\n  }\n\n  /** @nocollapse */\n  static readonly formAssociated = true;\n\n  /** @nocollapse */\n  static override shadowRootOptions: ShadowRootInit = {\n    mode: 'open',\n    delegatesFocus: true,\n  };\n\n  /**\n   * Disables the icon button and makes it non-interactive.\n   */\n  @property({type: Boolean, reflect: true}) disabled = false;\n\n  /**\n   * \"Soft-disables\" the icon button (disabled but still focusable).\n   *\n   * Use this when an icon button needs increased visibility when disabled. See\n   * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#kbd_disabled_controls\n   * for more guidance on when this is needed.\n   */\n  @property({type: Boolean, attribute: 'soft-disabled', reflect: true})\n  softDisabled = false;\n\n  /**\n   * Flips the icon if it is in an RTL context at startup.\n   */\n  @property({type: Boolean, attribute: 'flip-icon-in-rtl'})\n  flipIconInRtl = false;\n\n  /**\n   * Sets the underlying `HTMLAnchorElement`'s `href` resource attribute.\n   */\n  @property() href = '';\n\n  /**\n   * The filename to use when downloading the linked resource.\n   * If not specified, the browser will determine a filename.\n   * This is only applicable when the icon button is used as a link (`href` is set).\n   */\n  @property() download = '';\n\n  /**\n   * Sets the underlying `HTMLAnchorElement`'s `target` attribute.\n   */\n  @property() target: LinkTarget | '' = '';\n\n  /**\n   * The `aria-label` of the button when the button is toggleable and selected.\n   */\n  @property({attribute: 'aria-label-selected'}) ariaLabelSelected = '';\n\n  /**\n   * When true, the button will toggle between selected and unselected\n   * states\n   */\n  @property({type: Boolean}) toggle = false;\n\n  /**\n   * Sets the selected state. When false, displays the default icon. When true,\n   * displays the selected icon, or the default icon If no `slot=\"selected\"`\n   * icon is provided.\n   */\n  @property({type: Boolean, reflect: true}) selected = false;\n\n  /**\n   * The default behavior of the button. May be \"button\", \"reset\", or \"submit\"\n   * (default).\n   */\n  @property() type: FormSubmitterType = 'submit';\n\n  /**\n   * The value added to a form with the button's name when the button submits a\n   * form.\n   */\n  @property({reflect: true}) value = '';\n\n  get name() {\n    return this.getAttribute('name') ?? '';\n  }\n  set name(name: string) {\n    this.setAttribute('name', name);\n  }\n\n  /**\n   * The associated form element with which this element's value will submit.\n   */\n  get form() {\n    return this[internals].form;\n  }\n\n  /**\n   * The labels this element is associated with.\n   */\n  get labels() {\n    return this[internals].labels;\n  }\n\n  @state() private flipIcon = isRtl(this, this.flipIconInRtl);\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.addEventListener('click', this.handleClick.bind(this));\n    }\n  }\n\n  protected override willUpdate() {\n    // Link buttons cannot be disabled or soft-disabled.\n    if (this.href) {\n      this.disabled = false;\n      this.softDisabled = false;\n    }\n  }\n\n  protected override render() {\n    const tag = this.href ? literal`div` : literal`button`;\n    // Needed for closure conformance\n    const {ariaLabel, ariaHasPopup, ariaExpanded} = this as ARIAMixinStrict;\n    const hasToggledAriaLabel = ariaLabel && this.ariaLabelSelected;\n    const ariaPressedValue = !this.toggle ? nothing : this.selected;\n    let ariaLabelValue: string | null | typeof nothing = nothing;\n    if (!this.href) {\n      ariaLabelValue =\n        hasToggledAriaLabel && this.selected\n          ? this.ariaLabelSelected\n          : ariaLabel;\n    }\n    return staticHtml`<${tag}\n        class=\"icon-button ${classMap(this.getRenderClasses())}\"\n        id=\"button\"\n        aria-label=\"${ariaLabelValue || nothing}\"\n        aria-haspopup=\"${(!this.href && ariaHasPopup) || nothing}\"\n        aria-expanded=\"${(!this.href && ariaExpanded) || nothing}\"\n        aria-pressed=\"${ariaPressedValue}\"\n        aria-disabled=${(!this.href && this.softDisabled) || nothing}\n        ?disabled=\"${!this.href && this.disabled}\"\n        @click=\"${this.handleClickOnChild}\">\n        ${this.renderFocusRing()}\n        ${this.renderRipple()}\n        ${!this.selected ? this.renderIcon() : nothing}\n        ${this.selected ? this.renderSelectedIcon() : nothing}\n        ${this.href ? this.renderLink() : this.renderTouchTarget()}\n  </${tag}>`;\n  }\n\n  private renderLink() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`\n      <a\n        class=\"link\"\n        id=\"link\"\n        href=\"${this.href}\"\n        download=\"${this.download || nothing}\"\n        target=\"${this.target || nothing}\"\n        aria-label=\"${ariaLabel || nothing}\">\n        ${this.renderTouchTarget()}\n      </a>\n    `;\n  }\n\n  protected getRenderClasses() {\n    return {\n      'flip-icon': this.flipIcon,\n      'selected': this.toggle && this.selected,\n    };\n  }\n\n  private renderIcon() {\n    return html`<span class=\"icon\"><slot></slot></span>`;\n  }\n\n  private renderSelectedIcon() {\n    // Use default slot as fallback to not require specifying multiple icons\n    return html`<span class=\"icon icon--selected\"\n      ><slot name=\"selected\"><slot></slot></slot\n    ></span>`;\n  }\n\n  private renderTouchTarget() {\n    return html`<span class=\"touch\"></span>`;\n  }\n\n  private renderFocusRing() {\n    // TODO(b/310046938): use the same id for both elements\n    return html`<md-focus-ring\n      part=\"focus-ring\"\n      for=${this.href ? 'link' : 'button'}></md-focus-ring>`;\n  }\n\n  private renderRipple() {\n    const isRippleDisabled = !this.href && (this.disabled || this.softDisabled);\n    // TODO(b/310046938): use the same id for both elements\n    return html`<md-ripple\n      for=${this.href ? 'link' : nothing}\n      ?disabled=\"${isRippleDisabled}\"></md-ripple>`;\n  }\n\n  override connectedCallback() {\n    this.flipIcon = isRtl(this, this.flipIconInRtl);\n    super.connectedCallback();\n  }\n\n  /** Handles a click on this element. */\n  private handleClick(event: MouseEvent) {\n    // If the icon button is soft-disabled, we need to explicitly prevent the\n    // click from propagating to other event listeners as well as prevent the\n    // default action.\n    if (!this.href && this.softDisabled) {\n      event.stopImmediatePropagation();\n      event.preventDefault();\n      return;\n    }\n  }\n\n  /**\n   * Handles a click on the child <div> or <button> element within this\n   * element's shadow DOM.\n   */\n  private async handleClickOnChild(event: Event) {\n    // Allow the event to propagate\n    await 0;\n    if (\n      !this.toggle ||\n      this.disabled ||\n      this.softDisabled ||\n      event.defaultPrevented\n    ) {\n      return;\n    }\n\n    this.selected = !this.selected;\n    this.dispatchEvent(\n      new InputEvent('input', {bubbles: true, composed: true}),\n    );\n    // Bubbles but does not compose to mimic native browser <input> & <select>\n    // Additionally, native change event is not an InputEvent.\n    this.dispatchEvent(new Event('change', {bubbles: true}));\n  }\n}\n"]}