//
// Copyright 2023 Google LLC
// SPDX-License-Identifier: Apache-2.0
//

// go/keep-sorted start
@use 'sass:list';
@use 'sass:map';
// go/keep-sorted end
// go/keep-sorted start
@use '../../tokens';
// go/keep-sorted end

@mixin theme($tokens) {
  $supported-tokens: tokens.$md-comp-dialog-supported-tokens;
  @each $token, $value in $tokens {
    @if list.index($supported-tokens, $token) == null {
      @error 'Token `#{$token}` is not a supported token.';
    }

    @if $value {
      --md-dialog-#{$token}: #{$value};
    }
  }
}

@mixin styles() {
  $md-sys-color: tokens.md-sys-color-values-light();
  $md-sys-motion: tokens.md-sys-motion-values();
  $tokens: tokens.md-comp-dialog-values();

  :host {
    border-start-start-radius: map.get($tokens, 'container-shape-start-start');
    border-start-end-radius: map.get($tokens, 'container-shape-start-end');
    border-end-end-radius: map.get($tokens, 'container-shape-end-end');
    border-end-start-radius: map.get($tokens, 'container-shape-end-start');
    display: contents;
    margin: auto;
    max-height: min(560px, calc(100% - 48px));
    max-width: min(560px, calc(100% - 48px));
    min-height: 140px;
    min-width: 280px;
    position: fixed;
    height: fit-content;
    width: fit-content;
  }

  dialog {
    background: transparent;
    border: none;
    border-radius: inherit;
    flex-direction: column;
    height: inherit;
    margin: inherit;
    max-height: inherit;
    max-width: inherit;
    min-height: inherit;
    min-width: inherit;
    outline: none;
    overflow: visible;
    padding: 0;
    width: inherit;
  }

  dialog[open] {
    display: flex;
  }

  ::backdrop {
    // Can't use ::backdrop since Firefox does not allow animations on it.
    background: none;
  }

  .scrim {
    background: map.get($md-sys-color, 'scrim');
    display: none;
    inset: 0;
    opacity: 32%;
    pointer-events: none;
    position: fixed;
    z-index: 1;
  }

  :host([open]) .scrim {
    display: flex;
  }

  h2 {
    all: unset;
    align-self: stretch;
  }

  .headline {
    align-items: center;
    color: map.get($tokens, 'headline-color');
    display: flex;
    flex-direction: column;
    font-family: map.get($tokens, 'headline-font');
    font-size: map.get($tokens, 'headline-size');
    line-height: map.get($tokens, 'headline-line-height');
    font-weight: map.get($tokens, 'headline-weight');
    position: relative;
  }

  slot[name='headline']::slotted(*) {
    align-items: center;
    align-self: stretch;
    box-sizing: border-box;
    display: flex;
    gap: 8px;
    padding: 24px 24px 0;
  }

  .icon {
    display: flex;
  }

  slot[name='icon']::slotted(*) {
    color: map.get($tokens, 'icon-color');
    fill: currentColor;
    font-size: map.get($tokens, 'icon-size');
    margin-top: 24px;
    height: map.get($tokens, 'icon-size');
    width: map.get($tokens, 'icon-size');
  }

  .has-icon slot[name='headline']::slotted(*) {
    justify-content: center;
    padding-top: 16px;
  }

  .scrollable slot[name='headline']::slotted(*) {
    padding-bottom: 16px;
  }

  .scrollable.has-headline slot[name='content']::slotted(*) {
    padding-top: 8px;
  }

  .container {
    border-radius: inherit;
    display: flex;
    flex-direction: column;
    // Safari won't show content with "flex: 1", but container needs to grow if
    // height is set on the dialog, so use flex-grow instead.
    flex-grow: 1;
    overflow: hidden;
    position: relative;
    transform-origin: top;
  }

  .container::before {
    background: map.get($tokens, 'container-color');
    border-radius: inherit;
    content: '';
    inset: 0;
    position: absolute;
  }

  .scroller {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
    // needed to display scrollbars on Chrome linux. Also needs to be > 0 so
    // that content that is position: fixed in the content can render above the
    // actions bar. e.g. <md-select positioning="menu-fixed">
    z-index: 1;
  }

  .scrollable .scroller {
    // Only add scrollbars if the content is overflowing. This prevents extra
    // space from appearing on platforms that reserve scrollbar space.
    // Note: we only scroll vertically. Horizontal scrolling should be handled
    // by the content.
    overflow-y: scroll;
  }

  .content {
    color: map.get($tokens, 'supporting-text-color');
    font-family: map.get($tokens, 'supporting-text-font');
    font-size: map.get($tokens, 'supporting-text-size');
    line-height: map.get($tokens, 'supporting-text-line-height');
    flex: 1;
    font-weight: map.get($tokens, 'supporting-text-weight');
    height: min-content; // Needed for Safari
    position: relative;
  }

  slot[name='content']::slotted(*) {
    box-sizing: border-box;
    padding: 24px;
  }

  // Anchors are used with an IntersectionObserver to determine when the content
  // has scrolled.
  .anchor {
    position: absolute;
  }

  .top.anchor {
    top: 0;
  }

  .bottom.anchor {
    bottom: 0;
  }

  .actions {
    position: relative;
  }

  slot[name='actions']::slotted(*) {
    box-sizing: border-box;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    padding: 16px 24px 24px;
  }

  .has-actions slot[name='content']::slotted(*) {
    padding-bottom: 8px;
  }

  md-divider {
    display: none;
    position: absolute;
  }

  .has-headline.show-top-divider .headline md-divider,
  .has-actions.show-bottom-divider .actions md-divider {
    display: flex;
  }

  .headline md-divider {
    bottom: 0;
  }

  .actions md-divider {
    top: 0;
  }

  @media (forced-colors: active) {
    dialog {
      outline: 2px solid WindowText;
    }
  }
}
