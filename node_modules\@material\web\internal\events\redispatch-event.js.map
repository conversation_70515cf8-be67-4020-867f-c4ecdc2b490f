{"version": 3, "file": "redispatch-event.js", "sourceRoot": "", "sources": ["redispatch-event.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAM,UAAU,eAAe,CAAC,OAAgB,EAAE,KAAY;IAC5D,6EAA6E;IAC7E,yBAAyB;IACzB,IAAI,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC7D,KAAK,CAAC,eAAe,EAAE,CAAC;IAC1B,CAAC;IAED,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;IACvE,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC/C,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,KAAK,CAAC,cAAc,EAAE,CAAC;IACzB,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * Re-dispatches an event from the provided element.\n *\n * This function is useful for forwarding non-composed events, such as `change`\n * events.\n *\n * @example\n * class MyInput extends LitElement {\n *   render() {\n *     return html`<input @change=${this.redispatchEvent}>`;\n *   }\n *\n *   protected redispatchEvent(event: Event) {\n *     redispatchEvent(this, event);\n *   }\n * }\n *\n * @param element The element to dispatch the event from.\n * @param event The event to re-dispatch.\n * @return Whether or not the event was dispatched (if cancelable).\n */\nexport function redispatchEvent(element: Element, event: Event) {\n  // For bubbling events in SSR light DOM (or composed), stop their propagation\n  // and dispatch the copy.\n  if (event.bubbles && (!element.shadowRoot || event.composed)) {\n    event.stopPropagation();\n  }\n\n  const copy = Reflect.construct(event.constructor, [event.type, event]);\n  const dispatched = element.dispatchEvent(copy);\n  if (!dispatched) {\n    event.preventDefault();\n  }\n\n  return dispatched;\n}\n"]}