{"version": 3, "file": "select-validator.js", "sourceRoot": "", "sources": ["select-validator.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,IAAI,EAAE,MAAM,EAAC,MAAM,KAAK,CAAC;AAEjC,OAAO,EAAC,SAAS,EAAC,MAAM,gBAAgB,CAAC;AAiBzC;;;GAGG;AACH,MAAM,OAAO,eAAgB,SAAQ,SAAsB;IAGtC,eAAe,CAAC,KAAkB;QACnD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,oCAAoC;YACpC,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,CAAC,IAAI,CAAA,iBAAiB,KAAK,CAAC,KAAK,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAEzE,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QAC7C,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ;YACrC,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,iBAAiB;SACxD,CAAC;IACJ,CAAC;IAEkB,MAAM,CAAC,IAAiB,EAAE,IAAiB;QAC5D,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC;IACtE,CAAC;IAEkB,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAc;QACpD,OAAO,EAAC,KAAK,EAAE,QAAQ,EAAC,CAAC;IAC3B,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, render} from 'lit';\n\nimport {Validator} from './validator.js';\n\n/**\n * Constraint validation properties for a select dropdown.\n */\nexport interface SelectState {\n  /**\n   * The current selected value.\n   */\n  readonly value: string;\n\n  /**\n   * Whether the select is required.\n   */\n  readonly required: boolean;\n}\n\n/**\n * A validator that provides constraint validation that emulates `<select>`\n * validation.\n */\nexport class SelectValidator extends Validator<SelectState> {\n  private selectControl?: HTMLSelectElement;\n\n  protected override computeValidity(state: SelectState) {\n    if (!this.selectControl) {\n      // Lazily create the platform select\n      this.selectControl = document.createElement('select');\n    }\n\n    render(html`<option value=${state.value}></option>`, this.selectControl);\n\n    this.selectControl.value = state.value;\n    this.selectControl.required = state.required;\n    return {\n      validity: this.selectControl.validity,\n      validationMessage: this.selectControl.validationMessage,\n    };\n  }\n\n  protected override equals(prev: SelectState, next: SelectState) {\n    return prev.value === next.value && prev.required === next.required;\n  }\n\n  protected override copy({value, required}: SelectState) {\n    return {value, required};\n  }\n}\n"]}