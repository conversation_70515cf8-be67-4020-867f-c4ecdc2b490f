{"version": 3, "file": "outlined-icon-button.js", "sourceRoot": "", "sources": ["outlined-icon-button.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAGH,OAAO,EAAC,aAAa,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,EAAC,UAAU,EAAC,MAAM,2BAA2B,CAAC;AACrD,OAAO,EAAC,MAAM,EAAC,MAAM,+BAA+B,CAAC;AACrD,OAAO,EAAC,MAAM,IAAI,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAQnE;;;;;;;;;;;;;;;;;GAiBG;AAEI,IAAM,oBAAoB,GAA1B,MAAM,oBAAqB,SAAQ,UAAU;IAG/B,gBAAgB;QACjC,OAAO;YACL,GAAG,KAAK,CAAC,gBAAgB,EAAE;YAC3B,UAAU,EAAE,IAAI;SACjB,CAAC;IACJ,CAAC;;AAPe,2BAAM,GAAwB,CAAC,YAAY,EAAE,MAAM,CAAC,AAA9C,CAA+C;AAD1D,oBAAoB;IADhC,aAAa,CAAC,yBAAyB,CAAC;GAC5B,oBAAoB,CAShC", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {IconButton} from './internal/icon-button.js';\nimport {styles} from './internal/outlined-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-outlined-icon-button': MdOutlinedIconButton;\n  }\n}\n\n/**\n * @summary Icon buttons help people take supplementary actions with a single\n * tap.\n *\n * @description\n * __Emphasis:__ Low emphasis – For optional or supplementary actions with the\n * least amount of prominence.\n *\n * __Rationale:__ The most compact and unobtrusive type of button, icon buttons\n * are used for optional supplementary actions such as \"Bookmark\" or \"Star.\"\n *\n * __Example usages:__\n * - Add to Favorites\n * - Print\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-outlined-icon-button')\nexport class MdOutlinedIconButton extends IconButton {\n  static override styles: CSSResultOrNative[] = [sharedStyles, styles];\n\n  protected override getRenderClasses() {\n    return {\n      ...super.getRenderClasses(),\n      'outlined': true,\n    };\n  }\n}\n"]}