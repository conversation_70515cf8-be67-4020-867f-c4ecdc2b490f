{"version": 3, "file": "list-navigation-helpers.js", "sourceRoot": "", "sources": ["list-navigation-helpers.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAeH;;;;;;;GAOG;AACH,MAAM,UAAU,iBAAiB,CAC/B,KAAa,EACb,gBAAgB,CAAA,iBAAuB,CAAA;IAEvC,yEAAyE;IACzE,yEAAyE;IACzE,UAAU;IACV,MAAM,SAAS,GAAG,uBAAuB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;IAChE,IAAI,SAAS,EAAE,CAAC;QACd,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC;QACvB,SAAS,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,gBAAgB,CAC9B,KAAa,EACb,gBAAgB,CAAA,iBAAuB,CAAA;IAEvC,MAAM,QAAQ,GAAG,sBAAsB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;IAC9D,IAAI,QAAQ,EAAE,CAAC;QACb,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC;QACtB,QAAQ,CAAC,KAAK,EAAE,CAAC;IACnB,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,oBAAoB,CAClC,KAAa,EACb,gBAAgB,CAAA,iBAAuB,CAAA;IAEvC,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;IACvD,IAAI,UAAU,EAAE,CAAC;QACf,UAAU,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;IAChC,CAAC;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,aAAa,CAC3B,KAAa,EACb,gBAAgB,CAAA,iBAAuB,CAAA;IAEvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO;gBACL,IAAI;gBACJ,KAAK,EAAE,CAAC;aACW,CAAC;QACxB,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,uBAAuB,CACrC,KAAa,EACb,gBAAgB,CAAA,iBAAuB,CAAA;IAEvC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,sBAAsB,CACpC,KAAa,EACb,gBAAgB,CAAA,iBAAuB,CAAA;IAEvC,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3C,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,WAAW,CACzB,KAAa,EACb,KAAa,EACb,gBAAgB,CAAA,iBAAuB,CAAA,EACvC,IAAI,GAAG,IAAI;IAEX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QAC7C,IAAI,SAAS,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;YAC/B,oEAAoE;YACpE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;QAC9B,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAC5C,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,WAAW,CACzB,KAAa,EACb,KAAa,EACb,gBAAgB,CAAA,iBAAuB,CAAA,EACvC,IAAI,GAAG,IAAI;IAEX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,MAAM,SAAS,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5D,IAAI,SAAS,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;YAC/B,8DAA8D;YAC9D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;QAE9B,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAC5C,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAC9B,KAAa,EACb,gBAAyC,EACzC,gBAAgB,CAAA,iBAAuB,CAAA,EACvC,IAAI,GAAG,IAAI;IAEX,IAAI,gBAAgB,EAAE,CAAC;QACrB,MAAM,IAAI,GAAG,WAAW,CACtB,KAAK,EACL,gBAAgB,CAAC,KAAK,EACtB,aAAa,EACb,IAAI,CACL,CAAC;QAEF,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;YAClB,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;SAAM,CAAC;QACN,OAAO,iBAAiB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;IACjD,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,oBAAoB,CAClC,KAAa,EACb,gBAAyC,EACzC,gBAAgB,CAAA,iBAAuB,CAAA,EACvC,IAAI,GAAG,IAAI;IAEX,IAAI,gBAAgB,EAAE,CAAC;QACrB,MAAM,IAAI,GAAG,WAAW,CACtB,KAAK,EACL,gBAAgB,CAAC,KAAK,EACtB,aAAa,EACb,IAAI,CACL,CAAC;QACF,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;YAClB,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;SAAM,CAAC;QACN,OAAO,gBAAgB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;IAChD,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,0BAA0B;IACxC,OAAO,IAAI,KAAK,CAAC,kBAAkB,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AACxE,CAAC;AAUD;;;;;;;GAOG;AACH,MAAM,UAAU,4BAA4B;IAC1C,OAAO,IAAI,KAAK,CAAC,oBAAoB,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAC1E,CAAC;AASD;;;;;;GAMG;AACH,SAAS,iBAAiB,CAAwB,IAAU;IAC1D,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;AACxB,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nexport interface ListItem extends HTMLElement {\n  disabled: boolean;\n}\n\n/**\n * A record that describes a list item in a list with metadata such a reference\n * to the item and its index in the list.\n */\nexport interface ItemRecord<Item extends ListItem> {\n  item: Item;\n  index: number;\n}\n\n/**\n * Activates the first non-disabled item of a given array of items.\n *\n * @param items {Array<ListItem>} The items from which to activate the\n *     first item.\n * @param isActivatable Function to determine if an item can be  activated.\n *     Defaults to non-disabled items.\n */\nexport function activateFirstItem<Item extends ListItem>(\n  items: Item[],\n  isActivatable = isItemNotDisabled<Item>,\n) {\n  // NOTE: These selector functions are static and not on the instance such\n  // that multiple operations can be chained and we do not have to re-query\n  // the DOM\n  const firstItem = getFirstActivatableItem(items, isActivatable);\n  if (firstItem) {\n    firstItem.tabIndex = 0;\n    firstItem.focus();\n  }\n  return firstItem;\n}\n\n/**\n * Activates the last non-disabled item of a given array of items.\n *\n * @param items {Array<ListItem>} The items from which to activate the\n *     last item.\n * @param isActivatable Function to determine if an item can be  activated.\n *     Defaults to non-disabled items.\n * @nocollapse\n */\nexport function activateLastItem<Item extends ListItem>(\n  items: Item[],\n  isActivatable = isItemNotDisabled<Item>,\n) {\n  const lastItem = getLastActivatableItem(items, isActivatable);\n  if (lastItem) {\n    lastItem.tabIndex = 0;\n    lastItem.focus();\n  }\n  return lastItem;\n}\n\n/**\n * Deactivates the currently active item of a given array of items.\n *\n * @param items {Array<ListItem>} The items from which to deactivate the\n *     active item.\n * @param isActivatable Function to determine if an item can be  activated.\n *     Defaults to non-disabled items.\n * @return A record of the deleselcted activated item including the item and\n *     the index of the item or `null` if none are deactivated.\n * @nocollapse\n */\nexport function deactivateActiveItem<Item extends ListItem>(\n  items: Item[],\n  isActivatable = isItemNotDisabled<Item>,\n) {\n  const activeItem = getActiveItem(items, isActivatable);\n  if (activeItem) {\n    activeItem.item.tabIndex = -1;\n  }\n  return activeItem;\n}\n\n/**\n * Retrieves the first activated item of a given array of items.\n *\n * @param items {Array<ListItem>} The items to search.\n * @param isActivatable Function to determine if an item can be  activated.\n *     Defaults to non-disabled items.\n * @return A record of the first activated item including the item and the\n *     index of the item or `null` if none are activated.\n * @nocollapse\n */\nexport function getActiveItem<Item extends ListItem>(\n  items: Item[],\n  isActivatable = isItemNotDisabled<Item>,\n) {\n  for (let i = 0; i < items.length; i++) {\n    const item = items[i];\n    if (item.tabIndex === 0 && isActivatable(item)) {\n      return {\n        item,\n        index: i,\n      } as ItemRecord<Item>;\n    }\n  }\n  return null;\n}\n\n/**\n * Retrieves the first non-disabled item of a given array of items. This\n * the first item that is not disabled.\n *\n * @param items {Array<ListItem>} The items to search.\n * @param isActivatable Function to determine if an item can be  activated.\n *     Defaults to non-disabled items.\n * @return The first activatable item or `null` if none are activatable.\n * @nocollapse\n */\nexport function getFirstActivatableItem<Item extends ListItem>(\n  items: Item[],\n  isActivatable = isItemNotDisabled<Item>,\n) {\n  for (const item of items) {\n    if (isActivatable(item)) {\n      return item;\n    }\n  }\n\n  return null;\n}\n\n/**\n * Retrieves the last non-disabled item of a given array of items.\n *\n * @param items {Array<ListItem>} The items to search.\n * @param isActivatable Function to determine if an item can be  activated.\n *     Defaults to non-disabled items.\n * @return The last activatable item or `null` if none are activatable.\n * @nocollapse\n */\nexport function getLastActivatableItem<Item extends ListItem>(\n  items: Item[],\n  isActivatable = isItemNotDisabled<Item>,\n) {\n  for (let i = items.length - 1; i >= 0; i--) {\n    const item = items[i];\n    if (isActivatable(item)) {\n      return item;\n    }\n  }\n\n  return null;\n}\n\n/**\n * Retrieves the next non-disabled item of a given array of items.\n *\n * @param items {Array<ListItem>} The items to search.\n * @param index {{index: number}} The index to search from.\n * @param isActivatable Function to determine if an item can be  activated.\n *     Defaults to non-disabled items.\n * @param wrap If true, then the next item at the end of the list is the first\n *     item. Defaults to true.\n * @return The next activatable item or `null` if none are activatable.\n */\nexport function getNextItem<Item extends ListItem>(\n  items: Item[],\n  index: number,\n  isActivatable = isItemNotDisabled<Item>,\n  wrap = true,\n) {\n  for (let i = 1; i < items.length; i++) {\n    const nextIndex = (i + index) % items.length;\n    if (nextIndex < index && !wrap) {\n      // Return if the index loops back to the beginning and not wrapping.\n      return null;\n    }\n\n    const item = items[nextIndex];\n    if (isActivatable(item)) {\n      return item;\n    }\n  }\n\n  return items[index] ? items[index] : null;\n}\n\n/**\n * Retrieves the previous non-disabled item of a given array of items.\n *\n * @param items {Array<ListItem>} The items to search.\n * @param index {{index: number}} The index to search from.\n * @param isActivatable Function to determine if an item can be  activated.\n *     Defaults to non-disabled items.\n * @param wrap If true, then the previous item at the beginning of the list is\n *     the last item. Defaults to true.\n * @return The previous activatable item or `null` if none are activatable.\n */\nexport function getPrevItem<Item extends ListItem>(\n  items: Item[],\n  index: number,\n  isActivatable = isItemNotDisabled<Item>,\n  wrap = true,\n) {\n  for (let i = 1; i < items.length; i++) {\n    const prevIndex = (index - i + items.length) % items.length;\n    if (prevIndex > index && !wrap) {\n      // Return if the index loops back to the end and not wrapping.\n      return null;\n    }\n\n    const item = items[prevIndex];\n\n    if (isActivatable(item)) {\n      return item;\n    }\n  }\n\n  return items[index] ? items[index] : null;\n}\n\n/**\n * Activates the next item and focuses it. If nothing is currently activated,\n * activates the first item.\n */\nexport function activateNextItem<Item extends ListItem>(\n  items: Item[],\n  activeItemRecord: null | ItemRecord<Item>,\n  isActivatable = isItemNotDisabled<Item>,\n  wrap = true,\n): Item | null {\n  if (activeItemRecord) {\n    const next = getNextItem(\n      items,\n      activeItemRecord.index,\n      isActivatable,\n      wrap,\n    );\n\n    if (next) {\n      next.tabIndex = 0;\n      next.focus();\n    }\n\n    return next;\n  } else {\n    return activateFirstItem(items, isActivatable);\n  }\n}\n\n/**\n * Activates the previous item and focuses it. If nothing is currently\n * activated, activates the last item.\n */\nexport function activatePreviousItem<Item extends ListItem>(\n  items: Item[],\n  activeItemRecord: null | ItemRecord<Item>,\n  isActivatable = isItemNotDisabled<Item>,\n  wrap = true,\n): Item | null {\n  if (activeItemRecord) {\n    const prev = getPrevItem(\n      items,\n      activeItemRecord.index,\n      isActivatable,\n      wrap,\n    );\n    if (prev) {\n      prev.tabIndex = 0;\n      prev.focus();\n    }\n    return prev;\n  } else {\n    return activateLastItem(items, isActivatable);\n  }\n}\n\n/**\n * Creates an event that requests the parent md-list to deactivate all other\n * items.\n */\nexport function createDeactivateItemsEvent() {\n  return new Event('deactivate-items', {bubbles: true, composed: true});\n}\n\n/**\n * The type of the event that requests the parent md-list to deactivate all\n * other items.\n */\nexport type DeactivateItemsEvent = ReturnType<\n  typeof createDeactivateItemsEvent\n>;\n\n/**\n * Creates an event that requests the menu to set `tabindex=0` on the item and\n * focus it. We use this pattern because List keeps track of what element is\n * active in the List by maintaining tabindex. We do not want list items\n * to set tabindex on themselves or focus themselves so that we can organize all\n * that logic in the parent List and Menus, and list item stays as dumb as\n * possible.\n */\nexport function createRequestActivationEvent() {\n  return new Event('request-activation', {bubbles: true, composed: true});\n}\n\n/**\n * The type of the event that requests the list activates and focuses the item.\n */\nexport type RequestActivationEvent = ReturnType<\n  typeof createRequestActivationEvent\n>;\n\n/**\n * The default `isActivatable` function, which checks if an item is not\n * disabled.\n *\n * @param item The item to check.\n * @return true if `item.disabled` is `false.\n */\nfunction isItemNotDisabled<Item extends ListItem>(item: Item) {\n  return !item.disabled;\n}\n"]}