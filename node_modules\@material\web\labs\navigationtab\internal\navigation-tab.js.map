{"version": 3, "file": "navigation-tab.js", "sourceRoot": "", "sources": ["navigation-tab.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,iCAAiC,CAAC;AACzC,OAAO,2BAA2B,CAAC;AACnC,OAAO,sBAAsB,CAAC;AAE9B,OAAO,EAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAiB,MAAM,KAAK,CAAC;AAC9D,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAC,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAC,QAAQ,EAAC,MAAM,6BAA6B,CAAC;AAGrD,OAAO,EAAC,kBAAkB,EAAC,MAAM,oCAAoC,CAAC;AAItE,wCAAwC;AACxC,MAAM,sBAAsB,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAE9D;;;;;;;;GAQG;AACH,MAAM,OAAO,aACX,SAAQ,sBAAsB;IADhC;;QAI6B,aAAQ,GAAG,KAAK,CAAC;QACF,WAAM,GAAG,KAAK,CAAC;QAEzD,sBAAiB,GAAG,KAAK,CAAC;QAEY,eAAU,GAAG,EAAE,CAAC;QACF,cAAS,GAAG,KAAK,CAAC;IAyFxE,CAAC;IArFoB,MAAM;QACvB,iCAAiC;QACjC,MAAM,EAAC,SAAS,EAAC,GAAG,IAAuB,CAAC;QAC5C,OAAO,IAAI,CAAA;kCACmB,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;;uBAE5C,IAAI,CAAC,MAAM;mBACf,SAAS,IAAI,OAAO;kBACrB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,IAAI,CAAC,WAAW;;;qBAGX,IAAI,CAAC,QAAQ;;;;;;;;;WASvB,IAAI,CAAC,WAAW,EAAE;SACpB,IAAI,CAAC,WAAW,EAAE;cACb,CAAC;IACb,CAAC;IAEO,gBAAgB;QACtB,OAAO;YACL,yCAAyC,EAAE,IAAI,CAAC,iBAAiB;YACjE,4BAA4B,EAAE,IAAI,CAAC,MAAM;SAC1C,CAAC;IACJ,CAAC;IAEO,WAAW;QACjB,OAAO,IAAI,CAAC,SAAS;YACnB,CAAC,CAAC,IAAI,CAAA,qBAAqB,IAAI,CAAC,UAAU,eAAe;YACzD,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IAEO,WAAW;QACjB,iCAAiC;QACjC,MAAM,EAAC,SAAS,EAAC,GAAG,IAAuB,CAAC;QAC5C,MAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;QAChD,OAAO,CAAC,IAAI,CAAC,KAAK;YAChB,CAAC,CAAC,OAAO;YACT,CAAC,CAAC,IAAI,CAAA;yBACa,UAAU;;aAEtB,IAAI,CAAC,KAAK;UACb,CAAC;IACT,CAAC;IAEQ,YAAY,CAAC,iBAAiC;QACrD,KAAK,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QACtC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,EAAE;YACjD,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAEQ,KAAK;QACZ,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,IAAI,aAAa,EAAE,CAAC;YAClB,aAAa,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IAEQ,IAAI;QACX,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,IAAI,aAAa,EAAE,CAAC;YAClB,aAAa,CAAC,IAAI,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED,WAAW;QACT,kCAAkC;QAClC,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,4BAA4B,EAAE;YAC5C,MAAM,EAAE,EAAC,KAAK,EAAE,IAAI,EAAC;YACrB,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;SACf,CAAC,CACH,CAAC;IACJ,CAAC;CACF;AA/F4B;IAA1B,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC;+CAAkB;AACF;IAAzC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;6CAAgB;AAEzD;IADC,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,qBAAqB,EAAC,CAAC;wDAClC;AACd;IAAX,QAAQ,EAAE;4CAAgB;AACW;IAArC,QAAQ,CAAC,EAAC,SAAS,EAAE,aAAa,EAAC,CAAC;iDAAiB;AACF;IAAnD,QAAQ,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAC,CAAC;gDAAmB;AAErD;IAAhB,KAAK,CAAC,QAAQ,CAAC;oDAAoC", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../../focus/md-focus-ring.js';\nimport '../../../ripple/ripple.js';\nimport '../../badge/badge.js';\n\nimport {html, LitElement, nothing, PropertyValues} from 'lit';\nimport {property, query} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\n\nimport {NavigationTabState} from './state.js';\n\n// Separate variable needed for closure.\nconst navigationTabBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * b/265346501 - add docs\n *\n * @fires navigation-tab-rendered {Event} Dispatched when the navigation tab's\n * DOM has rendered and custom element definition has loaded. --bubbles\n * --composed\n * @fires navigation-tab-interaction {CustomEvent<{state: MdNavigationTab}>}\n * Dispatched when the navigation tab has been clicked. --bubbles --composed\n */\nexport class NavigationTab\n  extends navigationTabBaseClass\n  implements NavigationTabState\n{\n  @property({type: Boolean}) disabled = false;\n  @property({type: Boolean, reflect: true}) active = false;\n  @property({type: Boolean, attribute: 'hide-inactive-label'})\n  hideInactiveLabel = false;\n  @property() label?: string;\n  @property({attribute: 'badge-value'}) badgeValue = '';\n  @property({type: Boolean, attribute: 'show-badge'}) showBadge = false;\n\n  @query('button') buttonElement!: HTMLElement | null;\n\n  protected override render() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html` <button\n      class=\"md3-navigation-tab ${classMap(this.getRenderClasses())}\"\n      role=\"tab\"\n      aria-selected=\"${this.active}\"\n      aria-label=${ariaLabel || nothing}\n      tabindex=\"${this.active ? 0 : -1}\"\n      @click=\"${this.handleClick}\">\n      <md-focus-ring part=\"focus-ring\" inward></md-focus-ring>\n      <md-ripple\n        ?disabled=\"${this.disabled}\"\n        class=\"md3-navigation-tab__ripple\"></md-ripple>\n      <span aria-hidden=\"true\" class=\"md3-navigation-tab__icon-content\"\n        ><span class=\"md3-navigation-tab__active-indicator\"></span\n        ><span class=\"md3-navigation-tab__icon\"\n          ><slot name=\"inactive-icon\"></slot\n        ></span>\n        <span class=\"md3-navigation-tab__icon md3-navigation-tab__icon--active\"\n          ><slot name=\"active-icon\"></slot></span\n        >${this.renderBadge()}</span\n      >${this.renderLabel()}\n    </button>`;\n  }\n\n  private getRenderClasses() {\n    return {\n      'md3-navigation-tab--hide-inactive-label': this.hideInactiveLabel,\n      'md3-navigation-tab--active': this.active,\n    };\n  }\n\n  private renderBadge() {\n    return this.showBadge\n      ? html`<md-badge .value=\"${this.badgeValue}\"></md-badge>`\n      : nothing;\n  }\n\n  private renderLabel() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    const ariaHidden = ariaLabel ? 'true' : 'false';\n    return !this.label\n      ? nothing\n      : html` <span\n          aria-hidden=\"${ariaHidden}\"\n          class=\"md3-navigation-tab__label-text\"\n          >${this.label}</span\n        >`;\n  }\n\n  override firstUpdated(changedProperties: PropertyValues) {\n    super.firstUpdated(changedProperties);\n    const event = new Event('navigation-tab-rendered', {\n      bubbles: true,\n      composed: true,\n    });\n    this.dispatchEvent(event);\n  }\n\n  override focus() {\n    const buttonElement = this.buttonElement;\n    if (buttonElement) {\n      buttonElement.focus();\n    }\n  }\n\n  override blur() {\n    const buttonElement = this.buttonElement;\n    if (buttonElement) {\n      buttonElement.blur();\n    }\n  }\n\n  handleClick() {\n    // b/269772145 - connect to ripple\n    this.dispatchEvent(\n      new CustomEvent('navigation-tab-interaction', {\n        detail: {state: this},\n        bubbles: true,\n        composed: true,\n      }),\n    );\n  }\n}\n"]}